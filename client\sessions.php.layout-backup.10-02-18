<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once 'includes/auth.php';
require_once '../includes/shift_helpers.php';
require_once '../includes/billing_helper.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// التحقق من صلاحيات العملاء
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء - التحقق من صلاحية الوصول لصفحة الجلسات
    if (!hasPagePermission('sessions')) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى صفحة الجلسات';
        header('Location: dashboard.php');
        exit;
    }
}

// التحقق من صلاحيات الموظفين - التحقق من الوصول للصفحة والصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('sessions')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_sessions') && !employeeHasPermission('view_sessions')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// متغير لتتبع حالة حضور الموظف
$employee_attendance_status = null;
$can_create_sessions = true;
$attendance_message = '';

// التحقق من حضور الموظف إذا كان موظف وليس مالك
if (isset($_SESSION['employee_id'])) {
    $attendance_check = canEmployeeCreateSessions($pdo, $_SESSION['employee_id']);
    $can_create_sessions = $attendance_check['can_create'];
    $attendance_message = $attendance_check['message'];
    $employee_attendance_status = $attendance_check['attendance_status'] ?? null;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id']; // من جلسة الموظف
} else {
    $client_id = $_SESSION['client_id']; // من جلسة العميل
}

$page_title = "إدارة الجلسات";
$active_page = "sessions";

// بدء جلسة جديدة من Modal أو من رابط مباشر
if (isset($_POST['start_session']) || (isset($_GET['start']) && is_numeric($_GET['start']))) {
    try {
        // التحقق من إمكانية الموظف إنشاء جلسات (إذا كان موظف)
        if (isset($_SESSION['employee_id']) && !$can_create_sessions) {
            $_SESSION['error'] = $attendance_message;
            header('Location: sessions.php');
            exit;
        }

        // تحديد معرف الجهاز من POST أو GET
        $device_id = isset($_POST['device_id']) ? $_POST['device_id'] : $_GET['start'];

        // التحقق من حالة الجهاز
        $stmt = $pdo->prepare("SELECT status FROM devices WHERE device_id = ? AND client_id = ?");
        $stmt->execute([$device_id, $client_id]);
        $device = $stmt->fetch();

        if ($device && $device['status'] == 'available') {
            $pdo->beginTransaction();

            // تحديد معرف العميل (إذا تم اختياره)
            $customer_id = null;
            if (isset($_POST['customer_id']) && !empty($_POST['customer_id'])) {
                $customer_id = $_POST['customer_id'];
            }

            // التحقق من وجود جلسة نشطة للعميل (إذا تم تحديد عميل)
            if ($customer_id) {
                $active_session_check = $pdo->prepare("
                    SELECT COUNT(*) as active_count
                    FROM sessions s
                    JOIN devices d ON s.device_id = d.device_id
                    WHERE s.customer_id = ? AND s.status = 'active' AND d.client_id = ?
                ");
                $active_session_check->execute([$customer_id, $client_id]);
                $active_count = $active_session_check->fetchColumn();

                if ($active_count > 0) {
                    $pdo->rollback();
                    $_SESSION['error'] = "هذا العميل لديه جلسة نشطة بالفعل. لا يمكن إنشاء جلسة جديدة حتى انتهاء الجلسة الحالية.";
                    header('Location: sessions.php');
                    exit;
                }
            }

            // تحديد نوع الجلسة ومدتها
            $session_type = isset($_POST['session_type']) ? $_POST['session_type'] : 'open';
            $session_duration = isset($_POST['session_duration']) ? $_POST['session_duration'] : null;

            // تحديد نوع اللعب
            $game_type = isset($_POST['game_type']) ? $_POST['game_type'] : 'single';

            // تحديد وقت الانتهاء المتوقع للجلسات المحددة الوقت
            $expected_end_time = null;
            if ($session_type === 'timed' && $session_duration) {
                // تحويل المدة من ساعات إلى دقائق
                $duration_minutes = floatval($session_duration) * 60;
                $expected_end_time = date('Y-m-d H:i:s', strtotime("+{$duration_minutes} minutes"));
            }

            // إنشاء جلسة جديدة
            $stmt = $pdo->prepare("INSERT INTO sessions
                (device_id, client_id, customer_id, start_time, expected_end_time, status, notes, game_type, created_by)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, 'active', ?, ?, ?)");
            $stmt->execute([
                $device_id,
                $client_id,
                $customer_id,
                $expected_end_time,
                isset($_POST['notes']) ? $_POST['notes'] : null,
                $game_type,
                $client_id
            ]);

            $session_id = $pdo->lastInsertId();

            // تحديث حالة الجهاز
            $stmt = $pdo->prepare("UPDATE devices SET status = 'occupied' WHERE device_id = ?");
            $stmt->execute([$device_id]);

            $pdo->commit();

            $_SESSION['success'] = "تم بدء الجلسة بنجاح";
        } else {
            $_SESSION['error'] = "الجهاز غير متاح حالياً";
        }
    } catch (PDOException $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $_SESSION['error'] = "حدث خطأ أثناء بدء الجلسة: " . $e->getMessage();
    }
    header('Location: sessions.php');
    exit;
}

// إنهاء جلسة - دعم عدة طرق للإنهاء
if ((isset($_GET['action']) && $_GET['action'] === 'end' && isset($_GET['session_id'])) ||
    (isset($_GET['end']) && is_numeric($_GET['end']))) {

    try {
        $pdo->beginTransaction();

        // تحديد معرف الجلسة أو الجهاز
        if (isset($_GET['action']) && $_GET['action'] === 'end') {
            // الطريقة الجديدة: action=end&session_id=X
            $session_id_to_end = $_GET['session_id'];
            $is_session_id = true;
        } elseif (isset($_GET['session_id'])) {
            // الطريقة: end=X&session_id=1
            $session_id_to_end = $_GET['end'];
            $is_session_id = true;
        } else {
            // الطريقة القديمة: end=device_id
            $session_id_to_end = $_GET['end'];
            $is_session_id = false;
        }

        // جلب معلومات الجلسة النشطة
        if ($is_session_id) {
            // إنهاء بناءً على session_id
            $stmt = $pdo->prepare("
                SELECT
                    s.session_id,
                    s.device_id,
                    s.start_time,
                    d.device_name,
                    d.hourly_rate,
                    TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                WHERE s.session_id = ? AND s.status = 'active' AND d.client_id = ?
            ");
            $stmt->execute([$session_id_to_end, $client_id]);
        } else {
            // إنهاء بناءً على device_id (الطريقة القديمة)
            $stmt = $pdo->prepare("
                SELECT
                    s.session_id,
                    s.device_id,
                    s.start_time,
                    d.device_name,
                    d.hourly_rate,
                    TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes
                FROM sessions s
                JOIN devices d ON s.device_id = d.device_id
                WHERE s.device_id = ? AND s.status = 'active' AND d.client_id = ?
            ");
            $stmt->execute([$session_id_to_end, $client_id]);
        }

        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session) {
            // تسجيل تفصيلي للخطأ
            error_log("End session failed - No active session found. Parameters: " . json_encode($_GET) . ", Client ID: " . $client_id);
            throw new Exception("لم يتم العثور على جلسة نشطة للمعرف المحدد");
        }

        // تسجيل نجاح العثور على الجلسة
        error_log("Session found for ending: " . json_encode($session));

        // حساب تكلفة الوقت بناءً على نوع اللعب (بالدقائق مع تقريب للأعلى للساعة)
        $duration_minutes = $session['duration_minutes'] ?? 0;

        // جلب نوع اللعب من الجلسة
        $session_game_type_stmt = $pdo->prepare("SELECT game_type FROM sessions WHERE session_id = ?");
        $session_game_type_stmt->execute([$session['session_id']]);
        $game_type = $session_game_type_stmt->fetchColumn() ?: 'single';

        // جلب أسعار الجهاز
        $device_rates_stmt = $pdo->prepare("SELECT single_rate, multi_rate, hourly_rate FROM devices WHERE device_id = ?");
        $device_rates_stmt->execute([$session['device_id']]);
        $device_rates = $device_rates_stmt->fetch();

        // اختيار السعر المناسب بناءً على نوع اللعب
        if ($game_type === 'multiplayer') {
            $hourly_rate = $device_rates['multi_rate'] ?? $device_rates['hourly_rate'] ?? 0;
        } else {
            $hourly_rate = $device_rates['single_rate'] ?? $device_rates['hourly_rate'] ?? 0;
        }

        // حساب التكلفة بناءً على إعدادات العميل
        $time_cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);

        // حساب تكلفة المنتجات
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
            FROM session_products sp
            WHERE sp.session_id = ?
        ");
        $stmt->execute([$session['session_id']]);
        $products_cost = $stmt->fetchColumn() ?: 0;

        // إنشاء رقم الفاتورة
        $invoice_number = date('Ymd') . str_pad($session['session_id'], 4, '0', STR_PAD_LEFT);

        // تحديث الجلسة بالتكلفة النهائية
        $total_cost = $time_cost + $products_cost;
        $stmt = $pdo->prepare("
            UPDATE sessions SET
                end_time = CURRENT_TIMESTAMP,
                status = 'completed',
                total_cost = ?,
                updated_by = ?
            WHERE session_id = ? AND status = 'active'
        ");
        $stmt->execute([$total_cost, $client_id, $session['session_id']]);

        // تحديث حالة الجهاز
        $stmt = $pdo->prepare("UPDATE devices SET status = 'available' WHERE device_id = ?");
        $stmt->execute([$session['device_id']]);

        // إنشاء الفاتورة (إذا كان جدول الفواتير موجود)
        try {
            $stmt = $pdo->prepare("
                INSERT INTO invoices (
                    session_id,
                    invoice_number,
                    time_cost,
                    products_cost,
                    total_cost,
                    client_id,
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $session['session_id'],
                $invoice_number,
                $time_cost,
                $products_cost,
                $total_cost,
                $client_id,
                $client_id
            ]);
            $invoice_id = $pdo->lastInsertId();
        } catch (PDOException $e) {
            // إذا لم يكن جدول الفواتير موجود، تجاهل الخطأ
            $invoice_id = null;
        }

        $pdo->commit();

        $_SESSION['success'] = "تم إنهاء الجلسة بنجاح. التكلفة الإجمالية: " . number_format($total_cost, 2) . " ج.م";

        // إعادة توجيه إلى صفحة الفاتورة
        if ($invoice_id) {
            header("Location: invoice.php?id=" . $invoice_id);
        } else {
            // إذا لم يتم إنشاء فاتورة، اعرض الفاتورة من بيانات الجلسة مباشرة
            header("Location: invoice.php?session_id=" . $session['session_id']);
        }
        exit;

    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }

        // تسجيل تفصيلي للخطأ
        error_log("End session error: " . $e->getMessage() . " | Parameters: " . json_encode($_GET) . " | Client ID: " . $client_id);

        $_SESSION['error'] = "حدث خطأ أثناء إنهاء الجلسة: " . $e->getMessage();
        header('Location: sessions.php');
        exit;
    }
}

// التحقق من هيكل جدول customers أولاً
$customer_id_column = 'customer_id';
try {
    $stmt = $pdo->query("DESCRIBE customers");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
        $customer_id_column = 'id';
    }
} catch (PDOException $e) {
    // إذا لم يكن الجدول موجود، سنتجاهل ربط العملاء
    $customer_id_column = null;
}

// جلب الجلسات النشطة مع معلومات العملاء
if ($customer_id_column) {
    $active_sessions = $pdo->prepare("
        SELECT s.*,
               d.device_name,
               d.device_type,
               d.hourly_rate,
               d.single_rate,
               d.multi_rate,
               r.room_name,
               c.name as customer_name,
               c.phone as customer_phone,
               TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
               COALESCE(s.game_type, 'single') as game_type
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN customers c ON s.customer_id = c.$customer_id_column
        WHERE d.client_id = ? AND (s.status = 'active' OR s.status IS NULL)
        ORDER BY s.start_time DESC
    ");
} else {
    $active_sessions = $pdo->prepare("
        SELECT s.*,
               d.device_name,
               d.device_type,
               d.hourly_rate,
               d.single_rate,
               d.multi_rate,
               r.room_name,
               NULL as customer_name,
               NULL as customer_phone,
               TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
               COALESCE(s.game_type, 'single') as game_type
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        WHERE d.client_id = ? AND (s.status = 'active' OR s.status IS NULL)
        ORDER BY s.start_time DESC
    ");
}
$active_sessions->execute([$client_id]);
$active_sessions = $active_sessions->fetchAll();

// جلب الجلسات المكتملة لليوم الحالي فقط
if ($customer_id_column) {
    $today_completed_sessions = $pdo->prepare("
        SELECT s.*,
               d.device_name,
               d.device_type,
               d.hourly_rate,
               d.single_rate,
               d.multi_rate,
               r.room_name,
               c.name as customer_name,
               TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
               COALESCE(s.total_cost, 0) as final_cost,
               COALESCE(s.game_type, 'single') as game_type
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        LEFT JOIN customers c ON s.customer_id = c.$customer_id_column
        WHERE d.client_id = ? AND s.status = 'completed'
        AND DATE(s.end_time) = CURDATE()
        ORDER BY s.end_time DESC
    ");
} else {
    $today_completed_sessions = $pdo->prepare("
        SELECT s.*,
               d.device_name,
               d.device_type,
               d.hourly_rate,
               d.single_rate,
               d.multi_rate,
               r.room_name,
               NULL as customer_name,
               TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
               COALESCE(s.total_cost, 0) as final_cost,
               COALESCE(s.game_type, 'single') as game_type
        FROM sessions s
        JOIN devices d ON s.device_id = d.device_id
        LEFT JOIN rooms r ON d.room_id = r.room_id
        WHERE d.client_id = ? AND s.status = 'completed'
        AND DATE(s.end_time) = CURDATE()
        ORDER BY s.end_time DESC
    ");
}
$today_completed_sessions->execute([$client_id]);
$today_completed_sessions = $today_completed_sessions->fetchAll();

// معالجة البحث والفلتر للجلسات المكتملة
$search_from_date = isset($_GET['from_date']) ? $_GET['from_date'] : '';
$search_to_date = isset($_GET['to_date']) ? $_GET['to_date'] : '';
$search_customer = isset($_GET['search_customer']) ? $_GET['search_customer'] : '';

$filtered_sessions = [];
if ($search_from_date || $search_to_date || $search_customer) {
    $where_conditions = ["d.client_id = ?", "s.status = 'completed'"];
    $params = [$client_id];

    if ($search_from_date) {
        $where_conditions[] = "DATE(s.end_time) >= ?";
        $params[] = $search_from_date;
    }

    if ($search_to_date) {
        $where_conditions[] = "DATE(s.end_time) <= ?";
        $params[] = $search_to_date;
    }

    if ($search_customer && $customer_id_column) {
        $where_conditions[] = "c.name LIKE ?";
        $params[] = "%$search_customer%";
    }

    $where_clause = implode(" AND ", $where_conditions);

    if ($customer_id_column) {
        $filtered_sessions_query = $pdo->prepare("
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   c.name as customer_name,
                   TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
                   COALESCE(s.total_cost, 0) as final_cost,
                   COALESCE(s.game_type, 'single') as game_type
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            LEFT JOIN customers c ON s.customer_id = c.$customer_id_column
            WHERE $where_clause
            ORDER BY s.end_time DESC
            LIMIT 100
        ");
    } else {
        $filtered_sessions_query = $pdo->prepare("
            SELECT s.*,
                   d.device_name,
                   d.device_type,
                   d.hourly_rate,
                   d.single_rate,
                   d.multi_rate,
                   r.room_name,
                   NULL as customer_name,
                   TIMESTAMPDIFF(MINUTE, s.start_time, s.end_time) as duration_minutes,
                   COALESCE(s.total_cost, 0) as final_cost,
                   COALESCE(s.game_type, 'single') as game_type
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            LEFT JOIN rooms r ON d.room_id = r.room_id
            WHERE $where_clause
            ORDER BY s.end_time DESC
            LIMIT 100
        ");
    }

    $filtered_sessions_query->execute($params);
    $filtered_sessions = $filtered_sessions_query->fetchAll();
}

require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- تنبيه حالة الحضور للموظفين -->
    <?php if (isset($_SESSION['employee_id'])): ?>
        <?php if (!$can_create_sessions): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-clock me-2"></i>
                <strong>تنبيه:</strong> <?php echo $attendance_message; ?>
                <br><small>يجب تسجيل الحضور في وردية نشطة لإنشاء جلسات جديدة.</small>
                <a href="attendance.php" class="btn btn-sm btn-outline-primary ms-2">
                    <i class="fas fa-user-check"></i> انتقل لصفحة الحضور
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php elseif ($employee_attendance_status == 'checked_in'): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>حالة الحضور:</strong> مسجل حضور ويمكن إنشاء جلسات جديدة
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <!-- الجلسات النشطة -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-play-circle me-2"></i>الجلسات النشطة</h5>
            <button class="btn btn-light btn-sm" onclick="showAddSessionModal()"
                    <?php echo (!$can_create_sessions && isset($_SESSION['employee_id'])) ? 'disabled title="يجب تسجيل الحضور أولاً"' : ''; ?>>
                <i class="fas fa-plus me-1"></i>إضافة جلسة جديدة
            </button>
        </div>
        <div class="card-body">
            <?php if (count($active_sessions) > 0): ?>
                <div class="row g-2">
                    <?php foreach ($active_sessions as $session): ?>
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 shadow-sm active-session-card" style="font-size: 0.9rem;">
                                <div class="card-header bg-light py-2">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <img src="../assets/images/<?php echo strtolower($session['device_type']); ?>.png"
                                                 alt="<?php echo $session['device_type']; ?>"
                                                 class="me-2"
                                                 style="width: 24px;">
                                            <h6 class="mb-0 small"><?php echo htmlspecialchars($session['device_name']); ?></h6>
                                        </div>
                                        <?php if ($session['game_type'] == 'multiplayer'): ?>
                                            <span class="badge bg-info badge-sm">
                                                <i class="fas fa-users"></i>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary badge-sm">
                                                <i class="fas fa-user"></i>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row g-1">
                                        <div class="col-6">
                                            <small class="text-muted d-block">التكلفة:</small>
                                            <?php
                                            // حساب التكلفة الحالية بناءً على نوع اللعب
                                            $duration_minutes = $session['duration_minutes'] ?? 0;
                                            $game_type = $session['game_type'] ?? 'single';

                                            // اختيار السعر المناسب بناءً على نوع اللعب
                                            if ($game_type === 'multiplayer') {
                                                $hourly_rate = $session['multi_rate'] ?? $session['hourly_rate'] ?? 0;
                                            } else {
                                                $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                                            }

                                            // حساب التكلفة بناءً على إعدادات العميل
                                            $time_cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);

                                            // جلب تكلفة المنتجات
                                            try {
                                                $products_stmt = $pdo->prepare("
                                                    SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                                                    FROM session_products sp
                                                    WHERE sp.session_id = ?
                                                ");
                                                $products_stmt->execute([$session['session_id']]);
                                                $products_cost = $products_stmt->fetchColumn() ?: 0;
                                            } catch (PDOException $e) {
                                                $products_cost = 0;
                                            }

                                            $total_cost = $time_cost + $products_cost;
                                            ?>
                                            <strong class="text-primary session-cost" data-session-id="<?php echo $session['session_id']; ?>" data-hourly-rate="<?php echo $hourly_rate; ?>"><?php echo number_format($total_cost, 2); ?> ج.م</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted d-block">المدة:</small>
                                            <div class="timer fw-bold text-success" data-start="<?php echo $session['start_time']; ?>">
                                                <?php
                                                $duration_minutes = $session['duration_minutes'] ?? 0;
                                                if ($duration_minutes > 0) {
                                                    $hours = floor($duration_minutes / 60);
                                                    $minutes = $duration_minutes % 60;
                                                    echo sprintf("%02d:%02d", $hours, $minutes);
                                                } else {
                                                    echo "00:00";
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($session['customer_name']): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">العميل:</small>
                                        <div class="small"><?php echo htmlspecialchars($session['customer_name']); ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="mt-2">
                                        <small class="text-muted">بدأت:</small>
                                        <div class="small"><?php echo date('h:i A', strtotime($session['start_time'])); ?></div>
                                    </div>
                                </div>
                                <div class="card-footer py-2">
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-success btn-sm flex-fill" onclick="editSession(<?php echo $session['session_id']; ?>)" style="font-size: 0.8rem;">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button class="btn btn-warning btn-sm flex-fill" onclick="endSession(<?php echo $session['session_id']; ?>, true)" style="font-size: 0.8rem;">
                                            <i class="fas fa-stop-circle"></i> إنهاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-coffee fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد جلسات نشطة حالياً</h5>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- الجلسات المكتملة لليوم الحالي -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>الجلسات المكتملة اليوم (<?php echo date('Y-m-d'); ?>)</h5>
            <?php if (count($today_completed_sessions) > 0): ?>
                <div class="d-flex gap-3">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-list me-1"></i><?php echo count($today_completed_sessions); ?> جلسة
                    </span>
                    <?php
                    // حساب إجمالي الإيرادات لليوم
                    $today_total_revenue = 0;
                    foreach ($today_completed_sessions as $session) {
                        $today_total_revenue += $session['final_cost'] > 0 ? $session['final_cost'] : 0;
                    }
                    ?>
                    <span class="badge bg-success">
                        <i class="fas fa-money-bill-wave me-1"></i><?php echo number_format($today_total_revenue, 2); ?> ج.م
                    </span>
                </div>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if (count($today_completed_sessions) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-gamepad me-1"></i>الجهاز</th>
                                <th><i class="fas fa-users me-1"></i>نوع اللعب</th>
                                <th><i class="fas fa-money-bill-wave me-1"></i>التكلفة الإجمالية</th>
                                <th><i class="fas fa-door-open me-1"></i>الغرفة</th>
                                <th><i class="fas fa-clock me-1"></i>الوقت</th>
                                <th><i class="fas fa-hourglass-half me-1"></i>المدة</th>
                                <th><i class="fas fa-receipt me-1"></i>تفاصيل التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($today_completed_sessions as $session): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="../assets/images/<?php echo strtolower($session['device_type']); ?>.png"
                                                 alt="<?php echo $session['device_type']; ?>"
                                                 class="me-2"
                                                 style="width: 32px;">
                                            <?php echo htmlspecialchars($session['device_name']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($session['game_type'] == 'multiplayer'): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-users me-1"></i>زوجي
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-user me-1"></i>فردي
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong class="text-primary fs-5">
                                                <?php
                                                // استخدام التكلفة المحفوظة إذا كانت متاحة
                                                if ($session['final_cost'] > 0) {
                                                    echo number_format($session['final_cost'], 2);
                                                } else {
                                                    // حساب التكلفة إذا لم تكن محفوظة
                                                    $duration_minutes = $session['duration_minutes'] ?? 0;
                                                    $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                                                    // حساب التكلفة بناءً على إعدادات العميل
                                                    $cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);
                                                    echo number_format($cost, 2);
                                                }
                                                echo ' ج.م';
                                                ?>
                                            </strong>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($session['room_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <div><?php echo date('h:i A', strtotime($session['start_time'])); ?></div>
                                        <small class="text-muted">إلى <?php echo date('h:i A', strtotime($session['end_time'])); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $duration_minutes = $session['duration_minutes'] ?? 0;
                                        if ($duration_minutes > 0) {
                                            $hours = floor($duration_minutes / 60);
                                            $minutes = $duration_minutes % 60;
                                            echo sprintf("%02d:%02d", $hours, $minutes);
                                        } else {
                                            echo "00:00";
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php
                                            // حساب تفاصيل التكلفة
                                            $duration_minutes = $session['duration_minutes'] ?? 0;
                                            $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                                            // حساب التكلفة بناءً على إعدادات العميل
                                            $time_cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);

                                            // جلب تكلفة المنتجات
                                            try {
                                                $products_stmt = $pdo->prepare("
                                                    SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                                                    FROM session_products sp
                                                    WHERE sp.session_id = ?
                                                ");
                                                $products_stmt->execute([$session['session_id']]);
                                                $products_cost = $products_stmt->fetchColumn() ?: 0;
                                            } catch (PDOException $e) {
                                                $products_cost = 0;
                                            }
                                            ?>
                                            <div class="text-info">
                                                <i class="fas fa-clock me-1"></i>وقت: <?php echo number_format($time_cost, 2); ?> ج.م
                                            </div>
                                            <div class="text-warning">
                                                <i class="fas fa-coffee me-1"></i>منتجات: <?php echo number_format($products_cost, 2); ?> ج.م
                                            </div>
                                            <?php if ($session['customer_name']): ?>
                                                <div class="text-muted mt-1">
                                                    <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($session['customer_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-calendar-day fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد جلسات مكتملة اليوم</h5>
                    <p class="text-muted">استخدم البحث أدناه لعرض جلسات من أيام أخرى</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- البحث والفلتر للجلسات المكتملة -->
    <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-search me-2"></i>البحث في الجلسات المكتملة</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="sessions.php" class="row g-3">
                <div class="col-md-3">
                    <label for="from_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>من تاريخ
                    </label>
                    <input type="date" class="form-control" id="from_date" name="from_date"
                           value="<?php echo htmlspecialchars($search_from_date); ?>">
                </div>
                <div class="col-md-3">
                    <label for="to_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>إلى تاريخ
                    </label>
                    <input type="date" class="form-control" id="to_date" name="to_date"
                           value="<?php echo htmlspecialchars($search_to_date); ?>">
                </div>
                <div class="col-md-3">
                    <label for="search_customer" class="form-label">
                        <i class="fas fa-user me-1"></i>اسم العميل
                    </label>
                    <input type="text" class="form-control" id="search_customer" name="search_customer"
                           placeholder="ابحث باسم العميل..."
                           value="<?php echo htmlspecialchars($search_customer); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
                <div class="col-12">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="sessions.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times me-1"></i>مسح الفلتر
                        </a>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange('today')">
                            <i class="fas fa-calendar-day me-1"></i>اليوم
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange('yesterday')">
                            <i class="fas fa-calendar-minus me-1"></i>أمس
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange('week')">
                            <i class="fas fa-calendar-week me-1"></i>هذا الأسبوع
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setDateRange('month')">
                            <i class="fas fa-calendar me-1"></i>هذا الشهر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- نتائج البحث -->
    <?php if ($search_from_date || $search_to_date || $search_customer): ?>
    <div class="card shadow-sm mt-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>نتائج البحث
                <?php if ($search_from_date && $search_to_date): ?>
                    (من <?php echo $search_from_date; ?> إلى <?php echo $search_to_date; ?>)
                <?php elseif ($search_from_date): ?>
                    (من <?php echo $search_from_date; ?>)
                <?php elseif ($search_to_date): ?>
                    (حتى <?php echo $search_to_date; ?>)
                <?php endif; ?>
                <?php if ($search_customer): ?>
                    - العميل: <?php echo htmlspecialchars($search_customer); ?>
                <?php endif; ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if (count($filtered_sessions) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-gamepad me-1"></i>الجهاز</th>
                                <th><i class="fas fa-users me-1"></i>نوع اللعب</th>
                                <th><i class="fas fa-money-bill-wave me-1"></i>التكلفة الإجمالية</th>
                                <th><i class="fas fa-door-open me-1"></i>الغرفة</th>
                                <th><i class="fas fa-calendar me-1"></i>التاريخ</th>
                                <th><i class="fas fa-clock me-1"></i>المدة</th>
                                <th><i class="fas fa-receipt me-1"></i>تفاصيل التكلفة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($filtered_sessions as $session): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="../assets/images/<?php echo strtolower($session['device_type']); ?>.png"
                                                 alt="<?php echo $session['device_type']; ?>"
                                                 class="me-2"
                                                 style="width: 32px;">
                                            <?php echo htmlspecialchars($session['device_name']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($session['game_type'] == 'multiplayer'): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-users me-1"></i>زوجي
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-user me-1"></i>فردي
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong class="text-primary fs-5">
                                                <?php
                                                // استخدام التكلفة المحفوظة إذا كانت متاحة
                                                if ($session['final_cost'] > 0) {
                                                    echo number_format($session['final_cost'], 2);
                                                } else {
                                                    // حساب التكلفة إذا لم تكن محفوظة
                                                    $duration_minutes = $session['duration_minutes'] ?? 0;
                                                    $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                                                    // حساب التكلفة بناءً على إعدادات العميل
                                                    $cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);
                                                    echo number_format($cost, 2);
                                                }
                                                echo ' ج.م';
                                                ?>
                                            </strong>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($session['room_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <div><?php echo date('h:i A', strtotime($session['start_time'])); ?></div>
                                        <small class="text-muted"><?php echo date('Y-m-d', strtotime($session['start_time'])); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $duration_minutes = $session['duration_minutes'] ?? 0;
                                        if ($duration_minutes > 0) {
                                            $hours = floor($duration_minutes / 60);
                                            $minutes = $duration_minutes % 60;
                                            echo sprintf("%02d:%02d", $hours, $minutes);
                                        } else {
                                            echo "00:00";
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php
                                            // حساب تفاصيل التكلفة
                                            $duration_minutes = $session['duration_minutes'] ?? 0;
                                            $hourly_rate = $session['single_rate'] ?? $session['hourly_rate'] ?? 0;
                                            // حساب التكلفة بناءً على إعدادات العميل
                                            $time_cost = calculateTimeCost($pdo, $client_id, $duration_minutes, $hourly_rate);

                                            // جلب تكلفة المنتجات
                                            try {
                                                $products_stmt = $pdo->prepare("
                                                    SELECT COALESCE(SUM(sp.quantity * sp.price), 0) as products_cost
                                                    FROM session_products sp
                                                    WHERE sp.session_id = ?
                                                ");
                                                $products_stmt->execute([$session['session_id']]);
                                                $products_cost = $products_stmt->fetchColumn() ?: 0;
                                            } catch (PDOException $e) {
                                                $products_cost = 0;
                                            }
                                            ?>
                                            <div class="text-info">
                                                <i class="fas fa-clock me-1"></i>وقت: <?php echo number_format($time_cost, 2); ?> ج.م
                                            </div>
                                            <div class="text-warning">
                                                <i class="fas fa-coffee me-1"></i>منتجات: <?php echo number_format($products_cost, 2); ?> ج.م
                                            </div>
                                            <?php if ($session['customer_name']): ?>
                                                <div class="text-muted mt-1">
                                                    <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($session['customer_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        تم العثور على <?php echo count($filtered_sessions); ?> جلسة مكتملة
                        <?php if (count($filtered_sessions) >= 100): ?>
                            (تم عرض أول 100 نتيجة فقط)
                        <?php endif; ?>
                    </small>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">جرب تغيير معايير البحث</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Modal إضافة منتجات -->
<div class="modal fade" id="addProductsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة منتجات للجلسة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="currentSessionId">
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchProducts" placeholder="البحث في المنتجات...">
                </div>
                <div class="row g-3" id="productsList">
                    <!-- سيتم تحميل المنتجات هنا -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنهاء الجلسة مع المنتجات -->
<div class="modal fade" id="endSessionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-stop-circle me-2"></i>إنهاء الجلسة وإضافة المنتجات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="endSessionId">

                <!-- معلومات الجلسة -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>معلومات الجلسة:</h6>
                    <div id="endSessionInfo"></div>
                </div>

                <!-- اختيار المنتجات -->
                <div class="mb-3">
                    <h6><i class="fas fa-coffee me-1"></i>إضافة منتجات للفاتورة (اختياري):</h6>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="searchEndProducts" placeholder="البحث في المنتجات...">
                    </div>
                    <div class="row g-3" id="endProductsList">
                        <!-- سيتم تحميل المنتجات هنا -->
                    </div>
                </div>

                <!-- المنتجات المضافة -->
                <div class="mb-3">
                    <h6>المنتجات المضافة للفاتورة:</h6>
                    <div id="selectedEndProducts">
                        <p class="text-muted">لم يتم إضافة منتجات بعد</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="confirmEndSession()">
                    <i class="fas fa-check me-1"></i>إنهاء الجلسة وإنشاء الفاتورة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة جلسة جديدة -->
<div class="modal fade" id="addSessionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>إضافة جلسة جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="sessions.php">
                <div class="modal-body">
                    <input type="hidden" name="start_session" value="1">

                    <!-- اختيار الجهاز -->
                    <div class="mb-3">
                        <label for="device_id" class="form-label">
                            <i class="fas fa-gamepad me-1"></i>اختر الجهاز
                        </label>
                        <select class="form-select" name="device_id" id="device_id" required>
                            <option value="">-- اختر جهاز --</option>
                            <?php
                            // جلب الأجهزة المتاحة
                            $available_devices_stmt = $pdo->prepare("
                                SELECT d.device_id, d.device_name, d.device_type, d.hourly_rate, d.single_rate, d.multi_rate, r.room_name
                                FROM devices d
                                LEFT JOIN rooms r ON d.room_id = r.room_id
                                WHERE d.client_id = ? AND d.status = 'available'
                                ORDER BY r.room_name, d.device_name
                            ");
                            $available_devices_stmt->execute([$client_id]);
                            $available_devices_list = $available_devices_stmt->fetchAll();

                            foreach ($available_devices_list as $device): ?>
                                <option value="<?php echo $device['device_id']; ?>"
                                        data-hourly-rate="<?php echo $device['hourly_rate']; ?>"
                                        data-single-rate="<?php echo $device['single_rate']; ?>"
                                        data-multi-rate="<?php echo $device['multi_rate']; ?>">
                                    <?php echo htmlspecialchars($device['device_name']); ?>
                                    <?php if ($device['room_name']): ?>
                                        - <?php echo htmlspecialchars($device['room_name']); ?>
                                    <?php endif; ?>
                                    (<?php echo htmlspecialchars($device['device_type']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- اختيار العميل -->
                    <div class="mb-3">
                        <label for="customer_id_select_add" class="form-label">
                            <i class="fas fa-user me-1"></i>العميل (اختياري)
                        </label>
                        <select class="form-select" name="customer_id" id="customer_id_select_add">
                            <option value="">-- اختر عميل أو اتركه فارغاً --</option>
                            <?php
                            // جلب العملاء المتاحين
                            try {
                                // التحقق من وجود عمود customer_id في جدول customers
                                $customer_id_column = null;
                                try {
                                    $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'customer_id'");
                                    if ($columns_check->rowCount() > 0) {
                                        $customer_id_column = 'customer_id';
                                    } else {
                                        $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'id'");
                                        if ($columns_check->rowCount() > 0) {
                                            $customer_id_column = 'id';
                                        }
                                    }
                                } catch (PDOException $e) {
                                    $customer_id_column = 'customer_id'; // افتراضي
                                }

                                if ($customer_id_column) {
                                    $customers_stmt = $pdo->prepare("
                                        SELECT $customer_id_column as id, name, phone
                                        FROM customers
                                        WHERE client_id = ?
                                        ORDER BY name ASC
                                    ");
                                    $customers_stmt->execute([$client_id]);
                                    $customers_list = $customers_stmt->fetchAll();

                                    foreach ($customers_list as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if ($customer['phone']): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach;
                                }
                            } catch (PDOException $e) {
                                // في حالة عدم وجود جدول customers أو خطأ
                                echo '<option value="" disabled>لا يوجد عملاء متاحين</option>';
                            }
                            ?>
                        </select>
                        <small class="form-text text-muted">
                            يمكنك ترك هذا الحقل فارغاً لبدء جلسة بدون عميل محدد
                        </small>
                    </div>

                    <!-- نوع الجلسة -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-clock me-1"></i>نوع الجلسة
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="session_type" id="session_type_open_add" value="open" checked>
                            <label class="form-check-label" for="session_type_open_add">
                                جلسة مفتوحة (بدون وقت محدد)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="session_type" id="session_type_timed_add" value="timed">
                            <label class="form-check-label" for="session_type_timed_add">
                                جلسة محددة الوقت
                            </label>
                        </div>
                    </div>

                    <!-- نوع اللعب -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>نوع اللعب
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="game_type" id="game_type_single_add" value="single" checked>
                            <label class="form-check-label" for="game_type_single_add">
                                <i class="fas fa-user me-1"></i>فردي (لاعب واحد)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="game_type" id="game_type_multiplayer_add" value="multiplayer">
                            <label class="form-check-label" for="game_type_multiplayer_add">
                                <i class="fas fa-users me-1"></i>زوجي (أكثر من لاعب)
                            </label>
                        </div>
                    </div>

                    <!-- مدة الجلسة (للجلسات المحددة الوقت) -->
                    <div class="mb-3" id="session_duration_section_add" style="display: none;">
                        <label for="session_duration_add" class="form-label">
                            <i class="fas fa-hourglass-half me-1"></i>مدة الجلسة (بالساعات)
                        </label>
                        <select class="form-select" name="session_duration" id="session_duration_add">
                            <option value="0.5">نصف ساعة</option>
                            <option value="1" selected>ساعة واحدة</option>
                            <option value="1.5">ساعة ونصف</option>
                            <option value="2">ساعتان</option>
                            <option value="3">3 ساعات</option>
                            <option value="4">4 ساعات</option>
                            <option value="6">6 ساعات</option>
                            <option value="8">8 ساعات</option>
                        </select>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes_add" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>ملاحظات (اختياري)
                        </label>
                        <textarea class="form-control" name="notes" id="notes_add" rows="3"
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- التكلفة المتوقعة -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-calculator me-1"></i>التكلفة المتوقعة:</h6>
                        <div id="estimated_cost_add" class="fs-5 fw-bold text-primary">0.00 ج.م</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success" <?php echo (!$can_create_sessions && isset($_SESSION['employee_id'])) ? 'disabled' : ''; ?>>
                        <i class="fas fa-play me-1"></i>بدء الجلسة
                        <?php if (!$can_create_sessions && isset($_SESSION['employee_id'])): ?>
                            <br><small>يجب تسجيل الحضور أولاً</small>
                        <?php endif; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل الجلسة -->
<div class="modal fade" id="editSessionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>تعديل الجلسة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSessionForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_session_id">

                    <!-- معلومات الجهاز (للعرض فقط) -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-gamepad me-1"></i>الجهاز
                        </label>
                        <input type="text" class="form-control" id="edit_device_info" readonly>
                    </div>

                    <!-- اختيار العميل -->
                    <div class="mb-3">
                        <label for="customer_id_select_edit" class="form-label">
                            <i class="fas fa-user me-1"></i>العميل
                        </label>
                        <select class="form-select" id="customer_id_select_edit">
                            <option value="">-- اختر عميل أو اتركه فارغاً --</option>
                            <?php
                            // جلب العملاء المتاحين للتعديل
                            try {
                                if ($customer_id_column) {
                                    foreach ($customers_list as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if ($customer['phone']): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach;
                                }
                            } catch (PDOException $e) {
                                echo '<option value="" disabled>لا يوجد عملاء متاحين</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- نوع اللعب -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>نوع اللعب
                        </label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="game_type_edit" id="game_type_single_edit" value="single" checked>
                            <label class="form-check-label" for="game_type_single_edit">
                                <i class="fas fa-user me-1"></i>فردي (لاعب واحد)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="game_type_edit" id="game_type_multiplayer_edit" value="multiplayer">
                            <label class="form-check-label" for="game_type_multiplayer_edit">
                                <i class="fas fa-users me-1"></i>زوجي (أكثر من لاعب)
                            </label>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes_edit" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>ملاحظات
                        </label>
                        <textarea class="form-control" id="notes_edit" rows="3"
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">وقت البدء</label>
                                <input type="text" class="form-control" id="edit_start_time" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المدة الحالية</label>
                                <input type="text" class="form-control" id="edit_duration" readonly>
                            </div>
                        </div>
                    </div>

                    <!-- إدارة المنتجات -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-coffee me-1"></i>إدارة المنتجات
                        </label>
                        <div class="border rounded p-3">
                            <!-- المنتجات الحالية -->
                            <div class="mb-3">
                                <h6 class="text-muted">المنتجات المضافة:</h6>
                                <div id="edit_session_products" class="mb-3">
                                    <!-- سيتم تحميل المنتجات هنا -->
                                </div>
                            </div>

                            <!-- إضافة منتج جديد -->
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-info btn-sm" onclick="showAddProductsForEdit()">
                                    <i class="fas fa-plus me-1"></i>إضافة منتج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>حفظ التعديلات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحديث الوقت والتكلفة كل ثانية
function updateTimers() {
    document.querySelectorAll('.timer').forEach(function(timer) {
        let startTime = new Date(timer.dataset.start).getTime();
        let now = new Date().getTime();
        let diff = Math.floor((now - startTime) / 1000);

        let hours = Math.floor(diff / 3600);
        let minutes = Math.floor((diff % 3600) / 60);
        let seconds = diff % 60;

        timer.textContent =
            String(hours).padStart(2, '0') + ':' +
            String(minutes).padStart(2, '0') + ':' +
            String(seconds).padStart(2, '0');
    });

    // تحديث التكلفة للجلسات النشطة
    updateSessionCosts();
}

// دالة تحديث تكلفة الجلسات النشطة
let lastCostUpdate = 0;
function updateSessionCosts() {
    // تحديث التكلفة كل 30 ثانية فقط لتجنب الضغط على الخادم
    const now = Date.now();
    if (now - lastCostUpdate < 30000) {
        return;
    }
    lastCostUpdate = now;

    document.querySelectorAll('.session-cost[data-session-id]').forEach(function(costElement) {
        const sessionId = costElement.dataset.sessionId;
        const hourlyRate = parseFloat(costElement.dataset.hourlyRate) || 0;

        if (hourlyRate > 0) {
            const timerElement = costElement.closest('.card-body').querySelector('.timer[data-start]');

            if (timerElement) {
                const startTime = new Date(timerElement.dataset.start).getTime();
                const diffMinutes = Math.floor((now - startTime) / (1000 * 60));

                if (diffMinutes > 0) {
                    // حساب تكلفة الوقت
                    const timeCost = Math.ceil(diffMinutes / 60) * hourlyRate;

                    // جلب تكلفة المنتجات من API
                    fetch(`api/get_session_products.php?session_id=${sessionId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const productsCost = data.products.reduce((total, product) => {
                                    return total + (product.quantity * product.price);
                                }, 0);

                                const totalCost = timeCost + productsCost;
                                costElement.textContent = totalCost.toFixed(2) + ' ج.م';
                            }
                        })
                        .catch(error => {
                            // في حالة الخطأ، استخدم تكلفة الوقت فقط
                            costElement.textContent = timeCost.toFixed(2) + ' ج.م';
                        });
                }
            }
        }
    });
}

// تحديث كل ثانية
setInterval(updateTimers, 1000);
// تشغيل التحديث فور تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateTimers);

function endSession(sessionId, isSessionId = false) {
    // إظهار modal إنهاء الجلسة مع إمكانية إضافة المنتجات
    showEndSessionModal(sessionId, isSessionId);
}

function addProducts(sessionId) {
    let currentPage = 1;
    let searchQuery = '';
    
    // إضافة مستمع لحدث البحث
    const searchInput = document.getElementById('searchProducts');
    searchInput.addEventListener('input', function(e) {
        searchQuery = e.target.value;
        loadProducts(1); // إعادة تحميل الصفحة الأولى مع البحث
    });
    
    function loadProducts(page) {
        const productsList = document.getElementById('productsList');
        productsList.innerHTML = '<div class="text-center w-100"><div class="spinner-border text-primary" role="status"></div></div>';
        
        fetch(`api/get_products.php?page=${page}&search=${encodeURIComponent(searchQuery)}`)
            .then(response => response.json())
            .then(result => {
                if (!result.success) {
                    throw new Error(result.error || 'فشل في جلب المنتجات');
                }
                
                const { products, currentPage, totalPages } = result.data;
                
                if (products.length === 0) {
                    productsList.innerHTML = '<div class="col-12 text-center"><p>لا توجد منتجات متاحة</p></div>';
                    return;
                }
                
                // عرض المنتجات
                productsList.innerHTML = `
                    <div class="row g-3 mb-3">
                        ${products.map(product => `
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">${product.name}</h6>
                                        <p class="card-text text-primary fw-bold">${product.price_formatted || product.price} ج.م</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <button class="btn btn-outline-secondary btn-sm" onclick="decrementQuantity('qty_${product.id}')">-</button>
                                            <input type="number" id="qty_${product.id}" class="form-control form-control-sm mx-2" value="1" min="1" style="width: 60px">
                                            <button class="btn btn-outline-secondary btn-sm" onclick="incrementQuantity('qty_${product.id}')">+</button>
                                        </div>
                                        <button class="btn btn-primary btn-sm w-100" 
                                                onclick="addProductToSession(${sessionId}, ${product.id}, document.getElementById('qty_${product.id}').value)">
                                            <i class="fas fa-plus-circle me-1"></i>إضافة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                if (totalPages > 1) {
                    const paginationDiv = document.createElement('div');
                    paginationDiv.className = 'd-flex justify-content-center gap-2 mt-3';
                    paginationDiv.innerHTML = Array.from({ length: totalPages }, (_, i) => i + 1)
                        .map(pageNum => `
                            <button class="btn btn-${pageNum === currentPage ? 'primary' : 'outline-primary'} btn-sm"
                                    onclick="window.loadProducts(${pageNum})"
                                    ${pageNum === currentPage ? 'disabled' : ''}>
                                ${pageNum}
                            </button>
                        `).join('');
                    productsList.appendChild(paginationDiv);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                productsList.innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <p><i class="fas fa-exclamation-triangle me-2"></i>${error.message}</p>
                    </div>`;
            });
    }

    const modal = new bootstrap.Modal(document.getElementById('addProductsModal'));
    modal.show();
    
    window.loadProducts = loadProducts;
    loadProducts(currentPage);
}

function addProductToSession(sessionId, productId, quantity) {
    fetch('api/add_session_product.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId,
            product_id: productId,
            quantity: parseInt(quantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث التكلفة الإجمالية فوراً
            const costElement = document.querySelector(`.session-cost[data-session-id="${sessionId}"]`);
            if (costElement) {
                costElement.textContent = data.total_cost + ' ج.م';
            }
            
            // تحديث قائمة المنتجات
            updateSessionProducts(sessionId);
            
            Swal.fire({
                icon: 'success',
                title: 'تم إضافة المنتج بنجاح',
                text: `تمت إضافة ${quantity} ${data.product_name}`,
                showConfirmButton: false,
                timer: 1500
            });

            // إعادة تعيين قيمة الكمية
            const qtyInput = document.getElementById(`qty_${productId}`);
            if (qtyInput) {
                qtyInput.value = 1;
            }
        } else {
            throw new Error(data.error || 'حدث خطأ أثناء إضافة المنتج');
        }
    })
    .catch(error => {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: error.message
        });
    });
}

// دالة تحديث المنتجات
function updateSessionProducts(sessionId) {
    fetch(`api/get_session_products.php?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const productsContainer = document.querySelector(`[data-session-id="${sessionId}"] .session-products`);
                if (productsContainer) {
                    productsContainer.innerHTML = data.products.length > 0 ? `
                        <div class="list-group">
                            ${data.products.map(product => `
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">${product.product_name}</h6>
                                        <small class="text-muted">
                                            ${product.quantity} × ${product.price} ج.م
                                        </small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="me-3">${product.total} ج.م</span>
                                        <button class="btn btn-danger btn-sm" 
                                                onclick="deleteSessionProduct(${sessionId}, ${product.product_id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="text-muted">لا توجد منتجات مضافة</p>';
                }
            }
        });
}

function incrementQuantity(inputId) {
    const input = document.getElementById(inputId);
    input.value = parseInt(input.value) + 1;
}

function decrementQuantity(inputId) {
    const input = document.getElementById(inputId);
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

function deleteSessionProduct(sessionId, productId) {
    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا المنتج؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('api/delete_session_product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث التكلفة الإجمالية
                    const costElement = document.querySelector(`.session-cost[data-session-id="${sessionId}"]`);
                    if (costElement) {
                        costElement.textContent = `${data.total_cost} ج.م`;
                    }

                    // تحديث قائمة المنتجات
                    updateSessionProducts(sessionId);

                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف بنجاح',
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    throw new Error(data.error);
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: error.message
                });
            });
        }
    });
}

// دالة إظهار modal إضافة جلسة جديدة
function showAddSessionModal() {
    <?php if (!$can_create_sessions && isset($_SESSION['employee_id'])): ?>
        Swal.fire({
            icon: 'warning',
            title: 'تسجيل الحضور مطلوب',
            text: '<?php echo $attendance_message; ?>',
            confirmButtonText: 'انتقل لصفحة الحضور',
            showCancelButton: true,
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = 'attendance.php';
            }
        });
        return;
    <?php endif; ?>

    const modal = new bootstrap.Modal(document.getElementById('addSessionModal'));

    // إعادة تعيين النموذج
    document.getElementById('device_id').value = '';
    document.getElementById('customer_id_select_add').value = '';
    document.getElementById('session_type_open_add').checked = true;
    document.getElementById('game_type_single_add').checked = true;
    document.getElementById('session_duration_section_add').style.display = 'none';
    document.getElementById('notes_add').value = '';

    // تحديث التكلفة المتوقعة
    setTimeout(() => {
        updateEstimatedCostAdd();
    }, 100);

    modal.show();
}

// دالة إظهار modal تعديل الجلسة
function editSession(sessionId) {
    // جلب بيانات الجلسة
    fetch(`api/get_session_details.php?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const session = data.session;

                // ملء البيانات في النموذج
                document.getElementById('edit_session_id').value = sessionId;
                document.getElementById('edit_device_info').value = `${session.device_name} - ${session.room_name || 'غير محدد'}`;
                document.getElementById('edit_start_time').value = new Date(session.start_time).toLocaleString('ar-EG');
                document.getElementById('edit_duration').value = session.duration_display;
                document.getElementById('notes_edit').value = session.notes || '';

                // تعيين العميل إذا كان موجوداً
                const customerSelect = document.getElementById('customer_id_select_edit');
                if (session.customer_id) {
                    customerSelect.value = session.customer_id;
                } else {
                    customerSelect.value = '';
                }

                // تعيين نوع اللعب
                const gameType = session.game_type || 'single';
                if (gameType === 'multiplayer') {
                    document.getElementById('game_type_multiplayer_edit').checked = true;
                } else {
                    document.getElementById('game_type_single_edit').checked = true;
                }

                // تحميل المنتجات المضافة للجلسة
                loadEditSessionProducts(sessionId);

                // إظهار الـ modal
                const modal = new bootstrap.Modal(document.getElementById('editSessionModal'));
                modal.show();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.error || 'فشل في جلب بيانات الجلسة'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء جلب بيانات الجلسة'
            });
        });
}

// دوال مساعدة للنماذج

// دالة تحميل المنتجات في modal التعديل
function loadEditSessionProducts(sessionId) {
    console.log('Loading products for session:', sessionId);

    if (!sessionId) {
        console.error('Session ID is required');
        return;
    }

    const productsContainer = document.getElementById('edit_session_products');
    if (!productsContainer) {
        console.error('Products container not found: edit_session_products');
        return;
    }

    // إظهار مؤشر التحميل
    productsContainer.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري تحميل المنتجات...</p></div>';

    fetch(`api/get_session_products.php?session_id=${sessionId}`)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);

            try {
                const data = JSON.parse(responseText);
                console.log('Parsed data:', data);

                if (data.success) {
                    if (data.products && data.products.length > 0) {
                        // حساب إجمالي تكلفة المنتجات
                        const totalProductsCost = data.products.reduce((sum, product) => sum + parseFloat(product.total), 0);

                        productsContainer.innerHTML = `
                            <div class="list-group">
                                ${data.products.map(product => `
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">${product.product_name}</h6>
                                            <small class="text-muted">
                                                ${product.quantity} × ${product.price} ج.م
                                            </small>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="me-3 fw-bold">${product.total} ج.م</span>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    onclick="deleteEditSessionProduct(${sessionId}, ${product.product_id}); return false;"
                                                    title="حذف المنتج">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="mt-2 p-2 bg-light rounded">
                                <div class="d-flex justify-content-between">
                                    <span><strong>إجمالي تكلفة المنتجات:</strong></span>
                                    <span class="fw-bold text-primary">${totalProductsCost.toFixed(2)} ج.م</span>
                                </div>
                            </div>
                        `;
                    } else {
                        productsContainer.innerHTML = `
                            <div class="text-center p-3">
                                <i class="fas fa-coffee fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">لا توجد منتجات مضافة</p>
                                <small class="text-muted">اضغط على "إضافة منتج" لإضافة منتجات للجلسة</small>
                            </div>
                        `;
                    }
                } else {
                    throw new Error(data.error || 'فشل في جلب المنتجات');
                }
            } catch (jsonError) {
                console.error('JSON parsing error:', jsonError);
                productsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ في تحليل البيانات:</strong> ${jsonError.message}
                        <details class="mt-2">
                            <summary>تفاصيل الخطأ</summary>
                            <pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">${responseText}</pre>
                        </details>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            productsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <strong>خطأ في تحميل المنتجات:</strong> ${error.message}
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadEditSessionProducts(${sessionId})">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        });
}

// دالة حذف منتج من modal التعديل
function deleteEditSessionProduct(sessionId, productId) {
    // منع إرسال النموذج
    event.preventDefault();
    event.stopPropagation();

    console.log('Deleting product:', { sessionId, productId });

    if (!sessionId || !productId) {
        console.error('Session ID or Product ID is missing');
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'بيانات غير مكتملة'
        });
        return false;
    }

    Swal.fire({
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا المنتج؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار مؤشر التحميل
            Swal.fire({
                title: 'جاري الحذف...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            const deleteData = {
                session_id: parseInt(sessionId),
                product_id: parseInt(productId)
            };

            console.log('Sending delete request:', deleteData);

            fetch('api/delete_session_product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(deleteData)
            })
            .then(response => {
                console.log('Delete response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(responseText => {
                console.log('Delete raw response:', responseText);

                try {
                    const data = JSON.parse(responseText);
                    console.log('Delete parsed data:', data);

                    if (data.success) {
                        // إظهار رسالة النجاح أولاً
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الحذف بنجاح',
                            text: data.message,
                            showConfirmButton: false,
                            timer: 1500
                        });

                        // تحديث قائمة المنتجات في modal التعديل فوراً
                        setTimeout(() => {
                            loadEditSessionProducts(sessionId);
                        }, 100);

                        // تحديث التكلفة الإجمالية في الجدول الرئيسي
                        const costElement = document.querySelector(`.session-cost[data-session-id="${sessionId}"]`);
                        if (costElement) {
                            costElement.textContent = data.total_cost + ' ج.م';
                        }

                        // تحديث قائمة المنتجات في الجدول الرئيسي أيضاً
                        updateSessionProducts(sessionId);
                    } else {
                        throw new Error(data.error || 'فشل في حذف المنتج');
                    }
                } catch (jsonError) {
                    console.error('JSON parsing error:', jsonError);
                    throw new Error('خطأ في تحليل استجابة الخادم: ' + jsonError.message);
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في الحذف',
                    text: error.message,
                    footer: 'تحقق من اتصال الإنترنت وحاول مرة أخرى'
                });
            });
        }
    });

    return false; // منع إرسال النموذج
}

// دالة إظهار modal إضافة المنتجات من داخل modal التعديل
function showAddProductsForEdit() {
    const sessionId = document.getElementById('edit_session_id').value;
    if (!sessionId) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لم يتم العثور على معرف الجلسة'
        });
        return;
    }

    // إخفاء modal التعديل مؤقتاً
    const editModal = bootstrap.Modal.getInstance(document.getElementById('editSessionModal'));
    editModal.hide();

    // إظهار modal إضافة المنتجات
    setTimeout(() => {
        addProductsForEdit(sessionId);
    }, 300);
}

// دالة إضافة المنتجات للجلسة من modal التعديل
function addProductsForEdit(sessionId) {
    let currentPage = 1;
    let searchQuery = '';

    // إضافة مستمع لحدث البحث
    const searchInput = document.getElementById('searchProducts');
    searchInput.addEventListener('input', function(e) {
        searchQuery = e.target.value;
        loadProductsForEdit(1);
    });

    function loadProductsForEdit(page) {
        const productsList = document.getElementById('productsList');
        productsList.innerHTML = '<div class="text-center w-100"><div class="spinner-border text-primary" role="status"></div></div>';

        fetch(`api/get_products.php?page=${page}&search=${encodeURIComponent(searchQuery)}`)
            .then(response => response.json())
            .then(result => {
                if (!result.success) {
                    throw new Error(result.error || 'فشل في جلب المنتجات');
                }

                const { products, currentPage, totalPages } = result.data;

                if (products.length === 0) {
                    productsList.innerHTML = '<div class="col-12 text-center"><p>لا توجد منتجات متاحة</p></div>';
                    return;
                }

                // عرض المنتجات
                productsList.innerHTML = `
                    <div class="row g-3 mb-3">
                        ${products.map(product => `
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">${product.name}</h6>
                                        <p class="card-text text-primary fw-bold">${product.price_formatted || product.price} ج.م</p>
                                        <div class="d-flex align-items-center mb-2">
                                            <button class="btn btn-outline-secondary btn-sm" onclick="decrementQuantity('qty_edit_${product.id}')">-</button>
                                            <input type="number" id="qty_edit_${product.id}" class="form-control form-control-sm mx-2" value="1" min="1" style="width: 60px">
                                            <button class="btn btn-outline-secondary btn-sm" onclick="incrementQuantity('qty_edit_${product.id}')">+</button>
                                        </div>
                                        <button class="btn btn-primary btn-sm w-100"
                                                onclick="addProductToEditSession(${sessionId}, ${product.id}, document.getElementById('qty_edit_${product.id}').value)">
                                            <i class="fas fa-plus-circle me-1"></i>إضافة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                if (totalPages > 1) {
                    const paginationDiv = document.createElement('div');
                    paginationDiv.className = 'd-flex justify-content-center gap-2 mt-3';
                    paginationDiv.innerHTML = Array.from({ length: totalPages }, (_, i) => i + 1)
                        .map(pageNum => `
                            <button class="btn btn-${pageNum === currentPage ? 'primary' : 'outline-primary'} btn-sm"
                                    onclick="loadProductsForEdit(${pageNum})"
                                    ${pageNum === currentPage ? 'disabled' : ''}>
                                ${pageNum}
                            </button>
                        `).join('');
                    productsList.appendChild(paginationDiv);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                productsList.innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <p><i class="fas fa-exclamation-triangle me-2"></i>${error.message}</p>
                    </div>`;
            });
    }

    const modal = new bootstrap.Modal(document.getElementById('addProductsModal'));
    modal.show();

    // إضافة مستمع لإعادة فتح modal التعديل عند إغلاق modal المنتجات
    document.getElementById('addProductsModal').addEventListener('hidden.bs.modal', function() {
        setTimeout(() => {
            const editModal = new bootstrap.Modal(document.getElementById('editSessionModal'));
            editModal.show();
        }, 300);
    }, { once: true });

    window.loadProductsForEdit = loadProductsForEdit;
    loadProductsForEdit(currentPage);
}

// دالة إضافة منتج للجلسة من modal التعديل
function addProductToEditSession(sessionId, productId, quantity) {
    fetch('api/add_session_product.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId,
            product_id: productId,
            quantity: parseInt(quantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث قائمة المنتجات في modal التعديل فوراً
            loadEditSessionProducts(sessionId);

            // تحديث التكلفة الإجمالية في الجدول الرئيسي
            const costElement = document.querySelector(`.session-cost[data-session-id="${sessionId}"]`);
            if (costElement) {
                costElement.textContent = data.total_cost + ' ج.م';
            }

            // تحديث قائمة المنتجات في الجدول الرئيسي أيضاً
            updateSessionProducts(sessionId);

            Swal.fire({
                icon: 'success',
                title: 'تم إضافة المنتج بنجاح',
                text: `تمت إضافة ${quantity} ${data.product_name}`,
                showConfirmButton: false,
                timer: 1500
            });

            // إعادة تعيين قيمة الكمية
            const qtyInput = document.getElementById(`qty_edit_${productId}`);
            if (qtyInput) {
                qtyInput.value = 1;
            }
        } else {
            throw new Error(data.error || 'حدث خطأ أثناء إضافة المنتج');
        }
    })
    .catch(error => {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: error.message
        });
    });
}

// دوال إنهاء الجلسة مع المنتجات

// دالة إظهار modal إنهاء الجلسة
function showEndSessionModal(sessionId, isSessionId = false) {
    // جلب معلومات الجلسة
    fetch(`api/get_session_details.php?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const session = data.session;

                // ملء معلومات الجلسة
                document.getElementById('endSessionId').value = sessionId;
                document.getElementById('endSessionInfo').innerHTML = `
                    <div><strong>الجهاز:</strong> ${session.device_name} - ${session.room_name || 'غير محدد'}</div>
                    <div><strong>وقت البدء:</strong> ${new Date(session.start_time).toLocaleString('ar-EG')}</div>
                    <div><strong>المدة:</strong> ${session.duration_display}</div>
                    ${session.customer_name ? `<div><strong>العميل:</strong> ${session.customer_name}</div>` : ''}
                `;

                // تحميل المنتجات المتاحة
                loadEndSessionProducts();

                // إظهار modal
                const modal = new bootstrap.Modal(document.getElementById('endSessionModal'));
                modal.show();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: data.error || 'فشل في جلب بيانات الجلسة'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء جلب بيانات الجلسة'
            });
        });
}

// دالة تحميل المنتجات في modal إنهاء الجلسة
let selectedEndSessionProducts = [];

function loadEndSessionProducts(page = 1, searchQuery = '') {
    const productsList = document.getElementById('endProductsList');
    productsList.innerHTML = '<div class="text-center w-100"><div class="spinner-border text-primary" role="status"></div></div>';

    fetch(`api/get_products.php?page=${page}&search=${encodeURIComponent(searchQuery)}`)
        .then(response => response.json())
        .then(result => {
            if (!result.success) {
                throw new Error(result.error || 'فشل في جلب المنتجات');
            }

            const { products, currentPage, totalPages } = result.data;

            if (products.length === 0) {
                productsList.innerHTML = '<div class="col-12 text-center"><p>لا توجد منتجات متاحة</p></div>';
                return;
            }

            // عرض المنتجات
            productsList.innerHTML = `
                <div class="row g-3 mb-3">
                    ${products.map(product => `
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">${product.name}</h6>
                                    <p class="card-text text-primary fw-bold">${product.price_formatted || product.price} ج.م</p>
                                    <div class="d-flex align-items-center mb-2">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="decrementQuantity('qty_end_${product.id}')">-</button>
                                        <input type="number" id="qty_end_${product.id}" class="form-control form-control-sm mx-2" value="1" min="1" style="width: 60px">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="incrementQuantity('qty_end_${product.id}')">+</button>
                                    </div>
                                    <button class="btn btn-primary btn-sm w-100"
                                            onclick="addProductToEndSession(${product.id}, '${product.name}', ${product.price}, document.getElementById('qty_end_${product.id}').value)">
                                        <i class="fas fa-plus-circle me-1"></i>إضافة للفاتورة
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            if (totalPages > 1) {
                const paginationDiv = document.createElement('div');
                paginationDiv.className = 'd-flex justify-content-center gap-2 mt-3';
                paginationDiv.innerHTML = Array.from({ length: totalPages }, (_, i) => i + 1)
                    .map(pageNum => `
                        <button class="btn btn-${pageNum === currentPage ? 'primary' : 'outline-primary'} btn-sm"
                                onclick="loadEndSessionProducts(${pageNum}, '${searchQuery}')"
                                ${pageNum === currentPage ? 'disabled' : ''}>
                            ${pageNum}
                        </button>
                    `).join('');
                productsList.appendChild(paginationDiv);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            productsList.innerHTML = `
                <div class="col-12 text-center text-danger">
                    <p><i class="fas fa-exclamation-triangle me-2"></i>${error.message}</p>
                </div>`;
        });
}

// دالة إضافة منتج لقائمة المنتجات المختارة لإنهاء الجلسة
function addProductToEndSession(productId, productName, productPrice, quantity) {
    const qty = parseInt(quantity);
    const existingIndex = selectedEndSessionProducts.findIndex(p => p.id === productId);

    if (existingIndex >= 0) {
        // إذا كان المنتج موجود، زيادة الكمية
        selectedEndSessionProducts[existingIndex].quantity += qty;
    } else {
        // إضافة منتج جديد
        selectedEndSessionProducts.push({
            id: productId,
            name: productName,
            price: productPrice,
            quantity: qty
        });
    }

    // تحديث عرض المنتجات المختارة
    updateSelectedEndProducts();

    // إعادة تعيين قيمة الكمية
    const qtyInput = document.getElementById(`qty_end_${productId}`);
    if (qtyInput) {
        qtyInput.value = 1;
    }

    Swal.fire({
        icon: 'success',
        title: 'تم إضافة المنتج',
        text: `تمت إضافة ${qty} ${productName} للفاتورة`,
        showConfirmButton: false,
        timer: 1000
    });
}

// دالة تحديث عرض المنتجات المختارة
function updateSelectedEndProducts() {
    const container = document.getElementById('selectedEndProducts');

    if (selectedEndSessionProducts.length === 0) {
        container.innerHTML = '<p class="text-muted">لم يتم إضافة منتجات بعد</p>';
        return;
    }

    let totalCost = 0;
    const productsHtml = selectedEndSessionProducts.map(product => {
        const productTotal = product.quantity * product.price;
        totalCost += productTotal;

        return `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-0">${product.name}</h6>
                    <small class="text-muted">${product.quantity} × ${product.price} ج.م</small>
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-3">${productTotal.toFixed(2)} ج.م</span>
                    <button class="btn btn-danger btn-sm" onclick="removeProductFromEndSession(${product.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = `
        <div class="list-group">
            ${productsHtml}
        </div>
        <div class="mt-2 text-end">
            <strong>إجمالي تكلفة المنتجات: ${totalCost.toFixed(2)} ج.م</strong>
        </div>
    `;
}

// دالة حذف منتج من قائمة المنتجات المختارة
function removeProductFromEndSession(productId) {
    selectedEndSessionProducts = selectedEndSessionProducts.filter(p => p.id !== productId);
    updateSelectedEndProducts();
}

// دالة تأكيد إنهاء الجلسة
function confirmEndSession() {
    const sessionId = document.getElementById('endSessionId').value;

    if (confirm('هل أنت متأكد من إنهاء هذه الجلسة وإنشاء الفاتورة؟')) {
        // إظهار مؤشر التحميل
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإنهاء...';
        button.disabled = true;

        // إضافة المنتجات المختارة للجلسة أولاً
        if (selectedEndSessionProducts.length > 0) {
            addSelectedProductsToSession(sessionId)
                .then(() => {
                    // بعد إضافة المنتجات، إنهاء الجلسة
                    finishEndingSession(sessionId);
                })
                .catch(error => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: 'حدث خطأ أثناء إضافة المنتجات: ' + error.message
                    });
                });
        } else {
            // إنهاء الجلسة مباشرة بدون منتجات
            finishEndingSession(sessionId);
        }
    }
}

// دالة إضافة المنتجات المختارة للجلسة
function addSelectedProductsToSession(sessionId) {
    const promises = selectedEndSessionProducts.map(product => {
        return fetch('api/add_session_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                session_id: sessionId,
                product_id: product.id,
                quantity: product.quantity
            })
        }).then(response => response.json());
    });

    return Promise.all(promises);
}

// دالة إنهاء الجلسة النهائية
function finishEndingSession(sessionId) {
    window.location.href = `sessions.php?action=end&session_id=${sessionId}`;
}

// إعداد مستمع البحث في modal إنهاء الجلسة
document.addEventListener('DOMContentLoaded', function() {
    const searchEndInput = document.getElementById('searchEndProducts');
    if (searchEndInput) {
        searchEndInput.addEventListener('input', function(e) {
            const searchQuery = e.target.value;
            loadEndSessionProducts(1, searchQuery);
        });
    }

    // إعادة تعيين المنتجات المختارة عند إغلاق modal
    document.getElementById('endSessionModal').addEventListener('hidden.bs.modal', function() {
        selectedEndSessionProducts = [];
    });
});

// دالة تحديث التكلفة المتوقعة لإضافة الجلسة
function updateEstimatedCostAdd() {
    const deviceSelect = document.getElementById('device_id');
    const sessionTypeOpen = document.getElementById('session_type_open_add');
    const sessionTypeTimed = document.getElementById('session_type_timed_add');
    const durationSelect = document.getElementById('session_duration_add');
    const estimatedCostDiv = document.getElementById('estimated_cost_add');

    // جلب نوع اللعب المختار
    const gameTypeSingle = document.getElementById('game_type_single_add');
    const gameTypeMultiplayer = document.getElementById('game_type_multiplayer_add');

    if (!deviceSelect.value) {
        estimatedCostDiv.textContent = '0.00 ج.م';
        return;
    }

    const selectedOption = deviceSelect.options[deviceSelect.selectedIndex];
    const hourlyRate = parseFloat(selectedOption.dataset.hourlyRate) || 0;
    const singleRate = parseFloat(selectedOption.dataset.singleRate) || 0;
    const multiRate = parseFloat(selectedOption.dataset.multiRate) || 0;

    // تحديد السعر بناءً على نوع اللعب
    let selectedRate = singleRate || hourlyRate;
    if (gameTypeMultiplayer && gameTypeMultiplayer.checked) {
        selectedRate = multiRate || hourlyRate;
    }

    let cost = 0;

    if (sessionTypeTimed && sessionTypeTimed.checked) {
        const duration = parseFloat(durationSelect.value) || 1;
        cost = Math.ceil(duration) * selectedRate;
    } else {
        cost = selectedRate;
    }

    // عرض نوع اللعب والسعر
    const gameTypeText = (gameTypeMultiplayer && gameTypeMultiplayer.checked) ? 'زوجي' : 'فردي';
    estimatedCostDiv.innerHTML = `
        <div class="fw-bold text-primary">${cost.toFixed(2)} ج.م</div>
        <small class="text-muted">نوع اللعب: ${gameTypeText} (${selectedRate.toFixed(2)} ج.م/ساعة)</small>
    `;
}

// إعداد مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {

    // مستمعي أحداث نوع الجلسة
    const sessionTypeRadiosAdd = document.querySelectorAll('input[name="session_type"]');
    sessionTypeRadiosAdd.forEach(radio => {
        radio.addEventListener('change', function() {
            const durationSection = document.getElementById('session_duration_section_add');
            if (this.value === 'timed') {
                durationSection.style.display = 'block';
            } else {
                durationSection.style.display = 'none';
            }
            updateEstimatedCostAdd();
        });
    });

    // مستمعي أحداث نوع اللعب
    const gameTypeRadiosAdd = document.querySelectorAll('input[name="game_type"]');
    gameTypeRadiosAdd.forEach(radio => {
        radio.addEventListener('change', function() {
            updateEstimatedCostAdd();
        });
    });

    // مستمع تغيير الجهاز والمدة
    const deviceSelect = document.getElementById('device_id');
    const durationSelect = document.getElementById('session_duration_add');

    if (deviceSelect) {
        deviceSelect.addEventListener('change', updateEstimatedCostAdd);
    }

    if (durationSelect) {
        durationSelect.addEventListener('change', updateEstimatedCostAdd);
    }

    // مستمع إرسال نموذج تعديل الجلسة
    const editSessionForm = document.getElementById('editSessionForm');
    if (editSessionForm) {
        editSessionForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const sessionId = document.getElementById('edit_session_id').value;
            const customerId = document.getElementById('customer_id_select_edit').value;
            const notes = document.getElementById('notes_edit').value;
            const gameType = document.querySelector('input[name="game_type_edit"]:checked').value;

            fetch('api/update_session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    customer_id: customerId || null,
                    notes: notes,
                    game_type: gameType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم التحديث بنجاح',
                        text: 'تم حفظ التعديلات على الجلسة',
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    throw new Error(data.error || 'فشل في تحديث الجلسة');
                }
            })
            .catch(error => {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: error.message
                });
            });
        });
    }
});



// دالة تحديث قائمة المنتجات في الجدول الرئيسي
function updateSessionProducts(sessionId) {
    // تحديث عرض المنتجات في الجدول الرئيسي إذا لزم الأمر
    const sessionRow = document.querySelector(`tr[data-session-id="${sessionId}"]`);
    if (sessionRow) {
        // يمكن إضافة منطق تحديث إضافي هنا
        console.log('Updated session products for session:', sessionId);
    }
}

// التأكد من استدعاء الدالة عند فتح modal التعديل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع لحدث فتح modal التعديل
    const editModal = document.getElementById('editSessionModal');
    if (editModal) {
        editModal.addEventListener('shown.bs.modal', function() {
            const sessionId = document.getElementById('edit_session_id').value;
            if (sessionId) {
                console.log('Modal opened, loading products for session:', sessionId);
                loadEditSessionProducts(sessionId);
            }
        });
    }
});
</script>

<style>
/* تنسيق مخصص للكارت المصغر للجلسات النشطة */
.badge-sm {
    font-size: 0.65em;
    padding: 0.25em 0.5em;
}

.active-session-card {
    transition: all 0.2s ease;
    border: 1px solid #e9ecef;
}

.active-session-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #007bff;
}

.active-session-card .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef;
    padding: 0.5rem 0.75rem;
}

.active-session-card .card-body {
    padding: 0.75rem;
}

.active-session-card .card-footer {
    padding: 0.5rem 0.75rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.timer {
    font-family: 'Courier New', monospace;
    color: #28a745;
    font-weight: bold;
}

.session-cost {
    font-family: 'Courier New', monospace;
    color: #007bff;
    font-weight: bold;
}

/* تحسين المظهر على الشاشات الصغيرة */
@media (max-width: 768px) {
    .active-session-card {
        margin-bottom: 1rem;
    }

    .active-session-card .card-body {
        padding: 0.5rem;
    }

    .active-session-card .card-footer {
        padding: 0.5rem;
    }

    .active-session-card .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

/* تحسين مظهر الأزرار في الكارت المصغر */
.active-session-card .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 0.25rem;
}

.active-session-card .btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.active-session-card .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.active-session-card .btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.active-session-card .btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}
</style>

<script>
// دالة لتعيين نطاقات التاريخ السريعة
function setDateRange(range) {
    const today = new Date();
    const fromDateInput = document.getElementById('from_date');
    const toDateInput = document.getElementById('to_date');

    let fromDate, toDate;

    switch(range) {
        case 'today':
            fromDate = toDate = today;
            break;
        case 'yesterday':
            fromDate = toDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            break;
        case 'week':
            // بداية الأسبوع (الأحد)
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            fromDate = startOfWeek;
            toDate = today;
            break;
        case 'month':
            // بداية الشهر
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = today;
            break;
    }

    // تنسيق التاريخ بصيغة YYYY-MM-DD
    const formatDate = (date) => {
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    };

    fromDateInput.value = formatDate(fromDate);
    toDateInput.value = formatDate(toDate);
}

// تحديث التكلفة المتوقعة في modal إضافة الجلسة
document.addEventListener('DOMContentLoaded', function() {
    const deviceSelect = document.getElementById('device_id');
    const sessionTypeRadios = document.querySelectorAll('input[name="session_type"]');
    const gameTypeRadios = document.querySelectorAll('input[name="game_type"]');
    const durationSelect = document.getElementById('session_duration_add');
    const estimatedCostDiv = document.getElementById('estimated_cost_add');
    const durationSection = document.getElementById('session_duration_section_add');

    function updateEstimatedCost() {
        const selectedDevice = deviceSelect.options[deviceSelect.selectedIndex];
        const sessionType = document.querySelector('input[name="session_type"]:checked')?.value;
        const gameType = document.querySelector('input[name="game_type"]:checked')?.value;
        const duration = parseFloat(durationSelect.value) || 1;

        if (!selectedDevice || !selectedDevice.value) {
            estimatedCostDiv.textContent = '0.00 ج.م';
            return;
        }

        let hourlyRate = 0;
        if (gameType === 'multiplayer') {
            hourlyRate = parseFloat(selectedDevice.dataset.multiRate) || parseFloat(selectedDevice.dataset.hourlyRate) || 0;
        } else {
            hourlyRate = parseFloat(selectedDevice.dataset.singleRate) || parseFloat(selectedDevice.dataset.hourlyRate) || 0;
        }

        let estimatedCost = 0;
        if (sessionType === 'timed') {
            estimatedCost = hourlyRate * duration;
        } else {
            estimatedCost = hourlyRate; // تكلفة ساعة واحدة كتقدير
        }

        estimatedCostDiv.textContent = estimatedCost.toFixed(2) + ' ج.م';
    }

    // إظهار/إخفاء قسم المدة
    sessionTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'timed') {
                durationSection.style.display = 'block';
            } else {
                durationSection.style.display = 'none';
            }
            updateEstimatedCost();
        });
    });

    // تحديث التكلفة عند تغيير أي من الخيارات
    deviceSelect.addEventListener('change', updateEstimatedCost);
    gameTypeRadios.forEach(radio => {
        radio.addEventListener('change', updateEstimatedCost);
    });
    durationSelect.addEventListener('change', updateEstimatedCost);

    // تحديث أولي
    updateEstimatedCost();
});
</script>

<?php require_once 'includes/footer.php'; ?>
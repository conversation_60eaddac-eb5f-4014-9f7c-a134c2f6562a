/**
 * تصميم محسن للقائمة الجانبية
 * Enhanced Sidebar Design
 */

/* المتغيرات الأساسية */
:root {
    --sidebar-width: 280px;
    --sidebar-bg: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    --sidebar-text: #ecf0f1;
    --sidebar-text-muted: #bdc3c7;
    --sidebar-hover: rgba(52, 152, 219, 0.1);
    --sidebar-active: rgba(52, 152, 219, 0.2);
    --sidebar-accent: #3498db;
    --content-bg: #f8f9fa;
    --card-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* إعادة تعيين الأساسيات */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Sego<PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--content-bg);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* القائمة الجانبية الرئيسية */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    box-shadow: -2px 0 15px rgba(0,0,0,0.1);
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    transition: var(--transition);
}

/* ترويسة القائمة الجانبية */
.sidebar-header {
    padding: 25px 20px;
    background: rgba(0,0,0,0.15);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
    position: relative;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--sidebar-accent), transparent);
}

.sidebar-brand {
    color: white;
    text-decoration: none;
    font-size: 1.6rem;
    font-weight: 700;
    display: block;
    transition: var(--transition);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-brand:hover {
    color: var(--sidebar-accent);
    text-decoration: none;
    transform: scale(1.05);
}

.sidebar-brand i {
    margin-left: 10px;
    font-size: 1.4rem;
}

/* معلومات المستخدم */
.user-info {
    margin-top: 20px;
    padding: 18px;
    background: rgba(0,0,0,0.2);
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid rgba(255,255,255,0.1);
}

.user-info .user-name {
    color: white;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.user-info .user-role {
    color: var(--sidebar-text-muted);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.user-info .badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* محتوى القائمة */
.sidebar-nav {
    padding: 25px 0 20px;
}

.nav-section {
    margin-bottom: 35px;
}

.nav-section-title {
    color: var(--sidebar-text-muted);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    padding: 0 25px 12px;
    margin-bottom: 15px;
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 25px;
    left: 25px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

/* روابط التنقل */
.nav-link {
    display: flex;
    align-items: center;
    padding: 14px 25px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
    position: relative;
    margin: 2px 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 0;
    background: var(--sidebar-hover);
    transition: var(--transition);
    z-index: -1;
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link:hover {
    color: var(--sidebar-accent);
    border-right-color: var(--sidebar-accent);
    text-decoration: none;
    transform: translateX(-3px);
}

.nav-link.active {
    background: var(--sidebar-active);
    color: var(--sidebar-accent);
    border-right-color: var(--sidebar-accent);
    font-weight: 600;
}

.nav-link.active::before {
    width: 100%;
    background: var(--sidebar-active);
}

.nav-link i {
    width: 22px;
    margin-left: 15px;
    text-align: center;
    font-size: 1.1rem;
    transition: var(--transition);
}

.nav-link:hover i {
    transform: scale(1.1);
}

.nav-link span {
    flex: 1;
    font-size: 0.95rem;
}

.nav-link .badge {
    margin-right: auto;
    margin-left: 10px;
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* رابط تسجيل الخروج */
.nav-link.text-danger {
    color: #e74c3c !important;
    margin-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 20px;
}

.nav-link.text-danger:hover {
    background: rgba(231, 76, 60, 0.1);
    border-right-color: #e74c3c;
    color: #e74c3c !important;
}

/* المحتوى الرئيسي */
.main-content {
    margin-right: var(--sidebar-width);
    min-height: 100vh;
    padding: 30px;
    transition: var(--transition);
    background: var(--content-bg);
}

/* ترويسة المحتوى */
.content-header {
    background: white;
    padding: 25px 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
    border-right: 4px solid var(--sidebar-accent);
    position: relative;
    overflow: hidden;
}

.content-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--sidebar-accent), transparent);
}

.content-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.content-header h1 i {
    margin-left: 15px;
    color: var(--sidebar-accent);
}

.content-header .breadcrumb {
    margin: 15px 0 0;
    background: none;
    padding: 0;
    font-size: 0.9rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: #6c757d;
}

/* زر التبديل للموبايل */
.sidebar-toggle {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    background: var(--sidebar-accent);
    color: white;
    border: none;
    border-radius: 50%;
    width: 55px;
    height: 55px;
    font-size: 1.3rem;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: #2980b9;
    transform: scale(1.1);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 100%;
        box-shadow: none;
    }
    
    .sidebar.show {
        transform: translateX(0);
        box-shadow: 0 0 20px rgba(0,0,0,0.5);
    }
    
    .main-content {
        margin-right: 0;
        padding: 20px 15px;
    }
    
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .content-header {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .content-header h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px 10px;
    }
    
    .content-header {
        padding: 15px;
    }
    
    .sidebar-nav {
        padding: 20px 0;
    }
    
    .nav-section {
        margin-bottom: 25px;
    }
}

/* تحسينات عامة للمكونات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

/* تحسين الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(52, 152, 219, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
    transition: var(--transition);
}

/* تحسين النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: var(--transition);
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--sidebar-accent);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: scale(1.02);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تحسين التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

/* تحسين الشارات */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    transition: var(--transition);
}

.stats-card:hover::before {
    transform: rotate(45deg) scale(1.1);
}

.stats-card .card-body {
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--sidebar-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 9999;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين شريط التمرير */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.2);
    border-radius: 3px;
    transition: var(--transition);
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.3);
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.page-content > * {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .sidebar-toggle {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
        padding: 0 !important;
    }
    
    .content-header {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* تحسينات إضافية للتفاعل */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateY(-50%);
    transition: var(--transition);
}

.nav-link:hover::after {
    right: 100%;
}

/* تحسين الأيقونات */
.nav-link i {
    transition: var(--transition);
}

.nav-link:hover i {
    transform: scale(1.2) rotate(5deg);
}

.nav-link.active i {
    color: var(--sidebar-accent);
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

/* تحسين الإشعارات */
.notification-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* تحسينات للوضع المظلم (اختياري) */
@media (prefers-color-scheme: dark) {
    :root {
        --content-bg: #1a1a1a;
        --card-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }
    
    .content-header {
        background: #2d3748;
        color: white;
    }
    
    .content-header h1 {
        color: white;
    }
    
    .card {
        background: #2d3748;
        color: white;
    }
    
    .table {
        background: #2d3748;
        color: white;
    }
    
    .table th {
        background: #4a5568;
        color: white;
    }
}

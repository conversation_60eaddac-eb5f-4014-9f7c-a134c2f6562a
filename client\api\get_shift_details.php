<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$shift_id = $_GET['shift_id'] ?? 0;

if (!$shift_id) {
    echo json_encode(['success' => false, 'message' => 'معرف الشيفت مطلوب']);
    exit;
}

try {
    // جلب بيانات الشيفت
    $shift_stmt = $pdo->prepare("
        SELECT 
            es.*,
            e.name as employee_name,
            e.role as employee_role,
            e.phone as employee_phone
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        WHERE es.shift_id = ? AND es.client_id = ?
    ");
    $shift_stmt->execute([$shift_id, $client_id]);
    $shift = $shift_stmt->fetch();
    
    if (!$shift) {
        echo json_encode(['success' => false, 'message' => 'الشيفت غير موجود']);
        exit;
    }
    
    // جلب أنشطة الشيفت
    $activities_stmt = $pdo->prepare("
        SELECT 
            activity_type,
            activity_title,
            activity_description,
            target_type,
            target_id,
            activity_timestamp,
            activity_data
        FROM employee_shift_activities
        WHERE shift_id = ?
        ORDER BY activity_timestamp DESC
        LIMIT 50
    ");
    $activities_stmt->execute([$shift_id]);
    $activities = $activities_stmt->fetchAll();
    
    // حساب الإحصائيات
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_activities,
            COUNT(CASE WHEN activity_type = 'session_start' THEN 1 END) as sessions_started,
            COUNT(CASE WHEN activity_type = 'session_end' THEN 1 END) as sessions_ended,
            COUNT(CASE WHEN activity_type = 'customer_add' THEN 1 END) as customers_added,
            COUNT(CASE WHEN activity_type = 'customer_edit' THEN 1 END) as customers_edited,
            COUNT(CASE WHEN activity_type = 'cafeteria_order' THEN 1 END) as orders_created,
            COUNT(CASE WHEN activity_type = 'page_visit' THEN 1 END) as pages_visited
        FROM employee_shift_activities
        WHERE shift_id = ?
    ");
    $stats_stmt->execute([$shift_id]);
    $stats = $stats_stmt->fetch();
    
    // تجميع الأنشطة حسب النوع
    $activity_types_stmt = $pdo->prepare("
        SELECT 
            activity_type,
            COUNT(*) as count
        FROM employee_shift_activities
        WHERE shift_id = ?
        GROUP BY activity_type
        ORDER BY count DESC
    ");
    $activity_types_stmt->execute([$shift_id]);
    $activity_types = $activity_types_stmt->fetchAll();
    
    // حساب مدة الشيفت
    $duration_minutes = 0;
    $duration_text = '';
    
    if ($shift['shift_status'] == 'completed' && $shift['actual_duration_minutes']) {
        $duration_minutes = $shift['actual_duration_minutes'];
    } else {
        $duration_minutes = floor((strtotime('now') - strtotime($shift['shift_start_time'])) / 60);
    }
    
    $hours = floor($duration_minutes / 60);
    $minutes = $duration_minutes % 60;
    $duration_text = $hours . ' ساعة و ' . $minutes . ' دقيقة';
    
    // إنشاء HTML للعرض
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary">معلومات الشيفت</h6>
            <table class="table table-sm">
                <tr><td><strong>الموظف:</strong></td><td>' . htmlspecialchars($shift['employee_name']) . '</td></tr>
                <tr><td><strong>الدور:</strong></td><td>' . htmlspecialchars($shift['employee_role']) . '</td></tr>
                <tr><td><strong>تاريخ البدء:</strong></td><td>' . date('Y-m-d H:i', strtotime($shift['shift_start_time'])) . '</td></tr>';
    
    if ($shift['shift_end_time']) {
        $html .= '<tr><td><strong>تاريخ الانتهاء:</strong></td><td>' . date('Y-m-d H:i', strtotime($shift['shift_end_time'])) . '</td></tr>';
    }
    
    $html .= '
                <tr><td><strong>المدة:</strong></td><td>' . $duration_text . '</td></tr>
                <tr><td><strong>الحالة:</strong></td><td>';
    
    $status_labels = [
        'active' => '<span class="badge bg-success">نشط</span>',
        'completed' => '<span class="badge bg-primary">مكتمل</span>',
        'cancelled' => '<span class="badge bg-danger">ملغي</span>',
        'on_break' => '<span class="badge bg-warning">استراحة</span>'
    ];
    
    $html .= $status_labels[$shift['shift_status']] ?? $shift['shift_status'];
    $html .= '</td></tr>';
    
    if ($shift['location_info']) {
        $html .= '<tr><td><strong>الموقع:</strong></td><td>' . htmlspecialchars($shift['location_info']) . '</td></tr>';
    }
    
    $html .= '
            </table>
        </div>
        <div class="col-md-6">
            <h6 class="text-primary">إحصائيات الأنشطة</h6>
            <table class="table table-sm">
                <tr><td><strong>إجمالي الأنشطة:</strong></td><td><span class="badge bg-info">' . $stats['total_activities'] . '</span></td></tr>
                <tr><td><strong>الجلسات المبدوءة:</strong></td><td><span class="badge bg-success">' . $stats['sessions_started'] . '</span></td></tr>
                <tr><td><strong>الجلسات المنتهية:</strong></td><td><span class="badge bg-primary">' . $stats['sessions_ended'] . '</span></td></tr>
                <tr><td><strong>العملاء المضافون:</strong></td><td><span class="badge bg-warning">' . $stats['customers_added'] . '</span></td></tr>
                <tr><td><strong>العملاء المعدلون:</strong></td><td><span class="badge bg-secondary">' . $stats['customers_edited'] . '</span></td></tr>
                <tr><td><strong>طلبات الكافيتيريا:</strong></td><td><span class="badge bg-dark">' . $stats['orders_created'] . '</span></td></tr>
                <tr><td><strong>الصفحات المزارة:</strong></td><td><span class="badge bg-light text-dark">' . $stats['pages_visited'] . '</span></td></tr>
            </table>
        </div>
    </div>';
    
    if (!empty($activity_types)) {
        $html .= '
        <hr>
        <h6 class="text-primary">تفصيل الأنشطة حسب النوع</h6>
        <div class="row">';
        
        foreach ($activity_types as $type) {
            $type_labels = [
                'login' => 'تسجيل دخول',
                'logout' => 'تسجيل خروج',
                'session_start' => 'بدء جلسة',
                'session_end' => 'إنهاء جلسة',
                'session_update' => 'تحديث جلسة',
                'customer_add' => 'إضافة عميل',
                'customer_edit' => 'تعديل عميل',
                'cafeteria_order' => 'طلب كافيتيريا',
                'page_visit' => 'زيارة صفحة',
                'other' => 'أخرى'
            ];
            
            $label = $type_labels[$type['activity_type']] ?? $type['activity_type'];
            
            $html .= '
            <div class="col-md-4 mb-2">
                <div class="d-flex justify-content-between align-items-center border rounded p-2">
                    <span>' . $label . '</span>
                    <span class="badge bg-primary">' . $type['count'] . '</span>
                </div>
            </div>';
        }
        
        $html .= '</div>';
    }
    
    if (!empty($activities)) {
        $html .= '
        <hr>
        <h6 class="text-primary">آخر الأنشطة (أحدث 20 نشاط)</h6>
        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
            <table class="table table-sm table-striped">
                <thead class="table-light sticky-top">
                    <tr>
                        <th>الوقت</th>
                        <th>النوع</th>
                        <th>العنوان</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>';
        
        foreach (array_slice($activities, 0, 20) as $activity) {
            $type_labels = [
                'login' => '<span class="badge bg-success">دخول</span>',
                'logout' => '<span class="badge bg-danger">خروج</span>',
                'session_start' => '<span class="badge bg-primary">بدء جلسة</span>',
                'session_end' => '<span class="badge bg-secondary">إنهاء جلسة</span>',
                'session_update' => '<span class="badge bg-info">تحديث جلسة</span>',
                'customer_add' => '<span class="badge bg-warning">إضافة عميل</span>',
                'customer_edit' => '<span class="badge bg-light text-dark">تعديل عميل</span>',
                'cafeteria_order' => '<span class="badge bg-dark">طلب كافيتيريا</span>',
                'page_visit' => '<span class="badge bg-light text-muted">زيارة صفحة</span>',
                'other' => '<span class="badge bg-secondary">أخرى</span>'
            ];
            
            $type_badge = $type_labels[$activity['activity_type']] ?? '<span class="badge bg-secondary">' . $activity['activity_type'] . '</span>';
            
            $html .= '
                <tr>
                    <td><small>' . date('H:i:s', strtotime($activity['activity_timestamp'])) . '</small></td>
                    <td>' . $type_badge . '</td>
                    <td>' . htmlspecialchars($activity['activity_title']) . '</td>
                    <td><small>' . htmlspecialchars(substr($activity['activity_description'], 0, 50)) . (strlen($activity['activity_description']) > 50 ? '...' : '') . '</small></td>
                </tr>';
        }
        
        $html .= '
                </tbody>
            </table>
        </div>';
    }
    
    if ($shift['shift_notes']) {
        $html .= '
        <hr>
        <h6 class="text-primary">ملاحظات الشيفت</h6>
        <div class="alert alert-info">
            ' . nl2br(htmlspecialchars($shift['shift_notes'])) . '
        </div>';
    }
    
    echo json_encode([
        'success' => true,
        'html' => $html,
        'shift' => $shift,
        'stats' => $stats
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>

<?php
/**
 * صفحة اختبار التصميم الجديد مع القائمة الجانبية
 */

$page_title = "اختبار التصميم الجديد";
$page_icon = "fas fa-palette";
$active_page = "test_design";

$breadcrumb = [
    ['title' => 'لوحة التحكم', 'url' => 'dashboard.php'],
    ['title' => 'اختبار التصميم']
];

include 'includes/header_sidebar_only.php';
?>

<div class="row">
    <!-- إحصائيات سريعة -->
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-3"></i>
                <h3>150</h3>
                <p class="mb-0">إجمالي العملاء</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-gamepad fa-2x mb-3"></i>
                <h3>25</h3>
                <p class="mb-0">الأجهزة النشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-3"></i>
                <h3>1,250</h3>
                <p class="mb-0">ريال اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3"></i>
                <h3>45</h3>
                <p class="mb-0">ساعات اللعب</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- نموذج تجريبي -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit me-2"></i>نموذج تجريبي</h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label for="testInput" class="form-label">حقل تجريبي</label>
                        <input type="text" class="form-control" id="testInput" placeholder="أدخل نص تجريبي">
                    </div>
                    
                    <div class="mb-3">
                        <label for="testSelect" class="form-label">قائمة منسدلة</label>
                        <select class="form-select" id="testSelect">
                            <option selected>اختر خياراً</option>
                            <option value="1">الخيار الأول</option>
                            <option value="2">الخيار الثاني</option>
                            <option value="3">الخيار الثالث</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="testTextarea" class="form-label">منطقة نص</label>
                        <textarea class="form-control" id="testTextarea" rows="3" placeholder="أدخل ملاحظات"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-secondary me-md-2">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- جدول تجريبي -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>جدول تجريبي</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>الاسم</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>عميل تجريبي 1</td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>عميل تجريبي 2</td>
                                <td><span class="badge bg-warning">معلق</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>عميل تجريبي 3</td>
                                <td><span class="badge bg-secondary">غير نشط</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- تنبيهات تجريبية -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bell me-2"></i>تنبيهات تجريبية</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>نجح!</strong> تم حفظ البيانات بنجاح.
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> يرجى مراجعة البيانات المدخلة.
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>معلومة!</strong> يمكنك استخدام الاختصارات للتنقل السريع.
                </div>
                
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>خطأ!</strong> حدث خطأ في معالجة الطلب.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أزرار تجريبية -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-mouse-pointer me-2"></i>أزرار تجريبية</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة جديد
                    </button>
                    <button type="button" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>حفظ
                    </button>
                    <button type="button" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </button>
                    <button type="button" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف
                    </button>
                    <button type="button" class="btn btn-info">
                        <i class="fas fa-info me-2"></i>معلومات
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات التصميم -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات التصميم الجديد</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        القائمة الجانبية الثابتة
                        <span class="badge bg-success">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        تصميم متجاوب للموبايل
                        <span class="badge bg-success">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        تأثيرات CSS محسنة
                        <span class="badge bg-success">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        تنظيم أفضل للمحتوى
                        <span class="badge bg-success">✓</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        سهولة التنقل
                        <span class="badge bg-success">✓</span>
                    </li>
                </ul>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary" onclick="showNotification('تم اختبار الإشعارات!', 'success')">
                        <i class="fas fa-bell me-2"></i>اختبار الإشعارات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- اختبار الـ Modal -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-window-maximize me-2"></i>اختبار النوافذ المنبثقة</h5>
            </div>
            <div class="card-body text-center">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                    <i class="fas fa-external-link-alt me-2"></i>فتح نافذة تجريبية
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تجريبي -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-star me-2"></i>نافذة تجريبية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هذه نافذة تجريبية لاختبار التصميم الجديد مع القائمة الجانبية.</p>
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    التصميم الجديد يوفر تجربة أفضل وأكثر تنظيماً!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<script>
// اختبار JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل صفحة اختبار التصميم الجديد بنجاح!');
    
    // إضافة تأثيرات تفاعلية
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
});

// دالة اختبار الإشعارات
function showNotification(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>

<?php include 'includes/footer_sidebar_only.php'; ?>

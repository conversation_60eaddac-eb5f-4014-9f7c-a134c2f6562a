<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('shifts')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_shifts') && !employeeHasPermission('view_shifts')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];

$page_title = "إدارة الورديات";
$active_page = "shifts";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_shift':
                    $stmt = $pdo->prepare("INSERT INTO shifts (client_id, shift_name, shift_date, start_time, end_time, break_duration, max_employees, min_employees, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $client_id,
                        $_POST['shift_name'],
                        $_POST['shift_date'],
                        $_POST['start_time'],
                        $_POST['end_time'],
                        $_POST['break_duration'] ?? 30,
                        $_POST['max_employees'] ?? 1,
                        $_POST['min_employees'] ?? 1,
                        $_POST['notes'] ?? '',
                        $client_id
                    ]);
                    $_SESSION['success'] = "تم إنشاء الوردية بنجاح";
                    break;

                case 'update_shift':
                    $stmt = $pdo->prepare("UPDATE shifts SET shift_name = ?, shift_date = ?, start_time = ?, end_time = ?, break_duration = ?, max_employees = ?, min_employees = ?, notes = ? WHERE shift_id = ? AND client_id = ?");
                    $stmt->execute([
                        $_POST['shift_name'],
                        $_POST['shift_date'],
                        $_POST['start_time'],
                        $_POST['end_time'],
                        $_POST['break_duration'] ?? 30,
                        $_POST['max_employees'] ?? 1,
                        $_POST['min_employees'] ?? 1,
                        $_POST['notes'] ?? '',
                        $_POST['shift_id'],
                        $client_id
                    ]);
                    $_SESSION['success'] = "تم تحديث الوردية بنجاح";
                    break;

                case 'delete_shift':
                    $stmt = $pdo->prepare("DELETE FROM shifts WHERE shift_id = ? AND client_id = ?");
                    $stmt->execute([$_POST['shift_id'], $client_id]);
                    $_SESSION['success'] = "تم حذف الوردية بنجاح";
                    break;

                case 'assign_employee':
                    $stmt = $pdo->prepare("INSERT INTO employee_shift_assignments (shift_id, employee_id, role_in_shift, is_mandatory, assigned_by) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE role_in_shift = VALUES(role_in_shift), is_mandatory = VALUES(is_mandatory)");
                    $stmt->execute([
                        $_POST['shift_id'],
                        $_POST['employee_id'],
                        $_POST['role_in_shift'] ?? 'regular',
                        isset($_POST['is_mandatory']) ? 1 : 0,
                        $client_id
                    ]);
                    $_SESSION['success'] = "تم تخصيص الموظف للوردية بنجاح";
                    break;

                case 'remove_assignment':
                    $stmt = $pdo->prepare("DELETE FROM employee_shift_assignments WHERE assignment_id = ?");
                    $stmt->execute([$_POST['assignment_id']]);
                    $_SESSION['success'] = "تم إلغاء تخصيص الموظف من الوردية";
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: shifts.php');
    exit;
}

// جلب الورديات
$shifts_query = "
    SELECT s.*, st.template_name,
           COUNT(esa.assignment_id) as assigned_employees,
           COUNT(CASE WHEN esa.status = 'confirmed' THEN 1 END) as confirmed_employees
    FROM shifts s
    LEFT JOIN shift_templates st ON s.template_id = st.template_id
    LEFT JOIN employee_shift_assignments esa ON s.shift_id = esa.shift_id
    WHERE s.client_id = ?
    GROUP BY s.shift_id
    ORDER BY s.shift_date DESC, s.start_time ASC
";
$shifts = $pdo->prepare($shifts_query);
$shifts->execute([$client_id]);
$shifts_data = $shifts->fetchAll(PDO::FETCH_ASSOC);

// جلب الموظفين
$employees = $pdo->prepare("SELECT id, name, role FROM employees WHERE client_id = ? AND is_active = 1");
$employees->execute([$client_id]);
$employees_data = $employees->fetchAll(PDO::FETCH_ASSOC);

// جلب قوالب الورديات
$templates = $pdo->prepare("SELECT * FROM shift_templates WHERE client_id = ? AND is_active = 1");
$templates->execute([$client_id]);
$templates_data = $templates->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header_sidebar_only.php';
?>


        
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-clock me-2"></i><?php echo $page_title; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addShiftModal">
                        <i class="fas fa-plus me-1"></i>إضافة وردية جديدة
                    </button>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count($shifts_data); ?></h4>
                                    <p class="card-text">إجمالي الورديات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count(array_filter($shifts_data, function($s) { return $s['status'] == 'active'; })); ?></h4>
                                    <p class="card-text">الورديات النشطة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-play fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count(array_filter($shifts_data, function($s) { return $s['status'] == 'scheduled'; })); ?></h4>
                                    <p class="card-text">الورديات المجدولة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo array_sum(array_column($shifts_data, 'assigned_employees')); ?></h4>
                                    <p class="card-text">الموظفين المخصصين</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الورديات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">قائمة الورديات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الوردية</th>
                                    <th>التاريخ</th>
                                    <th>وقت البداية</th>
                                    <th>وقت النهاية</th>
                                    <th>الموظفين المخصصين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($shifts_data as $shift): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($shift['shift_name']); ?></strong>
                                        <?php if ($shift['template_name']): ?>
                                            <br><small class="text-muted">قالب: <?php echo htmlspecialchars($shift['template_name']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($shift['shift_date'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($shift['start_time'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($shift['end_time'])); ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $shift['assigned_employees']; ?></span>
                                        /
                                        <span class="badge bg-success"><?php echo $shift['confirmed_employees']; ?></span>
                                        <br><small class="text-muted">مخصص/مؤكد</small>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'scheduled' => 'bg-warning',
                                            'active' => 'bg-success',
                                            'completed' => 'bg-secondary',
                                            'cancelled' => 'bg-danger'
                                        ];
                                        $status_labels = [
                                            'scheduled' => 'مجدولة',
                                            'active' => 'نشطة',
                                            'completed' => 'مكتملة',
                                            'cancelled' => 'ملغية'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $status_classes[$shift['status']] ?? 'bg-secondary'; ?>">
                                            <?php echo $status_labels[$shift['status']] ?? $shift['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editShift(<?php echo $shift['shift_id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="manageEmployees(<?php echo $shift['shift_id']; ?>)">
                                                <i class="fas fa-users"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteShift(<?php echo $shift['shift_id']; ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج إضافة وردية جديدة -->
<div class="modal fade" id="addShiftModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة وردية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_shift">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shift_name" class="form-label">اسم الوردية</label>
                                <input type="text" class="form-control" id="shift_name" name="shift_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="shift_date" class="form-label">تاريخ الوردية</label>
                                <input type="date" class="form-control" id="shift_date" name="shift_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_time" class="form-label">وقت البداية</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_time" class="form-label">وقت النهاية</label>
                                <input type="time" class="form-control" id="end_time" name="end_time" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="break_duration" class="form-label">مدة الاستراحة (دقيقة)</label>
                                <input type="number" class="form-control" id="break_duration" name="break_duration" value="30" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min_employees" class="form-label">الحد الأدنى للموظفين</label>
                                <input type="number" class="form-control" id="min_employees" name="min_employees" value="1" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_employees" class="form-label">الحد الأقصى للموظفين</label>
                                <input type="number" class="form-control" id="max_employees" name="max_employees" value="1" min="1">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الوردية</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل الوردية -->
<div class="modal fade" id="editShiftModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الوردية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editShiftForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_shift">
                    <input type="hidden" name="shift_id" id="edit_shift_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_shift_name" class="form-label">اسم الوردية</label>
                                <input type="text" class="form-control" id="edit_shift_name" name="shift_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_shift_date" class="form-label">تاريخ الوردية</label>
                                <input type="date" class="form-control" id="edit_shift_date" name="shift_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_start_time" class="form-label">وقت البداية</label>
                                <input type="time" class="form-control" id="edit_start_time" name="start_time" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_end_time" class="form-label">وقت النهاية</label>
                                <input type="time" class="form-control" id="edit_end_time" name="end_time" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_break_duration" class="form-label">مدة الاستراحة (دقيقة)</label>
                                <input type="number" class="form-control" id="edit_break_duration" name="break_duration" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_min_employees" class="form-label">الحد الأدنى للموظفين</label>
                                <input type="number" class="form-control" id="edit_min_employees" name="min_employees" min="1">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_max_employees" class="form-label">الحد الأقصى للموظفين</label>
                                <input type="number" class="form-control" id="edit_max_employees" name="max_employees" min="1">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج إدارة الموظفين -->
<div class="modal fade" id="manageEmployeesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة موظفي الوردية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="employeeManagementContent">
                    <!-- سيتم تحميل المحتوى عبر AJAX -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات الورديات للجافا سكريبت
const shiftsData = <?php echo json_encode($shifts_data, JSON_UNESCAPED_UNICODE); ?>;
const employeesData = <?php echo json_encode($employees_data, JSON_UNESCAPED_UNICODE); ?>;

// تعديل الوردية
function editShift(shiftId) {
    const shift = shiftsData.find(s => s.shift_id == shiftId);
    if (!shift) return;

    document.getElementById('edit_shift_id').value = shift.shift_id;
    document.getElementById('edit_shift_name').value = shift.shift_name;
    document.getElementById('edit_shift_date').value = shift.shift_date;
    document.getElementById('edit_start_time').value = shift.start_time;
    document.getElementById('edit_end_time').value = shift.end_time;
    document.getElementById('edit_break_duration').value = shift.break_duration;
    document.getElementById('edit_min_employees').value = shift.min_employees;
    document.getElementById('edit_max_employees').value = shift.max_employees;
    document.getElementById('edit_notes').value = shift.notes || '';

    new bootstrap.Modal(document.getElementById('editShiftModal')).show();
}

// حذف الوردية
function deleteShift(shiftId) {
    if (confirm('هل أنت متأكد من حذف هذه الوردية؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_shift">
            <input type="hidden" name="shift_id" value="${shiftId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// إدارة الموظفين
function manageEmployees(shiftId) {
    // تحميل محتوى إدارة الموظفين عبر AJAX
    fetch('api/get_shift_employees.php?shift_id=' + shiftId)
        .then(response => response.text())
        .then(html => {
            document.getElementById('employeeManagementContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('manageEmployeesModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحميل بيانات الموظفين');
        });
}

// تعيين التاريخ الافتراضي لليوم الحالي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('shift_date').value = today;
});
</script>

<?php include 'includes/footer_sidebar_only.php'; ?>

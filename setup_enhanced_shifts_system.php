<?php
/**
 * تشغيل النظام المحسن لإدارة الشيفتات - PlayGood
 * يقوم بتطبيق جميع التحسينات والميزات الجديدة
 */

require_once 'config/database.php';

// بدء العملية
echo "<h1>🚀 تشغيل النظام المحسن لإدارة الشيفتات - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px;'>";

try {
    // قراءة ملف SQL المحسن
    $sql_file = 'enhance_shifts_logging_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<h2>📋 خطوات التشغيل:</h2>";
    
    // تقسيم الاستعلامات مع مراعاة الإجراءات المخزنة والمشغلات
    $queries = parseSQL($sql_content);
    $success_count = 0;
    $error_count = 0;

    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

    foreach ($queries as $index => $query) {
        $query = trim($query);

        // تجاهل الاستعلامات الفارغة والتعليقات
        if (empty($query) || strpos($query, '--') === 0 || strpos($query, '/*') === 0) {
            continue;
        }

        try {
            $pdo->exec($query);
            $success_count++;
            
            // تحديد نوع العملية
            if (stripos($query, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $query, $matches);
                $table_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>$table_name</strong></p>";
            } elseif (stripos($query, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE.*?`?(\w+)`?/i', $query, $matches);
                $table_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: blue;'>🔧 تم تعديل جدول: <strong>$table_name</strong></p>";
            } elseif (stripos($query, 'CREATE OR REPLACE VIEW') !== false) {
                preg_match('/CREATE OR REPLACE VIEW.*?`?(\w+)`?/i', $query, $matches);
                $view_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: purple;'>👁️ تم إنشاء عرض: <strong>$view_name</strong></p>";
            } elseif (stripos($query, 'CREATE OR REPLACE PROCEDURE') !== false) {
                preg_match('/CREATE OR REPLACE PROCEDURE.*?`?(\w+)`?/i', $query, $matches);
                $proc_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: orange;'>⚙️ تم إنشاء إجراء: <strong>$proc_name</strong></p>";
            } elseif (stripos($query, 'CREATE OR REPLACE TRIGGER') !== false) {
                preg_match('/CREATE OR REPLACE TRIGGER.*?`?(\w+)`?/i', $query, $matches);
                $trigger_name = $matches[1] ?? 'غير محدد';
                echo "<p style='color: red;'>🔥 تم إنشاء مشغل: <strong>$trigger_name</strong></p>";
            } elseif (stripos($query, 'INSERT') !== false) {
                echo "<p style='color: teal;'>📝 تم إدراج بيانات</p>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>❌ خطأ في الاستعلام " . ($index + 1) . ": " . $e->getMessage() . "</p>";
        }
    }
    
    echo "</div>";
    
    echo "<h2>📊 نتائج التشغيل:</h2>";
    echo "<div style='background: " . ($error_count > 0 ? '#fff3cd' : '#d1edff') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ العمليات الناجحة:</strong> $success_count</p>";
    echo "<p><strong>❌ العمليات الفاشلة:</strong> $error_count</p>";
    echo "</div>";
    
    if ($error_count == 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم تشغيل النظام المحسن بنجاح!</h3>";
        echo "<p>تم إضافة الميزات التالية:</p>";
        echo "<ul>";
        echo "<li>✨ نظام تسجيل أحداث الشيفت المفصل</li>";
        echo "<li>📊 نظام إنشاء التقارير التلقائية</li>";
        echo "<li>🔔 نظام الإشعارات للإدمن</li>";
        echo "<li>📈 نظام تقييم أداء الموظفين</li>";
        echo "<li>📋 نظام إدارة مهام الشيفت</li>";
        echo "<li>🎯 نظام الملخصات الذكية</li>";
        echo "</ul>";
        echo "<p>يمكنك الآن استخدام النظام من خلال:</p>";
        echo "<ul>";
        echo "<li><a href='client/shifts.php' target='_blank'>صفحة إدارة الورديات</a></li>";
        echo "<li><a href='client/attendance.php' target='_blank'>صفحة الحضور والانصراف</a></li>";
        echo "<li><a href='client/shift_events.php' target='_blank'>صفحة أحداث الشيفت</a></li>";
        echo "<li><a href='client/shift_summaries.php' target='_blank'>صفحة ملخصات الشيفت</a></li>";
        echo "<li><a href='client/notifications.php' target='_blank'>صفحة الإشعارات</a></li>";
        echo "<li><a href='client/shift_reports.php' target='_blank'>صفحة تقارير الورديات</a></li>";
        echo "</ul>";
        echo "</div>";
        
        // التحقق من البيانات
        echo "<h2>🔍 التحقق من البيانات:</h2>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        
        // عدد الجداول المنشأة
        $tables = [
            'shift_events' => 'أحداث الشيفت',
            'shift_summaries' => 'ملخصات الشيفت', 
            'shift_employee_performance' => 'تقييم أداء الموظفين',
            'shift_tasks' => 'مهام الشيفت',
            'admin_notifications' => 'إشعارات الإدمن'
        ];
        
        foreach ($tables as $table => $description) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "<p>📋 جدول <strong>$description ($table)</strong>: $count سجل</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ جدول <strong>$description ($table)</strong>: غير موجود</p>";
            }
        }
        
        // عدد الصلاحيات المضافة
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM permissions WHERE category = 'shifts'");
            $permissions_count = $stmt->fetchColumn();
            echo "<p>🔐 صلاحيات الورديات: $permissions_count صلاحية</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على صلاحيات الورديات</p>";
        }
        
        // عدد الصفحات المضافة
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM pages WHERE category IN ('shifts', 'system')");
            $pages_count = $stmt->fetchColumn();
            echo "<p>📄 صفحات النظام: $pages_count صفحة</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على صفحات النظام</p>";
        }
        
        // فحص الـ Views
        try {
            $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_station LIKE '%shift%'");
            $views = $stmt->fetchAll();
            echo "<p>👁️ العروض (Views): " . count($views) . " عرض</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على العروض</p>";
        }
        
        // فحص الـ Stored Procedures
        try {
            $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name LIKE '%Shift%'");
            $procedures = $stmt->fetchAll();
            echo "<p>⚙️ الإجراءات المخزنة: " . count($procedures) . " إجراء</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على الإجراءات المخزنة</p>";
        }
        
        // فحص الـ Triggers
        try {
            $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` LIKE '%shift%'");
            $triggers = $stmt->fetchAll();
            echo "<p>🔥 المشغلات (Triggers): " . count($triggers) . " مشغل</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ لم يتم العثور على المشغلات</p>";
        }
        
        echo "</div>";
        
        // نصائح الاستخدام
        echo "<h2>💡 نصائح الاستخدام:</h2>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>للبدء في استخدام النظام المحسن:</h4>";
        echo "<ol>";
        echo "<li><strong>إنشاء شيفت جديد:</strong> انتقل إلى صفحة الورديات وأنشئ شيفت جديد</li>";
        echo "<li><strong>تخصيص الموظفين:</strong> خصص الموظفين للشيفت وحدد أدوارهم</li>";
        echo "<li><strong>بدء الشيفت:</strong> سجل بداية الشيفت وحضور الموظفين</li>";
        echo "<li><strong>تسجيل الأحداث:</strong> استخدم صفحة أحداث الشيفت لتسجيل أي أحداث مهمة</li>";
        echo "<li><strong>إنهاء الشيفت:</strong> عند الانتهاء، غير حالة الشيفت إلى 'مكتمل'</li>";
        echo "<li><strong>إنشاء الملخص:</strong> انتقل إلى صفحة ملخصات الشيفت وأنشئ ملخص تلقائي</li>";
        echo "<li><strong>مراجعة الإشعارات:</strong> تحقق من صفحة الإشعارات للحصول على التحديثات</li>";
        echo "</ol>";
        echo "</div>";
        
        // معلومات إضافية
        echo "<h2>📚 معلومات إضافية:</h2>";
        echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>الميزات الجديدة:</h4>";
        echo "<ul>";
        echo "<li><strong>تسجيل الأحداث التلقائي:</strong> يتم تسجيل أحداث الحضور والانصراف تلقائياً</li>";
        echo "<li><strong>الإشعارات الذكية:</strong> إشعارات تلقائية عند اكتمال الشيفتات والأحداث الحرجة</li>";
        echo "<li><strong>التقارير المفصلة:</strong> تقارير شاملة مع إمكانية التصدير</li>";
        echo "<li><strong>تقييم الأداء:</strong> نظام تقييم أداء الموظفين في كل شيفت</li>";
        echo "<li><strong>إدارة المهام:</strong> تخصيص وتتبع مهام الشيفت</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ تحذير: حدثت بعض الأخطاء</h3>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام.</p>";
        echo "<p>يمكنك إعادة تشغيل هذا الملف بعد إصلاح المشاكل.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في التشغيل</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>وجود ملف enhance_shifts_logging_system.sql</li>";
    echo "<li>صحة اتصال قاعدة البيانات</li>";
    echo "<li>صلاحيات قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

/**
 * دالة لتحليل ملف SQL مع مراعاة الإجراءات المخزنة والمشغلات
 */
function parseSQL($sql_content) {
    $queries = [];
    $current_query = '';
    $in_procedure = false;
    $in_trigger = false;
    $delimiter = ';';

    // تقسيم المحتوى إلى أسطر
    $lines = explode("\n", $sql_content);

    foreach ($lines as $line) {
        $line = trim($line);

        // تجاهل التعليقات والأسطر الفارغة
        if (empty($line) || strpos($line, '--') === 0 || strpos($line, '/*') === 0) {
            continue;
        }

        // التحقق من تغيير المحدد
        if (stripos($line, 'DELIMITER') === 0) {
            $parts = explode(' ', $line);
            if (isset($parts[1])) {
                $delimiter = trim($parts[1]);
            }
            continue;
        }

        // التحقق من بداية إجراء مخزن أو مشغل
        if (stripos($line, 'CREATE OR REPLACE PROCEDURE') !== false ||
            stripos($line, 'CREATE PROCEDURE') !== false) {
            $in_procedure = true;
        } elseif (stripos($line, 'CREATE OR REPLACE TRIGGER') !== false ||
                  stripos($line, 'CREATE TRIGGER') !== false) {
            $in_trigger = true;
        }

        $current_query .= $line . "\n";

        // التحقق من نهاية الاستعلام
        if (($in_procedure || $in_trigger) && $delimiter !== ';') {
            // في حالة الإجراءات والمشغلات، ننتظر المحدد المخصص
            if (strpos($line, $delimiter) !== false) {
                $current_query = str_replace($delimiter, '', $current_query);
                $queries[] = trim($current_query);
                $current_query = '';
                $in_procedure = false;
                $in_trigger = false;
                $delimiter = ';'; // إعادة تعيين المحدد
            }
        } elseif (!$in_procedure && !$in_trigger && strpos($line, ';') !== false) {
            // استعلام عادي
            $queries[] = trim($current_query);
            $current_query = '';
        }
    }

    // إضافة آخر استعلام إذا لم ينته بفاصلة منقوطة
    if (!empty(trim($current_query))) {
        $queries[] = trim($current_query);
    }

    return $queries;
}

// إضافة CSS للتنسيق
echo "<style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    h1 { color: #2c3e50; text-align: center; }
    h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
    h3 { color: #27ae60; }
    h4 { color: #8e44ad; }
    a { color: #3498db; text-decoration: none; }
    a:hover { text-decoration: underline; }
    ul, ol { line-height: 1.6; }
    p { line-height: 1.5; }
</style>";
?>

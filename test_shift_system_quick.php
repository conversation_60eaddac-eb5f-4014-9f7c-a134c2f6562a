<?php
/**
 * اختبار سريع لنظام الشيفت
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار سريع لنظام الشيفت</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { text-align: center; color: #333; }
        .test-item { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🧪 اختبار سريع لنظام الشيفت</h1>";

$tests_passed = 0;
$total_tests = 0;

// اختبار 1: التحقق من وجود الجداول
echo "<div class='test-item'>";
echo "<h3>اختبار 1: التحقق من وجود الجداول</h3>";
$total_tests++;

$required_tables = [
    'employee_shifts',
    'employee_shift_activities', 
    'employee_shift_summaries',
    'shift_system_settings',
    'shift_notifications',
    'shift_system_errors'
];

$all_tables_exist = true;
foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ الجدول $table موجود</div>";
        } else {
            echo "<div class='error'>❌ الجدول $table غير موجود</div>";
            $all_tables_exist = false;
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في فحص الجدول $table: " . $e->getMessage() . "</div>";
        $all_tables_exist = false;
    }
}

if ($all_tables_exist) {
    $tests_passed++;
    echo "<div class='success'>✅ جميع الجداول المطلوبة موجودة</div>";
} else {
    echo "<div class='error'>❌ بعض الجداول مفقودة</div>";
}
echo "</div>";

// اختبار 2: التحقق من Views
echo "<div class='test-item'>";
echo "<h3>اختبار 2: التحقق من Views</h3>";
$total_tests++;

try {
    $pdo->query("SELECT * FROM active_employee_shifts LIMIT 1");
    echo "<div class='success'>✅ view active_employee_shifts يعمل</div>";
    
    $pdo->query("SELECT * FROM shift_quick_stats LIMIT 1");
    echo "<div class='success'>✅ view shift_quick_stats يعمل</div>";
    
    $tests_passed++;
    echo "<div class='success'>✅ جميع Views تعمل بشكل صحيح</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في Views: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 3: التحقق من الموظفين
echo "<div class='test-item'>";
echo "<h3>اختبار 3: التحقق من الموظفين</h3>";
$total_tests++;

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM employees WHERE is_active = 1");
    $employee_count = $stmt->fetch()['count'];
    
    if ($employee_count > 0) {
        echo "<div class='success'>✅ يوجد $employee_count موظف نشط</div>";
        $tests_passed++;
    } else {
        echo "<div class='error'>❌ لا يوجد موظفين نشطين</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في فحص الموظفين: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 4: اختبار إدراج شيفت تجريبي
echo "<div class='test-item'>";
echo "<h3>اختبار 4: اختبار إدراج شيفت تجريبي</h3>";
$total_tests++;

try {
    $employee_stmt = $pdo->query("SELECT id, client_id FROM employees WHERE is_active = 1 LIMIT 1");
    $employee = $employee_stmt->fetch();
    
    if ($employee) {
        // إدراج شيفت تجريبي
        $stmt = $pdo->prepare("
            INSERT INTO employee_shifts (employee_id, client_id, shift_status, location_info) 
            VALUES (?, ?, 'active', 'اختبار سريع')
        ");
        $stmt->execute([$employee['id'], $employee['client_id']]);
        $test_shift_id = $pdo->lastInsertId();
        
        echo "<div class='success'>✅ تم إنشاء شيفت تجريبي #$test_shift_id</div>";
        
        // إدراج نشاط تجريبي
        $stmt = $pdo->prepare("
            INSERT INTO employee_shift_activities (
                shift_id, employee_id, client_id, activity_type,
                activity_title, activity_description
            ) VALUES (?, ?, ?, 'other', 'اختبار سريع', 'نشاط تجريبي للاختبار')
        ");
        $stmt->execute([$test_shift_id, $employee['id'], $employee['client_id']]);
        
        echo "<div class='success'>✅ تم تسجيل نشاط تجريبي</div>";
        
        // حذف البيانات التجريبية
        $pdo->prepare("DELETE FROM employee_shift_activities WHERE shift_id = ?")->execute([$test_shift_id]);
        $pdo->prepare("DELETE FROM employee_shifts WHERE shift_id = ?")->execute([$test_shift_id]);
        
        echo "<div class='info'>ℹ️ تم حذف البيانات التجريبية</div>";
        
        $tests_passed++;
        echo "<div class='success'>✅ نظام إدراج الشيفت والأنشطة يعمل بشكل صحيح</div>";
    } else {
        echo "<div class='error'>❌ لا يوجد موظفين لاختبار النظام</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في اختبار الإدراج: " . $e->getMessage() . "</div>";
}
echo "</div>";

// اختبار 5: التحقق من الصفحات
echo "<div class='test-item'>";
echo "<h3>اختبار 5: التحقق من وجود الصفحات</h3>";
$total_tests++;

$required_files = [
    'client/employee_shift_start.php',
    'client/employee_shift_reports.php',
    'client/test_shift_system.php',
    'client/includes/shift_activity_tracker.php',
    'client/assets/js/shift_tracker.js',
    'client/api/log_shift_activity.php',
    'client/api/get_shift_details.php',
    'client/api/generate_shift_report.php'
];

$all_files_exist = true;
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ الملف $file موجود</div>";
    } else {
        echo "<div class='error'>❌ الملف $file غير موجود</div>";
        $all_files_exist = false;
    }
}

if ($all_files_exist) {
    $tests_passed++;
    echo "<div class='success'>✅ جميع الملفات المطلوبة موجودة</div>";
} else {
    echo "<div class='error'>❌ بعض الملفات مفقودة</div>";
}
echo "</div>";

// النتيجة النهائية
echo "<div class='test-item' style='text-align: center; margin-top: 30px;'>";
echo "<h2>النتيجة النهائية</h2>";

$success_rate = ($tests_passed / $total_tests) * 100;

if ($success_rate == 100) {
    echo "<div class='success' style='font-size: 18px;'>
        🎉 <strong>ممتاز! جميع الاختبارات نجحت ($tests_passed/$total_tests)</strong><br>
        نظام الشيفت جاهز للاستخدام بالكامل!
    </div>";
} elseif ($success_rate >= 80) {
    echo "<div class='info' style='font-size: 18px;'>
        ✅ <strong>جيد! معظم الاختبارات نجحت ($tests_passed/$total_tests)</strong><br>
        النظام يعمل مع بعض المشاكل البسيطة.
    </div>";
} else {
    echo "<div class='error' style='font-size: 18px;'>
        ❌ <strong>يحتاج إصلاح! ($tests_passed/$total_tests) اختبارات نجحت فقط</strong><br>
        يرجى مراجعة الأخطاء أعلاه.
    </div>";
}

echo "<div style='margin-top: 20px;'>
    <h3>روابط مفيدة:</h3>
    <ul>
        <li><a href='client/employee_shift_start.php'>صفحة إدارة الشيفت</a></li>
        <li><a href='client/employee_shift_reports.php'>صفحة تقارير الشيفت</a></li>
        <li><a href='client/test_shift_system.php'>صفحة اختبار شاملة</a></li>
        <li><a href='client/dashboard.php'>لوحة التحكم الرئيسية</a></li>
    </ul>
</div>";

echo "</div>";

echo "</div></body></html>";
?>

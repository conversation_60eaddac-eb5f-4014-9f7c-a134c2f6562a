<?php
/**
 * إصلاح أخطاء تنفيذ SQL - PlayGood
 * يقوم بتنفيذ ملفات SQL بشكل صحيح مع مراعاة الإجراءات المخزنة والمشغلات
 */

require_once 'config/database.php';

echo "<h1>🔧 إصلاح أخطاء تنفيذ SQL - PlayGood</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px;'>";

try {
    echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
    
    if (!$pdo) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    echo "<h2>2. تنفيذ ملف SQL المحسن</h2>";
    
    $sql_file = 'enhance_shifts_logging_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    // قراءة محتوى الملف
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<p style='color: green;'>✅ تم قراءة ملف SQL بنجاح</p>";
    
    // تنفيذ الملف باستخدام multi_query
    echo "<h3>تنفيذ الاستعلامات:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // تنظيف المحتوى من التعليقات غير الضرورية
    $sql_content = preg_replace('/^--.*$/m', '', $sql_content);
    $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);
    
    // تقسيم الاستعلامات بناءً على DELIMITER
    $statements = explode('DELIMITER', $sql_content);
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        
        if (empty($statement)) {
            continue;
        }
        
        // إزالة المحددات المخصصة
        $statement = str_replace('//', ';', $statement);
        $statement = str_replace('//;', ';', $statement);
        
        // تقسيم الاستعلامات العادية
        if (strpos($statement, 'CREATE OR REPLACE PROCEDURE') === false && 
            strpos($statement, 'CREATE OR REPLACE TRIGGER') === false) {
            $queries = explode(';', $statement);
        } else {
            $queries = [$statement];
        }
        
        foreach ($queries as $query) {
            $query = trim($query);
            
            if (empty($query) || strlen($query) < 10) {
                continue;
            }
            
            try {
                $pdo->exec($query);
                $success_count++;
                
                // تحديد نوع العملية
                if (stripos($query, 'CREATE TABLE') !== false) {
                    preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $query, $matches);
                    $table_name = $matches[1] ?? 'غير محدد';
                    echo "<p style='color: green;'>✅ تم إنشاء جدول: <strong>$table_name</strong></p>";
                } elseif (stripos($query, 'ALTER TABLE') !== false) {
                    preg_match('/ALTER TABLE.*?`?(\w+)`?/i', $query, $matches);
                    $table_name = $matches[1] ?? 'غير محدد';
                    echo "<p style='color: blue;'>🔧 تم تعديل جدول: <strong>$table_name</strong></p>";
                } elseif (stripos($query, 'CREATE OR REPLACE VIEW') !== false) {
                    preg_match('/CREATE OR REPLACE VIEW.*?`?(\w+)`?/i', $query, $matches);
                    $view_name = $matches[1] ?? 'غير محدد';
                    echo "<p style='color: purple;'>👁️ تم إنشاء عرض: <strong>$view_name</strong></p>";
                } elseif (stripos($query, 'CREATE OR REPLACE PROCEDURE') !== false) {
                    preg_match('/CREATE OR REPLACE PROCEDURE.*?`?(\w+)`?/i', $query, $matches);
                    $proc_name = $matches[1] ?? 'غير محدد';
                    echo "<p style='color: orange;'>⚙️ تم إنشاء إجراء: <strong>$proc_name</strong></p>";
                } elseif (stripos($query, 'CREATE OR REPLACE TRIGGER') !== false) {
                    preg_match('/CREATE OR REPLACE TRIGGER.*?`?(\w+)`?/i', $query, $matches);
                    $trigger_name = $matches[1] ?? 'غير محدد';
                    echo "<p style='color: red;'>🔥 تم إنشاء مشغل: <strong>$trigger_name</strong></p>";
                } elseif (stripos($query, 'INSERT') !== false) {
                    echo "<p style='color: teal;'>📝 تم إدراج بيانات</p>";
                }
                
            } catch (PDOException $e) {
                $error_count++;
                echo "<p style='color: red;'>❌ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
                echo "<details style='margin: 10px 0;'>";
                echo "<summary style='cursor: pointer; color: #666;'>عرض الاستعلام</summary>";
                echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px;'>" . htmlspecialchars(substr($query, 0, 500)) . "</pre>";
                echo "</details>";
            }
        }
    }
    
    echo "</div>";
    
    echo "<h2>📊 نتائج التنفيذ:</h2>";
    echo "<div style='background: " . ($error_count > 0 ? '#fff3cd' : '#d1edff') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>الاستعلامات المنفذة بنجاح:</strong> $success_count</p>";
    echo "<p><strong>الأخطاء:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 تم تنفيذ جميع الاستعلامات بنجاح!</p>";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ تم التنفيذ مع بعض الأخطاء</p>";
    }
    echo "</div>";
    
    // فحص الجداول المنشأة
    echo "<h2>3. فحص الجداول المنشأة</h2>";
    
    $tables_to_check = [
        'shift_events',
        'shift_summaries', 
        'shift_employee_performance',
        'shift_tasks',
        'admin_notifications'
    ];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ جدول <strong>$table</strong> موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ جدول <strong>$table</strong> غير موجود</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في فحص جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    // فحص الإجراءات المخزنة
    echo "<h2>4. فحص الإجراءات المخزنة</h2>";
    
    $procedures_to_check = [
        'GenerateShiftSummary'
    ];
    
    foreach ($procedures_to_check as $procedure) {
        try {
            $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '$procedure'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ إجراء <strong>$procedure</strong> موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ إجراء <strong>$procedure</strong> غير موجود</p>";
            }
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ خطأ في فحص إجراء $procedure: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>✅ تم الانتهاء من الإصلاح</h2>";
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li>تحقق من أن جميع الجداول والإجراءات تم إنشاؤها بنجاح</li>";
    echo "<li>اختبر وظائف نظام الشيفتات الجديد</li>";
    echo "<li>راجع الإشعارات والتقارير</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في التنفيذ</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

// إضافة CSS للتنسيق
echo "<style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    h1 { color: #2c3e50; text-align: center; }
    h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
    h3 { color: #27ae60; }
    details { margin: 10px 0; }
    summary { cursor: pointer; color: #666; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px; overflow-x: auto; }
</style>";
?>

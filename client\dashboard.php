<?php
require_once '../config/database.php';
require_once '../includes/auth_guard.php';
require_once 'includes/employee-auth.php';
require_once '../includes/shift_helpers.php';

// حماية الصفحة - التحقق من تسجيل الدخول إما عميل أو موظف
if (isset($_SESSION['client_id'])) {
    protectClientPage($pdo);
} elseif (isset($_SESSION['employee_id'])) {
    protectEmployeePage($pdo);
} else {
    // توجيه المستخدم إلى صفحة تسجيل الدخول المناسبة
    // إذا كان يحاول الوصول من مسار الموظف، توجيهه لصفحة الموظف
    // وإلا توجيهه لصفحة العميل
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, 'employee') !== false || isset($_GET['employee'])) {
        header('Location: employee-login.php');
    } else {
        header('Location: login.php');
    }
    exit;
}

// تحديد معرف العميل
if (isset($_SESSION['employee_id'])) {
    $client_id = $_SESSION['client_id']; // من جلسة الموظف
} else {
    $client_id = $_SESSION['client_id']; // من جلسة العميل
}

$page_title = "لوحة التحكم";
$active_page = "dashboard";

// معالجة أزرار فتح/إنهاء الوردية للموظفين
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['employee_id'])) {
    try {
        $employee_id = $_SESSION['employee_id'];

        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'start_shift':
                    // البحث عن الوردية النشطة للموظف
                    $stmt = $pdo->prepare("
                        SELECT es.assignment_id, es.shift_id, s.shift_name
                        FROM employee_shifts es
                        JOIN shifts s ON es.shift_id = s.shift_id
                        WHERE es.employee_id = ? AND s.shift_date = CURDATE()
                        AND s.status IN ('scheduled', 'active')
                        AND NOT EXISTS (
                            SELECT 1 FROM shift_attendance sa
                            WHERE sa.assignment_id = es.assignment_id
                            AND sa.check_in_time IS NOT NULL
                        )
                        ORDER BY s.start_time ASC
                        LIMIT 1
                    ");
                    $stmt->execute([$employee_id]);
                    $assignment = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($assignment) {
                        // إنشاء سجل حضور جديد
                        $stmt = $pdo->prepare("
                            INSERT INTO shift_attendance
                            (assignment_id, shift_id, employee_id, check_in_time, status, recorded_by)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'present', ?)
                        ");
                        $stmt->execute([
                            $assignment['assignment_id'],
                            $assignment['shift_id'],
                            $employee_id,
                            $employee_id
                        ]);

                        // تحديث حالة الوردية إلى نشطة
                        $stmt = $pdo->prepare("UPDATE shifts SET status = 'active' WHERE shift_id = ?");
                        $stmt->execute([$assignment['shift_id']]);

                        $_SESSION['success'] = "تم فتح الوردية وتسجيل الحضور بنجاح: " . $assignment['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا توجد وردية مجدولة اليوم أو تم تسجيل الحضور مسبقاً";
                    }
                    break;

                case 'end_shift':
                    // البحث عن سجل الحضور النشط
                    $stmt = $pdo->prepare("
                        SELECT sa.attendance_id, sa.shift_id, s.shift_name
                        FROM shift_attendance sa
                        JOIN shifts s ON sa.shift_id = s.shift_id
                        WHERE sa.employee_id = ? AND sa.check_in_time IS NOT NULL
                        AND sa.check_out_time IS NULL
                        ORDER BY sa.check_in_time DESC
                        LIMIT 1
                    ");
                    $stmt->execute([$employee_id]);
                    $attendance = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($attendance) {
                        // تسجيل الانصراف
                        $stmt = $pdo->prepare("
                            UPDATE shift_attendance
                            SET check_out_time = CURRENT_TIMESTAMP, recorded_by = ?
                            WHERE attendance_id = ?
                        ");
                        $stmt->execute([$employee_id, $attendance['attendance_id']]);

                        // حساب ساعات العمل باستخدام الدالة المساعدة
                        $calculation_result = calculateAttendanceHours($pdo, $attendance['attendance_id']);
                        if (isset($calculation_result['error'])) {
                            error_log('خطأ في حساب ساعات العمل: ' . $calculation_result['error']);
                        }

                        // تحديث حالة الوردية إلى مكتملة
                        $stmt = $pdo->prepare("UPDATE shifts SET status = 'completed' WHERE shift_id = ?");
                        $stmt->execute([$attendance['shift_id']]);

                        $_SESSION['success'] = "تم إنهاء الوردية وتسجيل الانصراف بنجاح: " . $attendance['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا يوجد سجل حضور نشط";
                    }
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }

    header('Location: dashboard.php');
    exit;
}

// جلب حالة الحضور للموظف (إذا كان موظف)
$employee_attendance_status = null;
if (isset($_SESSION['employee_id'])) {
    $employee_attendance_status = isEmployeeCheckedIn($pdo, $_SESSION['employee_id']);
}

// Initialize all variables at the start
$today = [
    'total_sessions' => 0,
    'active_sessions' => 0,
    'today_income' => 0,
    'pending_orders' => 0
];

$stats = [
    'total_customers' => 0,
    'total_devices' => 0,
    'total_rooms' => 0,
    'total_employees' => 0,
    'month_income' => 0,
    'year_income' => 0
];

$devices = [
    'total_devices' => 0,
    'available_devices' => 0,
    'occupied_devices' => 0,
    'maintenance_devices' => 0
];

// Initialize active sessions array
$active = [];

// Initialize orders array
$orders = [];

// Add this near the top with other initializations
$table_exists = false;

try {
    // Check if orders table exists
    $check_table = $pdo->prepare("
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = 'orders'
    ");
    $check_table->execute();
    $table_exists = (bool)$check_table->fetch()['table_exists'];

    // If table exists, get orders
    if ($table_exists) {
        // التحقق من هيكل جدول customers للطلبات
        $customer_id_column_orders = 'customer_id';
        try {
            $stmt = $pdo->query("DESCRIBE customers");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
                $customer_id_column_orders = 'id';
            }
        } catch (PDOException $e) {
            $customer_id_column_orders = null;
        }

        if ($customer_id_column_orders) {
            $orders_query = $pdo->prepare("
                SELECT o.*, c.name as customer_name
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.$customer_id_column_orders
                WHERE o.client_id = ?
                ORDER BY o.created_at DESC
                LIMIT 5
            ");
        } else {
            $orders_query = $pdo->prepare("
                SELECT o.*, NULL as customer_name
                FROM orders o
                WHERE o.client_id = ?
                ORDER BY o.created_at DESC
                LIMIT 5
            ");
        }
        $orders_query->execute([$client_id]);
        $orders = $orders_query->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $orders = [];
    }

    // Get devices statistics
    $devices_query = $pdo->prepare("
        SELECT
            COUNT(*) as total_devices,
            SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_devices,
            SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_devices,
            SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_devices
        FROM devices
        WHERE client_id = ?
    ");

    if ($devices_query->execute([$client_id])) {
        $result = $devices_query->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $devices = $result;
        }
    }


    // Get today's statistics with optimized single query
    try {
        $today_query = $pdo->prepare("
            SELECT
                COUNT(s.session_id) as total_sessions,
                SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                COALESCE(SUM(CASE WHEN s.status = 'completed' THEN s.total_cost ELSE 0 END), 0) as today_income
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            WHERE d.client_id = ?
            AND DATE(s.start_time) = CURRENT_DATE
        ");

        $today_query->execute([$client_id]);
        $result = $today_query->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $today['total_sessions'] = $result['total_sessions'] ?? 0;
            $today['active_sessions'] = $result['active_sessions'] ?? 0;
            $today['today_income'] = $result['today_income'] ?? 0;
        }

        // Get pending orders separately (only if table exists)
        $today['pending_orders'] = 0;
        if ($table_exists) {
            try {
                $orders_count_query = $pdo->prepare("SELECT COUNT(*) as pending_orders FROM orders WHERE client_id = ? AND status = 'pending'");
                $orders_count_query->execute([$client_id]);
                $orders_result = $orders_count_query->fetch(PDO::FETCH_ASSOC);
                $today['pending_orders'] = $orders_result['pending_orders'] ?? 0;
            } catch (PDOException $e) {
                // Table might not exist, ignore error
                $today['pending_orders'] = 0;
            }
        }

    } catch (PDOException $e) {
        error_log("Error getting today's statistics: " . $e->getMessage());
        // Keep default values
    }

    // إحصائيات عامة
    $stats_query = $pdo->prepare("
        SELECT
            (SELECT COUNT(*) FROM customers WHERE client_id = ?) as total_customers,
            (SELECT COUNT(*) FROM devices WHERE client_id = ?) as total_devices,
            (SELECT COUNT(*) FROM rooms WHERE client_id = ?) as total_rooms,
            (SELECT COUNT(*) FROM employees WHERE client_id = ? AND is_active = 1) as total_employees,
            (SELECT COALESCE(SUM(s.total_cost), 0)
             FROM sessions s
             JOIN devices d ON s.device_id = d.device_id
             WHERE d.client_id = ?
             AND MONTH(s.start_time) = MONTH(CURRENT_DATE)
             AND YEAR(s.start_time) = YEAR(CURRENT_DATE)
             AND s.status = 'completed') as month_income,
            (SELECT COALESCE(SUM(s.total_cost), 0)
             FROM sessions s
             JOIN devices d ON s.device_id = d.device_id
             WHERE d.client_id = ?
             AND YEAR(s.start_time) = YEAR(CURRENT_DATE)
             AND s.status = 'completed') as year_income
    ");

    $stats_query->execute([
        $client_id,
        $client_id,
        $client_id,
        $client_id,
        $client_id,
        $client_id
    ]);

    $stats = $stats_query->fetch(PDO::FETCH_ASSOC);


    // التحقق من هيكل جدول customers
    $customer_id_column = 'customer_id';
    try {
        $stmt = $pdo->query("DESCRIBE customers");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('id', $columns) && !in_array('customer_id', $columns)) {
            $customer_id_column = 'id';
        }
    } catch (PDOException $e) {
        $customer_id_column = null;
    }

    // الجلسات النشطة مع تفاصيل العملاء - محسن وسريع
    try {
        // استعلام محسن مع LIMIT لتحسين الأداء
        $active_sessions_query = "
            SELECT s.session_id, s.start_time, s.customer_id,
                   d.device_name, d.device_type, d.hourly_rate,
                   " . ($customer_id_column ? "c.name as customer_name, c.phone as customer_phone" : "NULL as customer_name, NULL as customer_phone") . ",
                   TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) as duration_minutes,
                   (TIMESTAMPDIFF(MINUTE, s.start_time, CURRENT_TIMESTAMP) * d.hourly_rate / 60) as current_cost
            FROM sessions s
            JOIN devices d ON s.device_id = d.device_id
            " . ($customer_id_column ? "LEFT JOIN customers c ON s.customer_id = c.$customer_id_column" : "") . "
            WHERE d.client_id = ? AND s.status = 'active'
            ORDER BY s.start_time DESC
            LIMIT 20
        ";

        $active_sessions = $pdo->prepare($active_sessions_query);
        $active_sessions->execute([$client_id]);
        $active = $active_sessions->fetchAll(PDO::FETCH_ASSOC) ?: [];

        // تحديث عدد الجلسات النشطة في الإحصائيات (استخدام العدد الفعلي)
        $today['active_sessions'] = count($active);

    } catch (PDOException $e) {
        error_log("Error getting active sessions: " . $e->getMessage());
        $active = [];
        $today['active_sessions'] = 0;
    }

    // جلب الأجهزة المتاحة لبدء الجلسات
    $available_devices_query = $pdo->prepare("
        SELECT d.*, r.room_name
        FROM devices d
        LEFT JOIN rooms r ON d.room_id = r.room_id
        WHERE d.client_id = ? AND d.status = 'available'
        ORDER BY r.room_name, d.device_name
        LIMIT 8
    ");
    $available_devices_query->execute([$client_id]);
    $available_devices = $available_devices_query->fetchAll(PDO::FETCH_ASSOC) ?: [];

} catch (PDOException $e) {
    $_SESSION['error'] = "حدث خطأ في جلب البيانات: " . $e->getMessage();
    $active = []; // Ensure $active is an array even on error
    $available_devices = [];
}

require_once 'includes/header.php';
?>

<style>
.device-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.device-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
}

.stats-icon {
    opacity: 0.8;
}

.device-icon {
    opacity: 0.9;
}

#customer_results {
    position: absolute;
    z-index: 1000;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    background: white;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.customer-suggestion {
    transition: background-color 0.2s ease;
}

.customer-suggestion:hover {
    background-color: #f8f9fa;
}

.modal-header.bg-success .btn-close-white {
    filter: brightness(0) invert(1);
}

/* إضافات للـ modal اليدوي */
.modal.show {
    display: block !important;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.5;
}

.modal-backdrop.show {
    opacity: 0.5;
}

body.modal-open {
    overflow: hidden;
}

/* تحسينات لقائمة العملاء */
#customer_suggestions {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
}

.customer-suggestion {
    transition: all 0.2s ease;
    border-bottom: 1px solid #f8f9fa !important;
}

.customer-suggestion:last-child {
    border-bottom: none !important;
}

.customer-suggestion:hover {
    background-color: #f8f9fa !important;
    transform: translateX(-2px);
}

#selected_customer_display {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
<div class="container-fluid py-4">
    <!-- رسائل الخطأ للصلاحيات -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php
            echo htmlspecialchars($_SESSION['error_message']);
            unset($_SESSION['error_message']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php
            switch($_GET['error']) {
                case 'no_page_access':
                    echo 'عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة. يرجى مراجعة المدير لتحديث صلاحياتك.';
                    break;
                case 'no_permission':
                    echo 'عذراً، ليس لديك الصلاحية المطلوبة لتنفيذ هذا الإجراء. يرجى مراجعة المدير.';
                    break;
                default:
                    echo 'حدث خطأ في الوصول. يرجى المحاولة مرة أخرى أو مراجعة المدير.';
            }
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- رسائل النجاح والخطأ العامة -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- أزرار الوردية للموظفين -->
    <?php if (isset($_SESSION['employee_id'])): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>إدارة الوردية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <?php if ($employee_attendance_status && $employee_attendance_status['is_checked_in']): ?>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="badge bg-success fs-6 px-3 py-2">
                                                <i class="fas fa-check-circle me-1"></i>في الوردية
                                            </span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($employee_attendance_status['shift_name']); ?></h6>
                                            <small class="text-muted">
                                                بدأت في: <?php echo date('H:i', strtotime($employee_attendance_status['check_in_time'])); ?>
                                                <?php if ($employee_attendance_status['status'] == 'on_break'): ?>
                                                    | <span class="text-warning">في استراحة</span>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="badge bg-warning fs-6 px-3 py-2">
                                                <i class="fas fa-clock me-1"></i>خارج الوردية
                                            </span>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">لم يتم فتح الوردية بعد</h6>
                                            <small class="text-muted">اضغط على "فتح الوردية" لبدء العمل</small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php if ($employee_attendance_status && $employee_attendance_status['is_checked_in']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="end_shift">
                                        <button type="submit" class="btn btn-danger btn-lg"
                                                onclick="return confirm('هل أنت متأكد من إنهاء الوردية؟')">
                                            <i class="fas fa-stop me-2"></i>إنهاء الوردية
                                        </button>
                                    </form>
                                    <a href="quick_attendance.php" class="btn btn-outline-info ms-2">
                                        <i class="fas fa-user-check me-1"></i>إدارة الحضور
                                    </a>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="start_shift">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-play me-2"></i>فتح الوردية
                                        </button>
                                    </form>
                                    <a href="shifts.php" class="btn btn-outline-primary ms-2">
                                        <i class="fas fa-calendar me-1"></i>عرض الورديات
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- ترحيب وملخص -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
        <div>
            <h1 class="h2 mb-0">
                مرحباً
                <?php
                if (isset($_SESSION['employee_id'])) {
                    echo htmlspecialchars($_SESSION['employee_name'] ?? 'موظف');
                } else {
                    echo htmlspecialchars($_SESSION['owner_name'] ?? $_SESSION['client_name'] ?? 'مالك المحل');
                }
                ?>
            </h1>
            <br>
            <p class="text-muted mb-0">
                <?php
                setlocale(LC_TIME, 'ar_EG.UTF-8');
                echo date('l, j F Y');
                ?>
            </p>
            <br>
        </div>
        <div class="btn-toolbar">
            <button class="btn btn-sm btn-outline-secondary me-2" id="refreshStats">
                <i class="fas fa-sync-alt me-1"></i>تحديث
            </button>
            <a href="reports.php" class="btn btn-sm btn-primary">
                <i class="fas fa-chart-bar me-1"></i>عرض التقارير
            </a>
        </div>
    </div>

    <!-- إحصائيات اليوم -->
    <div class="row g-3 mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-success h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-cash-register fa-3x text-success"></i>
                    </div>
                    <h6 class="card-title text-success mb-2">دخل اليوم</h6>
                    <div class="stats-number text-success"><?php echo number_format($today['today_income'], 2); ?></div>
                    <small class="text-muted">جنيه مصري</small>
                    <div class="mt-2">
                        <small class="text-muted">من <?php echo $today['total_sessions']; ?> جلسة</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-primary h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-calendar-alt fa-3x text-primary"></i>
                    </div>
                    <h6 class="card-title text-primary mb-2">دخل الشهر</h6>
                    <div class="stats-number text-primary"><?php echo number_format($stats['month_income'], 2); ?></div>
                    <small class="text-muted">جنيه مصري</small>
                    <div class="mt-2">
                        <small class="text-muted"><?php echo date('F Y'); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-info h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-desktop fa-3x text-info"></i>
                    </div>
                    <h6 class="card-title text-info mb-2">إجمالي الأجهزة</h6>
                    <div class="stats-number text-info"><?php echo $devices['total_devices']; ?></div>
                    <small class="text-muted">جهاز</small>
                    <div class="mt-2">
                        <small class="text-success">متاح: <?php echo $devices['available_devices']; ?></small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-warning h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-play-circle fa-3x text-warning"></i>
                    </div>
                    <h6 class="card-title text-warning mb-2">الجلسات النشطة</h6>
                    <div class="stats-number text-warning" id="active-sessions-count"><?php echo $today['active_sessions']; ?></div>
                    <small class="text-muted">جلسة</small>
                    <div class="mt-2">
                        <small class="text-muted">مشغولة حالياً</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-secondary h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-users fa-3x text-secondary"></i>
                    </div>
                    <h6 class="card-title text-secondary mb-2">إجمالي العملاء</h6>
                    <div class="stats-number text-secondary"><?php echo number_format($stats['total_customers']); ?></div>
                    <small class="text-muted">عميل</small>
                    <div class="mt-2">
                        <small class="text-muted">مسجل</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6">
            <div class="card stats-card border-dark h-100">
                <div class="card-body text-center">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-chart-line fa-3x text-dark"></i>
                    </div>
                    <h6 class="card-title text-dark mb-2">دخل السنة</h6>
                    <div class="stats-number text-dark"><?php echo number_format($stats['year_income'], 2); ?></div>
                    <small class="text-muted">جنيه مصري</small>
                    <div class="mt-2">
                        <small class="text-muted"><?php echo date('Y'); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>






    <!-- الإحصائيات التفصيلية -->
    <div class="row g-3 mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-gamepad me-2"></i>تفاصيل الأجهزة</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <div class="mt-2">
                                    <strong class="d-block"><?php echo $devices['available_devices'] ?? 0; ?></strong>
                                    <small>متاح</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-warning">
                                <i class="fas fa-clock fa-2x"></i>
                                <div class="mt-2">
                                    <strong class="d-block"><?php echo $devices['occupied_devices'] ?? 0; ?></strong>
                                    <small>مشغول</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-danger">
                                <i class="fas fa-tools fa-2x"></i>
                                <div class="mt-2">
                                    <strong class="d-block"><?php echo $devices['maintenance_devices'] ?? 0; ?></strong>
                                    <small>صيانة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="progress" style="height: 10px;">
                            <?php
                            $total = $devices['total_devices'] ?? 0;
                            if ($total > 0) {
                                $available_percent = ($devices['available_devices'] ?? 0) / $total * 100;
                                $occupied_percent = ($devices['occupied_devices'] ?? 0) / $total * 100;
                                $maintenance_percent = ($devices['maintenance_devices'] ?? 0) / $total * 100;
                            } else {
                                $available_percent = 0;
                                $occupied_percent = 0;
                                $maintenance_percent = 0;
                            }
                            ?>
                            <div class="progress-bar bg-success" style="width: <?php echo $available_percent; ?>%"></div>
                            <div class="progress-bar bg-warning" style="width: <?php echo $occupied_percent; ?>%"></div>
                            <div class="progress-bar bg-danger" style="width: <?php echo $maintenance_percent; ?>%"></div>
                        </div>
                        <?php if ($total == 0): ?>
                            <small class="text-muted mt-2 d-block">لا توجد أجهزة مسجلة</small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإحصائيات العامة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-door-open fa-2x text-info mb-2"></i>
                                <div>
                                    <strong class="d-block h4"><?php echo number_format($stats['total_rooms']); ?></strong>
                                    <small class="text-muted">غرفة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center">
                                <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                                <div>
                                    <strong class="d-block h4"><?php echo number_format($stats['total_employees']); ?></strong>
                                    <small class="text-muted">موظف</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="text-center">
                                <small class="text-muted">دخل الشهر: </small>
                                <strong class="text-success"><?php echo number_format($stats['month_income'], 2); ?> ج.م</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأجهزة المتاحة لبدء الجلسات -->
    <?php if (!empty($available_devices)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-play me-2"></i>الأجهزة المتاحة - بدء جلسة سريع
                        <span class="badge bg-light text-success ms-2"><?php echo count($available_devices); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php foreach ($available_devices as $device): ?>
                            <div class="col-md-3 col-sm-6">
                                <div class="card device-card h-100 border-success">
                                    <div class="card-body text-center">
                                        <div class="device-icon mb-3">
                                            <i class="fas fa-<?php echo $device['device_type'] === 'PS4' || $device['device_type'] === 'PS5' ? 'gamepad' : 'desktop'; ?> fa-3x text-success"></i>
                                        </div>
                                        <h6 class="card-title"><?php echo htmlspecialchars($device['device_name']); ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($device['device_type']); ?>
                                                <?php if ($device['room_name']): ?>
                                                    <br><?php echo htmlspecialchars($device['room_name']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </p>
                                        <div class="mb-2">
                                            <span class="badge bg-success">متاح</span>
                                        </div>
                                        <div class="mb-3">
                                            <?php
                                            $single_rate = $device['single_rate'] ?? $device['hourly_rate'];
                                            $multi_rate = $device['multi_rate'] ?? $device['hourly_rate'];
                                            ?>
                                            <div class="text-center">
                                                <small class="text-muted d-block">الأسعار</small>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-primary">
                                                            <i class="fas fa-user"></i> فردي<br>
                                                            <strong><?php echo number_format($single_rate, 2); ?> ج.م</strong>
                                                        </small>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-info">
                                                            <i class="fas fa-users"></i> زوجي<br>
                                                            <strong><?php echo number_format($multi_rate, 2); ?> ج.م</strong>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="btn btn-success btn-sm w-100"
                                                data-device-id="<?php echo $device['device_id']; ?>"
                                                data-device-name="<?php echo htmlspecialchars($device['device_name']); ?>"
                                                data-device-type="<?php echo htmlspecialchars($device['device_type']); ?>"
                                                data-hourly-rate="<?php echo $device['hourly_rate']; ?>"
                                                data-single-rate="<?php echo $device['single_rate'] ?? $device['hourly_rate']; ?>"
                                                data-multi-rate="<?php echo $device['multi_rate'] ?? $device['hourly_rate']; ?>"
                                                data-room-name="<?php echo htmlspecialchars($device['room_name'] ?? ''); ?>"
                                                onclick="console.log('Button clicked!'); if(typeof showStartSessionModalFromButton === 'function') { showStartSessionModalFromButton(this); } else { alert('الدالة غير متاحة، يرجى إعادة تحميل الصفحة'); }">
                                            <i class="fas fa-play me-1"></i>بدء جلسة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if (count($available_devices) >= 8): ?>
                        <div class="text-center mt-3">
                            <a href="devices.php" class="btn btn-outline-success">
                                <i class="fas fa-list me-1"></i>عرض جميع الأجهزة
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- الجلسات النشطة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-play-circle me-2"></i>الجلسات النشطة
                        <span class="badge bg-dark text-warning ms-2"><?php echo is_array($active) ? count($active) : 0; ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (is_array($active) && count($active) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th><i class="fas fa-gamepad me-2"></i>الجهاز</th>
                                        <th><i class="fas fa-user me-2"></i>العميل</th>
                                        <th><i class="fas fa-clock me-2"></i>مدة الجلسة</th>
                                        <th><i class="fas fa-money-bill me-2"></i>التكلفة الحالية</th>
                                        <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($active as $session): ?>
                                        <tr data-session-start="<?php echo $session['start_time']; ?>"
                                            data-hourly-rate="<?php echo $session['hourly_rate']; ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="device-icon me-2">
                                                        <i class="fas fa-<?php echo $session['device_type'] === 'PS4' ? 'gamepad' : 'desktop'; ?> fa-2x text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($session['device_name']); ?></strong>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($session['device_type']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <?php if ($session['customer_name']): ?>
                                                        <strong><?php echo htmlspecialchars($session['customer_name']); ?></strong>
                                                        <?php if ($session['customer_phone']): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($session['customer_phone']); ?></small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="session-duration">
                                                <?php
                                                $duration_minutes = $session['duration_minutes'] ?? 0;
                                                if ($duration_minutes > 0) {
                                                    $hours = floor($duration_minutes / 60);
                                                    $minutes = $duration_minutes % 60;
                                                    if ($hours > 0) {
                                                        echo $hours . ' ساعة ' . $minutes . ' دقيقة';
                                                    } else {
                                                        echo $minutes . ' دقيقة';
                                                    }
                                                } else {
                                                    echo '0 دقيقة';
                                                }
                                                ?>
                                            </td>
                                            <td class="session-cost">
                                                <strong class="text-success">
                                                    <?php
                                                    $current_cost = $session['current_cost'] ?? 0;
                                                    echo number_format($current_cost, 2);
                                                    ?> ج.م
                                                </strong>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-warning" onclick="endSession(<?php echo $session['session_id'] ?? $session['id']; ?>)">
                                                        <i class="fas fa-stop me-1"></i>إنهاء
                                                    </button>
                                                    <button class="btn btn-sm btn-info" onclick="location.href='sessions.php?view=<?php echo $session['session_id'] ?? $session['id']; ?>'">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جلسات نشطة حالياً</h5>
                            <p class="text-muted">جميع الأجهزة متاحة للاستخدام</p>
                            <a href="sessions.php" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>بدء جلسة جديدة
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

            <!-- أحدث الطلبات -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>أحدث الطلبات
                                <span class="badge bg-light text-info ms-2"><?php echo $today['pending_orders'] ?? 0; ?></span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (!empty($orders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>#</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($orders as $order): ?>
                                                <tr>
                                                    <td>#<?php echo $order['id']; ?></td>
                                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                    <td><?php echo number_format($order['total_amount'], 2); ?> ج.م</td>
                                                    <td>
                                                        <?php
                                                        $status_classes = [
                                                            'pending' => 'warning',
                                                            'completed' => 'success',
                                                            'cancelled' => 'danger'
                                                        ];
                                                        $status_text = [
                                                            'pending' => 'معلق',
                                                            'completed' => 'مكتمل',
                                                            'cancelled' => 'ملغي'
                                                        ];
                                                        ?>
                                                        <span class="badge bg-<?php echo $status_classes[$order['status']]; ?>">
                                                            <?php echo $status_text[$order['status']]; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                                    <td>
                                                        <a href="orders.php?id=<?php echo $order['id']; ?>" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <img src="assets/images/no-orders.svg" alt="" class="mb-3" style="width: 120px;">
                                    <h5 class="text-muted">لا توجد طلبات حديثة</h5>
                                    <?php if (!$table_exists): ?>
                                        <small class="text-muted d-block">جدول الطلبات غير موجود</small>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
</div>

<!-- Modal بدء جلسة جديدة -->
<div class="modal fade" id="startSessionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-play me-2"></i>بدء جلسة جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="startSessionForm" method="post" action="sessions.php">
                <div class="modal-body">
                    <input type="hidden" name="start_session" value="1">
                    <input type="hidden" name="device_id" id="modal_device_id">

                    <!-- معلومات الجهاز -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <i id="modal_device_icon" class="fas fa-gamepad fa-2x text-success"></i>
                                </div>
                                <div class="col">
                                    <h6 class="mb-1" id="modal_device_name">اسم الجهاز</h6>
                                    <small class="text-muted" id="modal_device_details">تفاصيل الجهاز</small>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-success">متاح</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار العميل -->
                    <div class="mb-3">
                        <label for="customer_search" class="form-label">
                            <i class="fas fa-user me-1"></i>العميل
                            <span class="badge bg-secondary ms-2">اختياري</span>
                        </label>
                        <div class="input-group position-relative">
                            <span class="input-group-text">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control" id="customer_search"
                                   placeholder="ابحث عن عميل أو اتركه فارغاً"
                                   autocomplete="off">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearCustomer()" title="مسح">
                                <i class="fas fa-times"></i>
                            </button>
                            <div id="customer_suggestions" class="position-absolute bg-white border rounded shadow-sm"
                                 style="top: 100%; left: 0; right: 0; z-index: 1050; display: none; max-height: 250px; overflow-y: auto;"></div>
                        </div>
                        <input type="hidden" name="customer_id" id="selected_customer_id">

                        <!-- عرض العميل المحدد -->
                        <div id="selected_customer_display" class="mt-2" style="display: none;">
                            <div class="alert alert-success py-2 mb-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-user-check me-2"></i>
                                        <strong id="selected_customer_name"></strong>
                                        <br><small id="selected_customer_phone" class="text-muted"></small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearCustomer()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- رسالة مساعدة -->
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            يمكنك البحث بالاسم أو رقم الهاتف، أو ترك الحقل فارغاً لجلسة بدون عميل
                        </small>

                        <!-- زر التبديل إلى القائمة المنسدلة -->
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleCustomerSelectionMode()">
                                <i class="fas fa-list me-1"></i>استخدام القائمة المنسدلة بدلاً من البحث
                            </button>
                        </div>

                        <!-- القائمة المنسدلة البديلة (مخفية افتراضياً) -->
                        <div id="customer_select_mode" style="display: none;" class="mt-3">
                            <label for="customer_select_dropdown" class="form-label">
                                <i class="fas fa-user me-1"></i>اختر العميل
                            </label>
                            <select class="form-select" id="customer_select_dropdown">
                                <option value="">-- اختر عميل أو اتركه فارغاً --</option>
                                <?php
                                // جلب العملاء للقائمة المنسدلة
                                try {
                                    // التحقق من وجود عمود customer_id في جدول customers
                                    $customer_id_column = null;
                                    try {
                                        $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'customer_id'");
                                        if ($columns_check->rowCount() > 0) {
                                            $customer_id_column = 'customer_id';
                                        } else {
                                            $columns_check = $pdo->query("SHOW COLUMNS FROM customers LIKE 'id'");
                                            if ($columns_check->rowCount() > 0) {
                                                $customer_id_column = 'id';
                                            }
                                        }
                                    } catch (PDOException $e) {
                                        $customer_id_column = 'customer_id'; // افتراضي
                                    }

                                    if ($customer_id_column) {
                                        $customers_stmt = $pdo->prepare("
                                            SELECT $customer_id_column as id, name, phone
                                            FROM customers
                                            WHERE client_id = ?
                                            ORDER BY name ASC
                                        ");
                                        $customers_stmt->execute([$client_id]);
                                        $customers_dropdown = $customers_stmt->fetchAll();

                                        foreach ($customers_dropdown as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($customer['name']); ?>"
                                                    data-phone="<?php echo htmlspecialchars($customer['phone'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                                <?php if ($customer['phone']): ?>
                                                    - <?php echo htmlspecialchars($customer['phone']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach;
                                    }
                                } catch (PDOException $e) {
                                    echo '<option value="" disabled>لا يوجد عملاء متاحين</option>';
                                }
                                ?>
                            </select>
                            <small class="form-text text-muted">
                                يمكنك ترك هذا الحقل فارغاً لبدء جلسة بدون عميل محدد
                            </small>
                        </div>
                    </div>

                    <!-- نوع الجلسة -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-clock me-1"></i>نوع الجلسة
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="session_type"
                                           id="session_open" value="open" checked>
                                    <label class="form-check-label" for="session_open">
                                        <strong>جلسة مفتوحة</strong>
                                        <br><small class="text-muted">بدون وقت محدد</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="session_type"
                                           id="session_timed" value="timed">
                                    <label class="form-check-label" for="session_timed">
                                        <strong>جلسة محددة الوقت</strong>
                                        <br><small class="text-muted">مع وقت انتهاء</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نوع اللعب -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>نوع اللعب
                        </label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="game_type"
                                           id="game_type_single" value="single" checked>
                                    <label class="form-check-label" for="game_type_single">
                                        <i class="fas fa-user me-1"></i><strong>فردي</strong>
                                        <br><small class="text-muted">لاعب واحد</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="game_type"
                                           id="game_type_multiplayer" value="multiplayer">
                                    <label class="form-check-label" for="game_type_multiplayer">
                                        <i class="fas fa-users me-1"></i><strong>زوجي</strong>
                                        <br><small class="text-muted">أكثر من لاعب</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مدة الجلسة (تظهر عند اختيار جلسة محددة الوقت) -->
                    <div class="mb-3" id="session_duration_section" style="display: none;">
                        <label for="session_duration" class="form-label">
                            <i class="fas fa-hourglass-half me-1"></i>مدة الجلسة (بالساعات)
                        </label>
                        <select class="form-select" name="session_duration" id="session_duration">
                            <option value="0.5">30 دقيقة</option>
                            <option value="1" selected>ساعة واحدة</option>
                            <option value="1.5">ساعة ونصف</option>
                            <option value="2">ساعتان</option>
                            <option value="3">3 ساعات</option>
                            <option value="4">4 ساعات</option>
                            <option value="6">6 ساعات</option>
                            <option value="8">8 ساعات</option>
                        </select>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="session_notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>ملاحظات (اختياري)
                        </label>
                        <textarea class="form-control" name="notes" id="session_notes"
                                  rows="2" placeholder="أي ملاحظات خاصة بالجلسة..."></textarea>
                    </div>

                    <!-- تكلفة متوقعة -->
                    <div class="card bg-light border-primary">
                        <div class="card-body text-center">
                            <div class="mb-2">
                                <i class="fas fa-calculator text-primary mb-2"></i>
                                <h6 class="text-primary mb-2">التكلفة المتوقعة</h6>
                            </div>
                            <div id="estimated_cost" class="text-center">
                                <span class="fw-bold text-primary">0.00 ج.م</span>
                                <br><small class="text-muted">سعر الساعة</small>
                            </div>
                            <hr class="my-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                السعر النهائي يحسب عند انتهاء الجلسة
                            </small>
                            <div id="device_rate_info" class="mt-2" style="display: none;">
                                <small class="text-info">
                                    <i class="fas fa-gamepad me-1"></i>
                                    <span id="device_name_cost"></span> -
                                    <span id="device_hourly_rate"></span> ج.م/ساعة
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-play me-1"></i>بدء الجلسة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إعلان الدوال عالمياً في البداية
window.showStartSessionModalFromButton = function(button) {
    try {
        console.log('Global showStartSessionModalFromButton called');

        // التأكد من وجود الزر والبيانات
        if (!button) {
            console.error('Button element is null');
            alert('خطأ: عنصر الزر غير موجود');
            return;
        }

        // استخراج البيانات من data attributes
        const device = {
            device_id: button.dataset.deviceId,
            device_name: button.dataset.deviceName,
            device_type: button.dataset.deviceType,
            hourly_rate: parseFloat(button.dataset.hourlyRate),
            single_rate: parseFloat(button.dataset.singleRate),
            multi_rate: parseFloat(button.dataset.multiRate),
            room_name: button.dataset.roomName
        };

        console.log('Device data from button:', device);

        // التحقق من صحة البيانات
        if (!device.device_id || !device.device_name) {
            console.error('Missing required device data:', device);
            alert('خطأ: بيانات الجهاز غير مكتملة');
            return;
        }

        // محاولة إظهار الـ modal
        const modalElement = document.getElementById('startSessionModal');
        if (!modalElement) {
            console.error('Modal element not found');
            alert('خطأ: نافذة بدء الجلسة غير موجودة');
            return;
        }

        // تحديث بيانات الـ modal
        currentDevice = device; // تعيين الجهاز الحالي
        document.getElementById('modal_device_id').value = device.device_id;
        document.getElementById('modal_device_name').textContent = device.device_name;
        document.getElementById('modal_device_details').textContent =
            `${device.device_type}${device.room_name ? ' - ' + device.room_name : ''}`;

        // تحديث أيقونة الجهاز
        const iconElement = document.getElementById('modal_device_icon');
        if (iconElement) {
            const iconClass = (device.device_type === 'PS4' || device.device_type === 'PS5') ? 'gamepad' : 'desktop';
            iconElement.className = `fas fa-${iconClass} fa-2x text-success`;
        }

        // إعادة تعيين نموذج العميل
        clearCustomer();

        // إعادة تعيين نوع الجلسة ونوع اللعب
        const sessionTypeOpen = document.getElementById('session_type_open');
        const gameTypeSingle = document.getElementById('game_type_single');
        const durationSection = document.getElementById('session_duration_section');

        if (sessionTypeOpen) sessionTypeOpen.checked = true;
        if (gameTypeSingle) gameTypeSingle.checked = true;
        if (durationSection) durationSection.style.display = 'none';

        // إظهار رسالة ترحيب في حقل البحث
        showWelcomeMessage();

        // تحديث التكلفة المتوقعة
        setTimeout(() => {
            updateEstimatedCost();
        }, 100);

        // إظهار الـ modal
        if (typeof bootstrap !== 'undefined') {
            try {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                console.log('Modal shown with Bootstrap');
            } catch (error) {
                console.error('Bootstrap modal error:', error);
                showModalManually(modalElement);
            }
        } else {
            console.log('Bootstrap not available, using manual method');
            showModalManually(modalElement);
        }

    } catch (error) {
        console.error('Error in showStartSessionModalFromButton:', error);
        alert('حدث خطأ: ' + error.message);
    }
};

// دالة لإظهار الـ modal يدوياً كحل بديل
window.showModalManually = function(modalElement) {
    console.log('Using manual modal display');

    // إضافة الكلاسات المطلوبة
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-modal', 'true');
    modalElement.setAttribute('role', 'dialog');

    // إضافة backdrop
    let backdrop = document.getElementById('manual-modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'manual-modal-backdrop';
        document.body.appendChild(backdrop);
    }

    // منع scroll في الـ body
    document.body.classList.add('modal-open');

    // إضافة event listener لإغلاق الـ modal
    const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            hideModalManually(modalElement);
        });
    });

    // إغلاق عند النقر على الـ backdrop
    backdrop.addEventListener('click', function() {
        hideModalManually(modalElement);
    });

    // إغلاق عند الضغط على Escape
    document.addEventListener('keydown', function escapeHandler(e) {
        if (e.key === 'Escape') {
            hideModalManually(modalElement);
            document.removeEventListener('keydown', escapeHandler);
        }
    });
};

// دالة لإخفاء الـ modal يدوياً
window.hideModalManually = function(modalElement) {
    console.log('Hiding modal manually');

    // إزالة الكلاسات
    modalElement.classList.remove('show');
    modalElement.style.display = 'none';
    modalElement.removeAttribute('aria-modal');
    modalElement.removeAttribute('role');

    // إزالة الـ backdrop
    const backdrop = document.getElementById('manual-modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }

    // إعادة تفعيل scroll في الـ body
    document.body.classList.remove('modal-open');
};

// تحديث الإحصائيات كل 5 دقائق
setInterval(function() {
    updateDashboardStats();
}, 300000);

// زر التحديث اليدوي
document.getElementById('refreshStats').addEventListener('click', function() {
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    this.disabled = true;
    updateDashboardStats();
});

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    fetch('api/get_dashboard_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عدد الجلسات النشطة
                const activeSessionsElement = document.getElementById('active-sessions-count');
                if (activeSessionsElement) {
                    activeSessionsElement.textContent = data.active_sessions || 0;
                }

                // تحديث badge الجلسات النشطة
                const activeSessionsBadge = document.querySelector('.card-header .badge');
                if (activeSessionsBadge) {
                    activeSessionsBadge.textContent = data.active_sessions || 0;
                }

                console.log('Dashboard stats updated successfully');
            }
        })
        .catch(error => {
            console.error('Error updating dashboard stats:', error);
        })
        .finally(() => {
            // إعادة تعيين زر التحديث
            const refreshBtn = document.getElementById('refreshStats');
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>تحديث';
                refreshBtn.disabled = false;
            }
        });
}

function startSession(deviceId) {
    if (confirm('هل تريد بدء جلسة جديدة؟')) {
        location.href = 'sessions.php?start=' + deviceId;
    }
}

function endSession(sessionId) {
    if (confirm('هل تريد إنهاء الجلسة الحالية؟\nسيتم حساب التكلفة النهائية وإنشاء الفاتورة.')) {
        // إظهار مؤشر التحميل
        const button = event.target;
        if (button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإنهاء...';
            button.disabled = true;
        }

        location.href = 'sessions.php?action=end&session_id=' + sessionId;
    }
}

// تحديث أوقات الجلسات النشطة كل دقيقة
setInterval(updateSessionTimers, 60000);

function updateSessionTimers() {
    const sessionRows = document.querySelectorAll('[data-session-start]');

    sessionRows.forEach(row => {
        const startTime = new Date(row.dataset.sessionStart);
        const now = new Date();
        const diffMinutes = Math.floor((now - startTime) / (1000 * 60));

        const durationCell = row.querySelector('.session-duration');
        if (durationCell) {
            const hours = Math.floor(diffMinutes / 60);
            const minutes = diffMinutes % 60;

            if (hours > 0) {
                durationCell.textContent = `${hours} ساعة ${minutes} دقيقة`;
            } else {
                durationCell.textContent = `${minutes} دقيقة`;
            }
        }

        const costCell = row.querySelector('.session-cost');
        const hourlyRate = parseFloat(row.dataset.hourlyRate || 0);
        if (costCell && hourlyRate > 0) {
            const currentCost = (diffMinutes / 60) * hourlyRate;
            costCell.innerHTML = `<strong class="text-success">${currentCost.toFixed(2)} ج.م</strong>`;
        }
    });
}

// تشغيل تحديث الأوقات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    console.log('Modal element exists:', document.getElementById('startSessionModal') !== null);
    updateSessionTimers();

    // التأكد من تحميل Bootstrap وإعداد الـ Modal
    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded!');
    }

    // التأكد من أن الدوال متاحة عالمياً
    window.showStartSessionModalFromButton = showStartSessionModalFromButton;
    window.showStartSessionModal = showStartSessionModal;
    window.showModalManually = showModalManually;
    window.hideModalManually = hideModalManually;
});

// إدارة Modal بدء الجلسة
let currentDevice = null;





// تحديث التكلفة المتوقعة
function updateEstimatedCost() {
    if (!currentDevice) {
        console.log('No current device selected');
        return;
    }

    try {
        const sessionTypeElement = document.querySelector('input[name="session_type"]:checked');
        const sessionType = sessionTypeElement ? sessionTypeElement.value : 'open';
        const durationElement = document.getElementById('session_duration');
        const duration = durationElement ? parseFloat(durationElement.value) || 1 : 1;

        // جلب نوع اللعب المختار
        const gameTypeElement = document.querySelector('input[name="game_type"]:checked');
        const gameType = gameTypeElement ? gameTypeElement.value : 'single';

        // تحديد السعر بناءً على نوع اللعب
        let selectedRate = 0;
        if (gameType === 'multiplayer') {
            selectedRate = parseFloat(currentDevice.multi_rate) || parseFloat(currentDevice.hourly_rate) || 0;
        } else {
            selectedRate = parseFloat(currentDevice.single_rate) || parseFloat(currentDevice.hourly_rate) || 0;
        }

        console.log('Calculating cost:', {
            sessionType: sessionType,
            duration: duration,
            gameType: gameType,
            selectedRate: selectedRate
        });

        let estimatedCost = 0;
        let costDescription = '';
        let gameTypeText = gameType === 'multiplayer' ? 'زوجي' : 'فردي';

        if (sessionType === 'timed') {
            // جلسة محددة الوقت
            estimatedCost = duration * selectedRate;

            // تحويل المدة إلى ساعات ودقائق للعرض
            const hours = Math.floor(duration);
            const minutes = Math.round((duration - hours) * 60);

            if (hours > 0 && minutes > 0) {
                costDescription = `${hours} ساعة و ${minutes} دقيقة`;
            } else if (hours > 0) {
                costDescription = `${hours} ساعة`;
            } else {
                costDescription = `${minutes} دقيقة`;
            }
        } else {
            // جلسة مفتوحة - عرض سعر الساعة
            estimatedCost = selectedRate;
            costDescription = 'سعر الساعة';
        }

        const costElement = document.getElementById('estimated_cost');
        if (costElement) {
            if (sessionType === 'timed') {
                costElement.innerHTML = `
                    <div class="text-success">
                        <span class="fw-bold fs-4">${estimatedCost.toFixed(2)} ج.م</span>
                        <br><small class="text-muted">${costDescription}</small>
                        <br><small class="text-success">
                            <i class="fas fa-clock me-1"></i>
                            ${selectedRate.toFixed(2)} ج.م/ساعة × ${duration} ساعة
                        </small>
                        <br><small class="text-info">
                            <i class="fas fa-${gameType === 'multiplayer' ? 'users' : 'user'} me-1"></i>
                            نوع اللعب: ${gameTypeText}
                        </small>
                    </div>
                `;
            } else {
                costElement.innerHTML = `
                    <div class="text-primary">
                        <span class="fw-bold fs-4">${estimatedCost.toFixed(2)} ج.م</span>
                        <br><small class="text-muted">${costDescription}</small>
                        <br><small class="text-info">
                            <i class="fas fa-infinity me-1"></i>
                            جلسة مفتوحة - الحساب عند الانتهاء
                        </small>
                        <br><small class="text-info">
                            <i class="fas fa-${gameType === 'multiplayer' ? 'users' : 'user'} me-1"></i>
                            نوع اللعب: ${gameTypeText}
                        </small>
                    </div>
                `;
            }
        }

        console.log('Cost updated:', {
            estimatedCost: estimatedCost,
            description: costDescription
        });

    } catch (error) {
        console.error('Error updating estimated cost:', error);
        const costElement = document.getElementById('estimated_cost');
        if (costElement) {
            costElement.innerHTML = '<span class="text-danger">خطأ في الحساب</span>';
        }
    }
}

// إدارة نوع الجلسة ونوع اللعب
document.addEventListener('DOMContentLoaded', function() {
    const sessionTypeRadios = document.querySelectorAll('input[name="session_type"]');
    const gameTypeRadios = document.querySelectorAll('input[name="game_type"]');
    const durationSection = document.getElementById('session_duration_section');
    const durationSelect = document.getElementById('session_duration');

    if (sessionTypeRadios.length > 0) {
        sessionTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'timed') {
                    durationSection.style.display = 'block';
                } else {
                    durationSection.style.display = 'none';
                }
                updateEstimatedCost();
            });
        });
    }

    if (gameTypeRadios.length > 0) {
        gameTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                updateEstimatedCost();
            });
        });
    }

    if (durationSelect) {
        durationSelect.addEventListener('change', updateEstimatedCost);
    }
});

// البحث عن العملاء
let customerSearchTimeout;
let selectedCustomerId = null;

document.addEventListener('DOMContentLoaded', function() {
    const customerSearchInput = document.getElementById('customer_search');
    const customerSuggestions = document.getElementById('customer_suggestions');

    if (customerSearchInput) {
        customerSearchInput.addEventListener('input', function() {
            clearTimeout(customerSearchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                customerSuggestions.style.display = 'none';
                return;
            }

            customerSearchTimeout = setTimeout(() => {
                showSearchLoading();
                searchCustomers(query);
            }, 300);
        });

        // إخفاء القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!customerSearchInput.contains(e.target) && !customerSuggestions.contains(e.target)) {
                customerSuggestions.style.display = 'none';
            }
        });
    }
});

function searchCustomers(query) {
    console.log('Searching for customers:', query);

    fetch(`api/search-customers.php?q=${encodeURIComponent(query)}`)
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);

        // التحقق من وجود محتوى
        if (!text || text.trim() === '') {
            console.error('Empty response received');
            showSearchError('لم يتم استلام أي بيانات من الخادم');
            return;
        }

        try {
            const data = JSON.parse(text);
            console.log('Parsed data:', data);

            // التحقق من التنسيق الجديد للـ API
            if (data && data.success === true && Array.isArray(data.customers)) {
                displayCustomerSuggestions(data.customers);
            } else if (Array.isArray(data)) {
                // التنسيق القديم - مصفوفة مباشرة
                displayCustomerSuggestions(data);
            } else if (data && data.success === false) {
                console.error('API Error:', data.message);
                showSearchError('خطأ في البحث: ' + (data.message || 'خطأ غير معروف'));
            } else {
                console.error('Unexpected response format:', data);
                console.error('Expected object with success and customers properties, got:', typeof data);
                showSearchError('تنسيق استجابة غير متوقع - تحقق من سجل وحدة التحكم');
            }
        } catch (parseError) {
            console.error('JSON Parse Error:', parseError);
            console.error('Response text:', text);

            // محاولة عرض جزء من النص للتشخيص
            const preview = text.length > 200 ? text.substring(0, 200) + '...' : text;
            showSearchError('خطأ في تحليل البيانات - النص المستلم: ' + preview);
        }
    })
    .catch(error => {
        console.error('Fetch Error:', error);
        showSearchError('خطأ في الاتصال: ' + error.message);
    });
}

function showSearchError(message) {
    const customerSuggestions = document.getElementById('customer_suggestions');
    customerSuggestions.innerHTML = `
        <div class="p-3 text-center text-danger">
            <i class="fas fa-exclamation-triangle mb-2"></i>
            <br><strong>خطأ في البحث</strong>
            <br><small>${message}</small>
            <br><small class="text-muted mt-2">
                <i class="fas fa-info-circle me-1"></i>
                نصائح للحل:
                <br>• تحقق من الاتصال بالإنترنت
                <br>• جرب إعادة تحميل الصفحة
                <br>• اتصل بالدعم الفني إذا استمرت المشكلة
            </small>
            <br>
            <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="location.reload()">
                <i class="fas fa-refresh me-1"></i>إعادة تحميل الصفحة
            </button>
        </div>
    `;
    customerSuggestions.style.display = 'block';
}

function displayCustomerSuggestions(customers) {
    const customerSuggestions = document.getElementById('customer_suggestions');

    if (customers.length === 0) {
        const searchQuery = document.getElementById('customer_search').value.trim();
        customerSuggestions.innerHTML = `
            <div class="p-3 text-center">
                <div class="text-muted mb-3">
                    <i class="fas fa-search fa-2x mb-2 text-secondary"></i>
                    <br><strong>لا توجد نتائج مطابقة</strong>
                    <br><small>لم يتم العثور على عملاء يطابقون "${searchQuery}"</small>
                </div>
                ${searchQuery.length >= 2 ? `
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="showAddCustomerOption('${searchQuery.replace(/'/g, "\\'")}')">
                            <i class="fas fa-user-plus me-1"></i>
                            إضافة عميل جديد: "${searchQuery}"
                        </button>
                    </div>
                ` : ''}
                <div class="text-muted">
                    <small>
                        <i class="fas fa-lightbulb me-1"></i>
                        نصائح للبحث:
                        <br>• جرب البحث بجزء من الاسم
                        <br>• ابحث برقم الهاتف
                        <br>• تأكد من الإملاء
                    </small>
                </div>
            </div>
        `;
        customerSuggestions.style.display = 'block';
        return;
    }

    let html = '';
    customers.forEach((customer, index) => {
        const customerName = customer.name.replace(/'/g, "\\'");
        const customerPhone = customer.phone || '';

        html += `
            <div class="customer-suggestion p-3 border-bottom" style="cursor: pointer; transition: background-color 0.2s;"
                 onclick="selectCustomer(${customer.id}, '${customerName}', '${customerPhone}')"
                 onmouseover="this.style.backgroundColor='#f8f9fa'"
                 onmouseout="this.style.backgroundColor='white'">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 40px; height: 40px; font-size: 16px; font-weight: bold;">
                            ${customer.name.charAt(0).toUpperCase()}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold text-dark">${customer.name}</div>
                        ${customer.phone ? `
                            <small class="text-muted">
                                <i class="fas fa-phone me-1"></i>${customer.phone}
                            </small>
                        ` : '<small class="text-muted">لا يوجد رقم هاتف</small>'}
                        ${customer.email ? `
                            <br><small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>${customer.email}
                            </small>
                        ` : ''}
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                </div>
            </div>
        `;
    });

    customerSuggestions.innerHTML = html;
    customerSuggestions.style.display = 'block';
}

function selectCustomer(customerId, customerName, customerPhone) {
    selectedCustomerId = customerId;

    // إخفاء حقل البحث وإظهار العميل المحدد
    document.getElementById('customer_search').value = '';
    document.getElementById('selected_customer_id').value = customerId;
    document.getElementById('customer_suggestions').style.display = 'none';

    // عرض معلومات العميل المحدد
    const selectedDisplay = document.getElementById('selected_customer_display');
    const selectedName = document.getElementById('selected_customer_name');
    const selectedPhone = document.getElementById('selected_customer_phone');

    if (selectedDisplay && selectedName) {
        selectedName.textContent = customerName;
        if (selectedPhone) {
            selectedPhone.textContent = customerPhone || 'لا يوجد رقم هاتف';
        }
        selectedDisplay.style.display = 'block';
    }

    console.log('Customer selected:', {
        id: customerId,
        name: customerName,
        phone: customerPhone
    });

    // إظهار رسالة تأكيد قصيرة
    showAlert(`تم تحديد العميل: ${customerName}`, 'success');
}

function clearCustomer() {
    selectedCustomerId = null;
    document.getElementById('customer_search').value = '';
    document.getElementById('selected_customer_id').value = '';
    document.getElementById('customer_suggestions').style.display = 'none';

    // مسح القائمة المنسدلة أيضاً
    const dropdown = document.getElementById('customer_select_dropdown');
    if (dropdown) {
        dropdown.value = '';
    }

    // إخفاء عرض العميل المحدد
    const selectedDisplay = document.getElementById('selected_customer_display');
    if (selectedDisplay) {
        selectedDisplay.style.display = 'none';
    }

    console.log('Customer selection cleared');
}

// دالة لإظهار خيار إضافة عميل جديد
function showAddCustomerOption(customerName) {
    const result = confirm(`هل تريد إضافة عميل جديد باسم "${customerName}"؟\n\nسيتم إنشاء العميل وتحديده تلقائياً للجلسة.`);

    if (result) {
        // إخفاء قائمة الاقتراحات
        document.getElementById('customer_suggestions').style.display = 'none';

        // إظهار مؤشر التحميل
        const searchInput = document.getElementById('customer_search');
        const originalPlaceholder = searchInput.placeholder;
        searchInput.placeholder = 'جاري إضافة العميل...';
        searchInput.disabled = true;

        // إرسال طلب إضافة العميل
        fetch('api/add-customer-quick.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: customerName.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديد العميل الجديد
                selectCustomer(data.customer_id, data.customer_name, '');

                // إظهار رسالة نجاح
                showAlert('تم إضافة العميل بنجاح!', 'success');
            } else {
                showAlert('خطأ في إضافة العميل: ' + (data.message || 'خطأ غير معروف'), 'error');
            }
        })
        .catch(error => {
            console.error('Error adding customer:', error);
            showAlert('حدث خطأ في الاتصال', 'error');
        })
        .finally(() => {
            // إعادة تفعيل حقل البحث
            searchInput.placeholder = originalPlaceholder;
            searchInput.disabled = false;
        });
    }
}

// دالة لإظهار التنبيهات
function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // البحث عن container للتنبيهات أو إنشاؤه
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '1055';
        alertContainer.style.marginTop = '80px';
        document.body.appendChild(alertContainer);
    }

    // إضافة التنبيه
    const alertElement = document.createElement('div');
    alertElement.innerHTML = alertHtml;
    alertContainer.appendChild(alertElement.firstElementChild);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function showSearchLoading() {
    const customerSuggestions = document.getElementById('customer_suggestions');
    customerSuggestions.innerHTML = `
        <div class="p-3 text-center text-muted">
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            جاري البحث...
        </div>
    `;
    customerSuggestions.style.display = 'block';
}

function showWelcomeMessage() {
    const customerSuggestions = document.getElementById('customer_suggestions');
    customerSuggestions.innerHTML = `
        <div class="p-4 text-center">
            <div class="mb-3">
                <i class="fas fa-users fa-3x text-primary mb-2"></i>
                <h6 class="text-primary">اختر عميل للجلسة</h6>
            </div>
            <div class="text-muted mb-3">
                <small>
                    <i class="fas fa-search me-1"></i>
                    ابدأ بكتابة اسم العميل أو رقم الهاتف للبحث
                </small>
            </div>
            <div class="text-muted mb-3">
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    يمكنك أيضاً ترك الحقل فارغاً لبدء جلسة بدون عميل
                </small>
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-sm btn-outline-info" onclick="testCustomerSearch()">
                    <i class="fas fa-bug me-1"></i>اختبار البحث
                </button>
            </div>
        </div>
    `;
    customerSuggestions.style.display = 'block';

    // إخفاء الرسالة عند البدء في الكتابة
    const searchInput = document.getElementById('customer_search');
    if (searchInput) {
        const hideWelcome = () => {
            customerSuggestions.style.display = 'none';
            searchInput.removeEventListener('focus', hideWelcome);
            searchInput.removeEventListener('input', hideWelcome);
        };

        searchInput.addEventListener('focus', hideWelcome);
        searchInput.addEventListener('input', hideWelcome);
    }
}

// دالة التبديل بين البحث والقائمة المنسدلة
function toggleCustomerSelectionMode() {
    const searchMode = document.querySelector('.input-group.position-relative');
    const selectMode = document.getElementById('customer_select_mode');
    const toggleButton = event.target;

    if (selectMode.style.display === 'none') {
        // التبديل إلى القائمة المنسدلة
        searchMode.style.display = 'none';
        selectMode.style.display = 'block';
        toggleButton.innerHTML = '<i class="fas fa-search me-1"></i>استخدام البحث بدلاً من القائمة';

        // إعداد مستمع الأحداث للقائمة المنسدلة
        const dropdown = document.getElementById('customer_select_dropdown');
        dropdown.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const customerId = selectedOption.value;
                const customerName = selectedOption.dataset.name;
                const customerPhone = selectedOption.dataset.phone || '';

                // تحديد العميل
                selectCustomerFromDropdown(customerId, customerName, customerPhone);
            } else {
                // مسح التحديد
                clearCustomer();
            }
        });
    } else {
        // التبديل إلى البحث
        searchMode.style.display = 'block';
        selectMode.style.display = 'none';
        toggleButton.innerHTML = '<i class="fas fa-list me-1"></i>استخدام القائمة المنسدلة بدلاً من البحث';

        // مسح القائمة المنسدلة
        document.getElementById('customer_select_dropdown').value = '';
    }
}

// دالة تحديد العميل من القائمة المنسدلة
function selectCustomerFromDropdown(customerId, customerName, customerPhone) {
    selectedCustomerId = customerId;

    // تحديث الحقول المخفية
    document.getElementById('selected_customer_id').value = customerId;

    // إظهار معلومات العميل المحدد
    const selectedDisplay = document.getElementById('selected_customer_display');
    const selectedName = document.getElementById('selected_customer_name');
    const selectedPhoneElement = document.getElementById('selected_customer_phone');

    if (selectedDisplay && selectedName) {
        selectedName.textContent = customerName;
        if (selectedPhoneElement) {
            selectedPhoneElement.textContent = customerPhone || 'لا يوجد رقم هاتف';
        }
        selectedDisplay.style.display = 'block';
    }

    console.log('Customer selected from dropdown:', {
        id: customerId,
        name: customerName,
        phone: customerPhone
    });

    // إظهار رسالة تأكيد
    showAlert(`تم تحديد العميل: ${customerName}`, 'success');
}

// دالة اختبار البحث
function testCustomerSearch() {
    console.log('بدء اختبار البحث...');

    const customerSuggestions = document.getElementById('customer_suggestions');
    customerSuggestions.innerHTML = `
        <div class="p-3 text-center">
            <div class="spinner-border text-primary mb-2" role="status"></div>
            <br><small>جاري اختبار البحث...</small>
        </div>
    `;

    // اختبار البحث بكلمة "أحمد"
    fetch('api/search-customers.php?q=أحمد')
        .then(response => response.text())
        .then(text => {
            console.log('نتيجة اختبار البحث:', text);

            try {
                const data = JSON.parse(text);

                if (data && data.success === true && Array.isArray(data.customers)) {
                    customerSuggestions.innerHTML = `
                        <div class="p-3 text-center">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>البحث يعمل بشكل صحيح!</strong>
                                <br><small>تم العثور على ${data.customers.length} نتيجة</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                                <i class="fas fa-arrow-left me-1"></i>العودة
                            </button>
                        </div>
                    `;
                } else if (Array.isArray(data)) {
                    customerSuggestions.innerHTML = `
                        <div class="p-3 text-center">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>البحث يعمل بشكل صحيح!</strong>
                                <br><small>تم العثور على ${data.length} نتيجة</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                                <i class="fas fa-arrow-left me-1"></i>العودة
                            </button>
                        </div>
                    `;
                } else if (data && data.success === false) {
                    customerSuggestions.innerHTML = `
                        <div class="p-3 text-center">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>خطأ في البحث:</strong>
                                <br><small>${data.message}</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                                <i class="fas fa-arrow-left me-1"></i>العودة
                            </button>
                        </div>
                    `;
                } else {
                    customerSuggestions.innerHTML = `
                        <div class="p-3 text-center">
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>تنسيق استجابة غير متوقع</strong>
                                <br><small>تحقق من وحدة التحكم للمزيد من التفاصيل</small>
                            </div>
                            <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                                <i class="fas fa-arrow-left me-1"></i>العودة
                            </button>
                        </div>
                    `;
                }
            } catch (e) {
                customerSuggestions.innerHTML = `
                    <div class="p-3 text-center">
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>خطأ في تحليل البيانات</strong>
                            <br><small>${e.message}</small>
                            <br><pre style="font-size: 10px; max-height: 100px; overflow-y: auto;">${text}</pre>
                        </div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                            <i class="fas fa-arrow-left me-1"></i>العودة
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('خطأ في اختبار البحث:', error);
            customerSuggestions.innerHTML = `
                <div class="p-3 text-center">
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        <strong>خطأ في الاتصال</strong>
                        <br><small>${error.message}</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="showWelcomeMessage()">
                        <i class="fas fa-arrow-left me-1"></i>العودة
                    </button>
                </div>
            `;
        });
}


</script>

<!-- إضافة JavaScript لإدارة الورديات -->
<?php if (isset($_SESSION['employee_id'])): ?>
<script src="assets/js/shift-management.js"></script>
<script>
// إضافة مؤشر الوقت الحالي في الداشبورد
document.addEventListener('DOMContentLoaded', function() {
    // إضافة عرض الوقت الحالي
    const timeDisplay = document.createElement('div');
    timeDisplay.id = 'current-time-display';
    timeDisplay.className = 'text-muted small';
    timeDisplay.style.position = 'fixed';
    timeDisplay.style.top = '10px';
    timeDisplay.style.left = '10px';
    timeDisplay.style.background = 'rgba(0,0,0,0.7)';
    timeDisplay.style.color = 'white';
    timeDisplay.style.padding = '5px 10px';
    timeDisplay.style.borderRadius = '5px';
    timeDisplay.style.fontSize = '12px';
    timeDisplay.style.zIndex = '1000';

    document.body.appendChild(timeDisplay);
});
</script>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
<?php
/**
 * سكريبت تحديث جميع الصفحات لاستخدام القائمة الجانبية
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>تحديث الصفحات للقائمة الجانبية</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center text-primary mb-4'>
        <i class='fas fa-exchange-alt'></i>
        تحديث الصفحات للقائمة الجانبية
    </h1>";

// قائمة الصفحات المراد تحديثها
$pages_to_update = [
    'client/dashboard.php',
    'client/sessions.php', 
    'client/devices.php',
    'client/customers.php',
    'client/cafeteria.php',
    'client/orders.php',
    'client/employees.php',
    'client/attendance.php',
    'client/shifts.php',
    'client/invoices.php',
    'client/finances.php',
    'client/reports.php',
    'client/settings.php',
    'client/profile.php',
    'client/notifications.php',
    'client/employee_shift_start.php',
    'client/employee_shift_reports.php',
    'client/test_shift_system.php'
];

$updated_count = 0;
$error_count = 0;

echo "<div class='card mb-4'>
    <div class='card-header bg-info text-white'>
        <h5><i class='fas fa-list'></i> الصفحات المراد تحديثها</h5>
    </div>
    <div class='card-body'>";

foreach ($pages_to_update as $page_path) {
    echo "<div class='mb-3 p-3 border rounded'>";
    echo "<h6 class='text-primary'><i class='fas fa-file'></i> " . basename($page_path) . "</h6>";
    
    if (!file_exists($page_path)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> الملف غير موجود</div>";
        echo "</div>";
        continue;
    }
    
    try {
        // قراءة محتوى الملف
        $content = file_get_contents($page_path);
        
        if ($content === false) {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> فشل في قراءة الملف</div>";
            $error_count++;
            echo "</div>";
            continue;
        }
        
        // التحقق من وجود include للـ header القديم
        $has_old_header = strpos($content, "include 'includes/header.php'") !== false ||
                         strpos($content, 'include "includes/header.php"') !== false ||
                         strpos($content, "require 'includes/header.php'") !== false ||
                         strpos($content, 'require "includes/header.php"') !== false;
        
        $has_old_footer = strpos($content, "include 'includes/footer.php'") !== false ||
                         strpos($content, 'include "includes/footer.php"') !== false;
        
        if (!$has_old_header && !$has_old_footer) {
            echo "<div class='alert alert-info'><i class='fas fa-info'></i> الملف لا يحتوي على header/footer قديم</div>";
            echo "</div>";
            continue;
        }
        
        // إنشاء نسخة احتياطية
        $backup_path = $page_path . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_path, $content);
        echo "<div class='alert alert-info'><i class='fas fa-save'></i> تم إنشاء نسخة احتياطية: " . basename($backup_path) . "</div>";
        
        // استبدال includes
        $updated_content = $content;
        
        // استبدال header
        $header_patterns = [
            "include 'includes/header.php';",
            'include "includes/header.php";',
            "require 'includes/header.php';",
            'require "includes/header.php";',
            "include_once 'includes/header.php';",
            'include_once "includes/header.php";'
        ];
        
        foreach ($header_patterns as $pattern) {
            $updated_content = str_replace($pattern, "include 'includes/header_sidebar_only.php';", $updated_content);
        }
        
        // استبدال footer
        $footer_patterns = [
            "include 'includes/footer.php';",
            'include "includes/footer.php";',
            "require 'includes/footer.php';",
            'require "includes/footer.php";'
        ];
        
        foreach ($footer_patterns as $pattern) {
            $updated_content = str_replace($pattern, "include 'includes/footer_sidebar_only.php';", $updated_content);
        }
        
        // إزالة أي container-fluid أو row div إضافية قد تتضارب مع التصميم الجديد
        $updated_content = preg_replace('/<div class="container-fluid">\s*<div class="row">/i', '', $updated_content);
        $updated_content = preg_replace('/<\/div>\s*<\/div>\s*(?=<\?php\s+include.*footer)/i', '', $updated_content);
        
        // إزالة sidebar include القديم إذا كان موجوداً
        $sidebar_patterns = [
            "<?php include 'includes/sidebar.php'; ?>",
            '<?php include "includes/sidebar.php"; ?>',
            "include 'includes/sidebar.php';",
            'include "includes/sidebar.php";'
        ];
        
        foreach ($sidebar_patterns as $pattern) {
            $updated_content = str_replace($pattern, '', $updated_content);
        }
        
        // كتابة المحتوى المحدث
        if (file_put_contents($page_path, $updated_content) !== false) {
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تحديث الملف بنجاح</div>";
            $updated_count++;
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> فشل في كتابة الملف المحدث</div>";
            $error_count++;
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ: " . $e->getMessage() . "</div>";
        $error_count++;
    }
    
    echo "</div>";
}

echo "</div></div>";

// النتيجة النهائية
echo "<div class='card border-primary'>
    <div class='card-header bg-primary text-white text-center'>
        <h3><i class='fas fa-chart-pie'></i> نتائج التحديث</h3>
    </div>
    <div class='card-body text-center'>";

$total_pages = count($pages_to_update);
$success_rate = ($updated_count / $total_pages) * 100;

if ($success_rate >= 90) {
    echo "<div class='alert alert-success' style='font-size: 1.2em;'>
        <i class='fas fa-check-circle fa-2x mb-2'></i><br>
        <strong>ممتاز! تم تحديث $updated_count من $total_pages صفحة</strong><br>
        (" . round($success_rate) . "% نجح)
    </div>";
} elseif ($success_rate >= 70) {
    echo "<div class='alert alert-warning' style='font-size: 1.2em;'>
        <i class='fas fa-exclamation-circle fa-2x mb-2'></i><br>
        <strong>جيد! تم تحديث $updated_count من $total_pages صفحة</strong><br>
        (" . round($success_rate) . "% نجح، $error_count أخطاء)
    </div>";
} else {
    echo "<div class='alert alert-danger' style='font-size: 1.2em;'>
        <i class='fas fa-times-circle fa-2x mb-2'></i><br>
        <strong>يحتاج مراجعة! تم تحديث $updated_count من $total_pages صفحة فقط</strong><br>
        (" . round($success_rate) . "% نجح، $error_count أخطاء)
    </div>";
}

echo "<div class='row mt-4'>
    <div class='col-md-4'>
        <div class='card border-success'>
            <div class='card-body text-center'>
                <h3 class='text-success'>$updated_count</h3>
                <p>صفحات محدثة</p>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card border-danger'>
            <div class='card-body text-center'>
                <h3 class='text-danger'>$error_count</h3>
                <p>أخطاء</p>
            </div>
        </div>
    </div>
    <div class='col-md-4'>
        <div class='card border-info'>
            <div class='card-body text-center'>
                <h3 class='text-info'>" . round($success_rate) . "%</h3>
                <p>معدل النجاح</p>
            </div>
        </div>
    </div>
</div>";

echo "<div class='mt-4'>
    <h5>الخطوات التالية:</h5>
    <div class='alert alert-info'>
        <ul class='mb-0'>
            <li>تصفح الصفحات المحدثة للتأكد من عملها بشكل صحيح</li>
            <li>في حالة وجود مشاكل، يمكن استعادة النسخ الاحتياطية</li>
            <li>تخصيص تصميم القائمة الجانبية حسب الحاجة</li>
            <li>إضافة أي صفحات جديدة لم يتم تحديثها</li>
        </ul>
    </div>
    
    <div class='text-center'>
        <a href='client/dashboard.php' class='btn btn-primary btn-lg me-2'>
            <i class='fas fa-home'></i> اختبار لوحة التحكم
        </a>
        <a href='client/sessions.php' class='btn btn-success btn-lg me-2'>
            <i class='fas fa-play'></i> اختبار الجلسات
        </a>
        <a href='client/employees.php' class='btn btn-info btn-lg'>
            <i class='fas fa-users'></i> اختبار الموظفين
        </a>
    </div>
</div>";

echo "</div></div>";

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>

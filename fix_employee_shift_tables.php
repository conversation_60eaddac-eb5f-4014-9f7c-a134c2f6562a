<?php
/**
 * إصلاح مشكلة تضارب جداول الشيفت
 * تاريخ الإنشاء: 2025-08-01
 */

require_once 'config/database.php';

// تعيين الترميز
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح جداول الشيفت</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 إصلاح جداول نظام الشيفت</h1>";

try {
    // الخطوة 1: التحقق من الجداول الموجودة
    echo "<div class='step'><h2>الخطوة 1: فحص الجداول الموجودة</h2>";
    
    $tables_to_check = ['employee_shifts', 'employee_shift_activities', 'employee_shift_summaries'];
    $existing_tables = [];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
            echo "<div class='warning'>⚠️ الجدول $table موجود مسبقاً</div>";
            
            // فحص هيكل الجدول
            $columns = $pdo->query("DESCRIBE $table")->fetchAll();
            $has_client_id = false;
            foreach ($columns as $column) {
                if ($column['Field'] == 'client_id') {
                    $has_client_id = true;
                    break;
                }
            }
            
            if (!$has_client_id && $table == 'employee_shifts') {
                echo "<div class='error'>❌ الجدول $table لا يحتوي على عمود client_id - يحتاج إعادة إنشاء</div>";
            } else {
                echo "<div class='success'>✅ الجدول $table له الهيكل الصحيح</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ الجدول $table غير موجود - سيتم إنشاؤه</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 2: إعادة تسمية الجداول القديمة
    echo "<div class='step'><h2>الخطوة 2: إعادة تسمية الجداول القديمة</h2>";
    
    if (in_array('employee_shifts', $existing_tables)) {
        try {
            // التحقق من وجود عمود client_id
            $columns = $pdo->query("DESCRIBE employee_shifts")->fetchAll();
            $has_client_id = false;
            foreach ($columns as $column) {
                if ($column['Field'] == 'client_id') {
                    $has_client_id = true;
                    break;
                }
            }
            
            if (!$has_client_id) {
                $pdo->exec("DROP TABLE IF EXISTS employee_shifts_backup");
                $pdo->exec("RENAME TABLE employee_shifts TO employee_shifts_backup");
                echo "<div class='success'>✅ تم نقل الجدول القديم إلى employee_shifts_backup</div>";
            } else {
                echo "<div class='info'>ℹ️ الجدول employee_shifts له الهيكل الصحيح</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في نقل الجدول: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 3: إنشاء جدول الشيفت الجديد
    echo "<div class='step'><h2>الخطوة 3: إنشاء جدول الشيفت الجديد</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shifts (
        shift_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        shift_end_time TIMESTAMP NULL,
        planned_start_time TIME NULL COMMENT 'الوقت المخطط لبدء الشيفت',
        planned_end_time TIME NULL COMMENT 'الوقت المخطط لانتهاء الشيفت',
        actual_duration_minutes INT DEFAULT 0,
        break_start_time TIMESTAMP NULL,
        break_end_time TIMESTAMP NULL,
        break_duration_minutes INT DEFAULT 0,
        shift_status ENUM('active', 'completed', 'cancelled', 'on_break') DEFAULT 'active',
        shift_notes TEXT NULL,
        location_info VARCHAR(255) NULL COMMENT 'معلومات الموقع أو المحطة',
        ip_address VARCHAR(45) NULL,
        device_info TEXT NULL COMMENT 'معلومات الجهاز المستخدم',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_employee_shift (employee_id, shift_status),
        INDEX idx_client_shift (client_id, shift_start_time),
        INDEX idx_shift_status (shift_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shifts الجديد بنجاح</div>";
    echo "</div>";
    
    // الخطوة 4: إنشاء جدول الأنشطة
    echo "<div class='step'><h2>الخطوة 4: إنشاء جدول الأنشطة</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_activities (
        activity_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        activity_type ENUM(
            'login', 'logout', 'session_start', 'session_end', 'session_update',
            'customer_add', 'customer_edit', 'customer_delete',
            'device_update', 'device_maintenance',
            'cafeteria_order', 'cafeteria_payment',
            'inventory_update', 'inventory_check',
            'financial_transaction', 'report_view',
            'settings_change', 'employee_action',
            'break_start', 'break_end',
            'system_access', 'page_visit',
            'other'
        ) NOT NULL,
        activity_title VARCHAR(255) NOT NULL,
        activity_description TEXT NULL,
        target_type VARCHAR(50) NULL COMMENT 'نوع الهدف (session, customer, device, etc.)',
        target_id INT NULL COMMENT 'معرف الهدف',
        old_values JSON NULL COMMENT 'القيم القديمة قبل التغيير',
        new_values JSON NULL COMMENT 'القيم الجديدة بعد التغيير',
        activity_data JSON NULL COMMENT 'بيانات إضافية للنشاط',
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        page_url VARCHAR(500) NULL,
        http_method VARCHAR(10) NULL,
        response_status INT NULL,
        execution_time_ms INT NULL COMMENT 'وقت تنفيذ العملية بالميلي ثانية',
        activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_shift_activities (shift_id, activity_timestamp),
        INDEX idx_employee_activities (employee_id, activity_timestamp),
        INDEX idx_activity_type (activity_type),
        INDEX idx_target (target_type, target_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_activities بنجاح</div>";
    echo "</div>";
    
    // الخطوة 5: إنشاء جدول الملخصات
    echo "<div class='step'><h2>الخطوة 5: إنشاء جدول الملخصات</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_summaries (
        summary_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL UNIQUE,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_date DATE NOT NULL,
        total_duration_minutes INT NOT NULL,
        break_duration_minutes INT DEFAULT 0,
        productive_time_minutes INT NOT NULL,
        total_activities INT DEFAULT 0,
        sessions_handled INT DEFAULT 0,
        customers_served INT DEFAULT 0,
        revenue_generated DECIMAL(10,2) DEFAULT 0.00,
        activities_breakdown JSON NULL COMMENT 'تفصيل الأنشطة حسب النوع',
        performance_metrics JSON NULL COMMENT 'مقاييس الأداء',
        shift_rating ENUM('excellent', 'good', 'average', 'poor') NULL,
        manager_notes TEXT NULL,
        auto_generated_summary TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_employee_summaries (employee_id, shift_date),
        INDEX idx_client_summaries (client_id, shift_date),
        INDEX idx_shift_date (shift_date),
        INDEX idx_shift_rating (shift_rating)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_summaries بنجاح</div>";
    echo "</div>";
    
    // الخطوة 6: إنشاء جدول الإعدادات
    echo "<div class='step'><h2>الخطوة 6: إنشاء جدول الإعدادات</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT NOT NULL,
        setting_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_client_setting (client_id, setting_key),
        INDEX idx_client_settings (client_id, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_system_settings بنجاح</div>";
    echo "</div>";
    
    // الخطوة 7: تحديث جدول الموظفين
    echo "<div class='step'><h2>الخطوة 7: تحديث جدول الموظفين</h2>";
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS current_shift_id INT NULL");
        echo "<div class='success'>✅ تم إضافة عمود current_shift_id</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود current_shift_id موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS shift_status ENUM('off_duty', 'on_duty', 'on_break') DEFAULT 'off_duty'");
        echo "<div class='success'>✅ تم إضافة عمود shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود shift_status موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS last_activity_time TIMESTAMP NULL");
        echo "<div class='success'>✅ تم إضافة عمود last_activity_time</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود last_activity_time موجود مسبقاً</div>";
    }
    
    echo "</div>";
    
    // الخطوة 8: إنشاء Views
    echo "<div class='step'><h2>الخطوة 8: إنشاء Views</h2>";
    
    $sql = "
    CREATE OR REPLACE VIEW active_employee_shifts AS
    SELECT 
        es.shift_id,
        es.employee_id,
        e.name as employee_name,
        e.role as employee_role,
        es.client_id,
        c.business_name,
        es.shift_start_time,
        es.planned_end_time,
        es.shift_status,
        es.location_info,
        TIMESTAMPDIFF(MINUTE, es.shift_start_time, NOW()) as current_duration_minutes,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities,
        (SELECT activity_timestamp FROM employee_shift_activities WHERE shift_id = es.shift_id ORDER BY activity_timestamp DESC LIMIT 1) as last_activity_time
    FROM employee_shifts es
    JOIN employees e ON es.employee_id = e.id
    JOIN clients c ON es.client_id = c.client_id
    WHERE es.shift_status IN ('active', 'on_break')";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء view active_employee_shifts</div>";
    
    $sql = "
    CREATE OR REPLACE VIEW shift_quick_stats AS
    SELECT 
        'active_shifts' as stat_name,
        COUNT(*) as stat_value,
        'عدد الشيفت النشط حالياً' as stat_description
    FROM employee_shifts 
    WHERE shift_status IN ('active', 'on_break')
    
    UNION ALL
    
    SELECT 
        'total_activities_today' as stat_name,
        COUNT(*) as stat_value,
        'إجمالي الأنشطة اليوم' as stat_description
    FROM employee_shift_activities 
    WHERE DATE(activity_timestamp) = CURDATE()
    
    UNION ALL
    
    SELECT 
        'employees_on_duty' as stat_name,
        COUNT(*) as stat_value,
        'عدد الموظفين في الخدمة' as stat_description
    FROM employees 
    WHERE shift_status = 'on_duty'
    
    UNION ALL
    
    SELECT 
        'completed_shifts_today' as stat_name,
        COUNT(*) as stat_value,
        'الشيفت المكتملة اليوم' as stat_description
    FROM employee_shifts 
    WHERE DATE(shift_start_time) = CURDATE() 
    AND shift_status = 'completed'";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء view shift_quick_stats</div>";
    echo "</div>";
    
    // الخطوة 9: إدراج الإعدادات الافتراضية
    echo "<div class='step'><h2>الخطوة 9: إدراج الإعدادات الافتراضية</h2>";
    
    $settings = [
        ['auto_track_activities', 'true', 'تفعيل تتبع الأنشطة التلقائي'],
        ['require_shift_notes', 'false', 'إجبار كتابة ملاحظات عند انتهاء الشيفت'],
        ['max_shift_duration_hours', '12', 'الحد الأقصى لمدة الشيفت بالساعات'],
        ['break_reminder_minutes', '240', 'تذكير بالاستراحة كل X دقيقة']
    ];
    
    $clients_stmt = $pdo->query("SELECT client_id FROM clients WHERE is_active = 1");
    $clients = $clients_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($clients as $client_id) {
        foreach ($settings as $setting) {
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) VALUES (?, ?, ?, ?)");
                $stmt->execute([$client_id, $setting[0], $setting[1], $setting[2]]);
            } catch (Exception $e) {
                // تجاهل الأخطاء للإعدادات الموجودة
            }
        }
    }
    
    echo "<div class='success'>✅ تم إدراج الإعدادات الافتراضية لـ " . count($clients) . " عميل</div>";
    echo "</div>";
    
    // رسالة النجاح النهائية
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>
        🎉 <strong>تم إصلاح نظام الشيفت بنجاح!</strong><br>
        يمكنك الآن استخدام النظام بدون مشاكل.
    </div>";
    
    echo "<div class='info'>
        <h3>الخطوات التالية:</h3>
        <ul>
            <li>اختبار النظام من صفحة <a href='client/test_shift_system.php'>اختبار الشيفت</a></li>
            <li>بدء الشيفت من صفحة <a href='client/employee_shift_start.php'>إدارة الشيفت</a></li>
            <li>عرض التقارير من صفحة <a href='client/employee_shift_reports.php'>تقارير الشيفت</a></li>
        </ul>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div></body></html>";
?>

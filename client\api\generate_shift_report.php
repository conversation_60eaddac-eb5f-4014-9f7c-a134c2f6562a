<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: ../login.php');
    exit;
}

$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$shift_id = $_GET['shift_id'] ?? 0;

if (!$shift_id) {
    die('معرف الشيفت مطلوب');
}

try {
    // جلب بيانات الشيفت
    $shift_stmt = $pdo->prepare("
        SELECT 
            es.*,
            e.name as employee_name,
            e.role as employee_role,
            e.phone as employee_phone,
            c.business_name,
            c.owner_name
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        JOIN clients c ON es.client_id = c.client_id
        WHERE es.shift_id = ? AND es.client_id = ?
    ");
    $shift_stmt->execute([$shift_id, $client_id]);
    $shift = $shift_stmt->fetch();
    
    if (!$shift) {
        die('الشيفت غير موجود');
    }
    
    // جلب أنشطة الشيفت
    $activities_stmt = $pdo->prepare("
        SELECT 
            activity_type,
            activity_title,
            activity_description,
            target_type,
            target_id,
            activity_timestamp,
            activity_data,
            old_values,
            new_values
        FROM employee_shift_activities
        WHERE shift_id = ?
        ORDER BY activity_timestamp ASC
    ");
    $activities_stmt->execute([$shift_id]);
    $activities = $activities_stmt->fetchAll();
    
    // حساب الإحصائيات
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_activities,
            COUNT(CASE WHEN activity_type = 'session_start' THEN 1 END) as sessions_started,
            COUNT(CASE WHEN activity_type = 'session_end' THEN 1 END) as sessions_ended,
            COUNT(CASE WHEN activity_type = 'customer_add' THEN 1 END) as customers_added,
            COUNT(CASE WHEN activity_type = 'customer_edit' THEN 1 END) as customers_edited,
            COUNT(CASE WHEN activity_type = 'cafeteria_order' THEN 1 END) as orders_created,
            COUNT(CASE WHEN activity_type = 'page_visit' THEN 1 END) as pages_visited,
            MIN(activity_timestamp) as first_activity,
            MAX(activity_timestamp) as last_activity
        FROM employee_shift_activities
        WHERE shift_id = ?
    ");
    $stats_stmt->execute([$shift_id]);
    $stats = $stats_stmt->fetch();
    
    // تجميع الأنشطة حسب النوع
    $activity_types_stmt = $pdo->prepare("
        SELECT 
            activity_type,
            COUNT(*) as count
        FROM employee_shift_activities
        WHERE shift_id = ?
        GROUP BY activity_type
        ORDER BY count DESC
    ");
    $activity_types_stmt->execute([$shift_id]);
    $activity_types = $activity_types_stmt->fetchAll();
    
    // حساب مدة الشيفت
    $duration_minutes = $shift['actual_duration_minutes'] ?: 0;
    $hours = floor($duration_minutes / 60);
    $minutes = $duration_minutes % 60;
    $duration_text = $hours . ' ساعة و ' . $minutes . ' دقيقة';
    
    // إعداد الترويسة للطباعة
    header('Content-Type: text/html; charset=utf-8');
    
    ?>
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير الشيفت - <?php echo htmlspecialchars($shift['employee_name']); ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .report-header { border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
            .report-section { margin-bottom: 30px; }
            .activity-item { border-left: 3px solid #28a745; padding-left: 15px; margin-bottom: 15px; }
            .activity-item.session { border-left-color: #007bff; }
            .activity-item.customer { border-left-color: #ffc107; }
            .activity-item.cafeteria { border-left-color: #6f42c1; }
            .activity-item.system { border-left-color: #6c757d; }
            @media print {
                .no-print { display: none !important; }
                .page-break { page-break-before: always; }
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <!-- أزرار الطباعة والتصدير -->
            <div class="no-print mb-3">
                <div class="d-flex justify-content-between">
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button onclick="exportToPDF()" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> تصدير PDF
                        </button>
                        <button onclick="window.close()" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <div>
                        <small class="text-muted">تم إنشاء التقرير في: <?php echo date('Y-m-d H:i:s'); ?></small>
                    </div>
                </div>
            </div>
            
            <!-- ترويسة التقرير -->
            <div class="report-header text-center">
                <h1 class="text-primary">تقرير الشيفت</h1>
                <h3><?php echo htmlspecialchars($shift['business_name']); ?></h3>
                <p class="text-muted">تقرير مفصل عن شيفت الموظف وجميع الأنشطة المنجزة</p>
            </div>
            
            <!-- معلومات الشيفت الأساسية -->
            <div class="report-section">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-info-circle"></i>
                    معلومات الشيفت
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr><td><strong>اسم الموظف:</strong></td><td><?php echo htmlspecialchars($shift['employee_name']); ?></td></tr>
                            <tr><td><strong>الدور:</strong></td><td><?php echo htmlspecialchars($shift['employee_role']); ?></td></tr>
                            <tr><td><strong>رقم الهاتف:</strong></td><td><?php echo htmlspecialchars($shift['employee_phone']); ?></td></tr>
                            <tr><td><strong>تاريخ الشيفت:</strong></td><td><?php echo date('Y-m-d', strtotime($shift['shift_start_time'])); ?></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr><td><strong>وقت البدء:</strong></td><td><?php echo date('H:i', strtotime($shift['shift_start_time'])); ?></td></tr>
                            <tr><td><strong>وقت الانتهاء:</strong></td><td><?php echo $shift['shift_end_time'] ? date('H:i', strtotime($shift['shift_end_time'])) : 'جاري...'; ?></td></tr>
                            <tr><td><strong>إجمالي المدة:</strong></td><td><?php echo $duration_text; ?></td></tr>
                            <tr><td><strong>الموقع:</strong></td><td><?php echo htmlspecialchars($shift['location_info'] ?: 'غير محدد'); ?></td></tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات الأداء -->
            <div class="report-section">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات الأداء
                </h4>
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo $stats['total_activities']; ?></h3>
                                <p class="card-text">إجمالي الأنشطة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $stats['sessions_started']; ?></h3>
                                <p class="card-text">جلسات مبدوءة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo $stats['sessions_ended']; ?></h3>
                                <p class="card-text">جلسات منتهية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-warning"><?php echo $stats['customers_added']; ?></h3>
                                <p class="card-text">عملاء جدد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-secondary"><?php echo $stats['orders_created']; ?></h3>
                                <p class="card-text">طلبات كافيتيريا</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="text-dark"><?php echo $stats['pages_visited']; ?></h3>
                                <p class="card-text">صفحات مزارة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تفصيل الأنشطة حسب النوع -->
            <?php if (!empty($activity_types)): ?>
            <div class="report-section">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-pie-chart"></i>
                    تفصيل الأنشطة حسب النوع
                </h4>
                <div class="row">
                    <?php foreach ($activity_types as $type): ?>
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title"><?php echo $type['count']; ?></h5>
                                <p class="card-text"><?php echo getActivityTypeLabel($type['activity_type']); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- سجل الأنشطة التفصيلي -->
            <?php if (!empty($activities)): ?>
            <div class="report-section page-break">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-list"></i>
                    سجل الأنشطة التفصيلي (<?php echo count($activities); ?> نشاط)
                </h4>
                
                <?php foreach ($activities as $activity): ?>
                <div class="activity-item <?php echo getActivityClass($activity['activity_type']); ?>">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                <?php echo getActivityTypeIcon($activity['activity_type']); ?>
                                <?php echo htmlspecialchars($activity['activity_title']); ?>
                            </h6>
                            <?php if ($activity['activity_description']): ?>
                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($activity['activity_description']); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($activity['target_type'] && $activity['target_id']): ?>
                            <small class="text-info">
                                <i class="fas fa-link"></i>
                                <?php echo htmlspecialchars($activity['target_type']); ?> #<?php echo $activity['target_id']; ?>
                            </small>
                            <?php endif; ?>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">
                                <?php echo date('H:i:s', strtotime($activity['activity_timestamp'])); ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            
            <!-- ملاحظات الشيفت -->
            <?php if ($shift['shift_notes']): ?>
            <div class="report-section">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-sticky-note"></i>
                    ملاحظات الشيفت
                </h4>
                <div class="alert alert-info">
                    <?php echo nl2br(htmlspecialchars($shift['shift_notes'])); ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- تذييل التقرير -->
            <div class="report-section text-center border-top pt-3">
                <p class="text-muted">
                    تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة محلات البلايستيشن<br>
                    التاريخ والوقت: <?php echo date('Y-m-d H:i:s'); ?>
                </p>
            </div>
        </div>
        
        <script>
        function exportToPDF() {
            window.print();
        }
        </script>
    </body>
    </html>
    
    <?php
    
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

function getActivityTypeLabel($type) {
    $labels = [
        'login' => 'تسجيل دخول الشيفت',
        'logout' => 'تسجيل خروج الشيفت',
        'session_start' => 'بدء جلسة',
        'session_end' => 'إنهاء جلسة',
        'session_update' => 'تحديث جلسة',
        'customer_add' => 'إضافة عميل',
        'customer_edit' => 'تعديل عميل',
        'customer_delete' => 'حذف عميل',
        'cafeteria_order' => 'طلب كافيتيريا',
        'cafeteria_payment' => 'دفع طلب',
        'device_update' => 'تحديث جهاز',
        'page_visit' => 'زيارة صفحة',
        'financial_transaction' => 'معاملة مالية',
        'report_view' => 'عرض تقرير',
        'settings_change' => 'تغيير إعدادات',
        'break_start' => 'بدء استراحة',
        'break_end' => 'انتهاء استراحة',
        'other' => 'نشاط آخر'
    ];
    
    return $labels[$type] ?? $type;
}

function getActivityClass($type) {
    $classes = [
        'session_start' => 'session',
        'session_end' => 'session',
        'session_update' => 'session',
        'customer_add' => 'customer',
        'customer_edit' => 'customer',
        'customer_delete' => 'customer',
        'cafeteria_order' => 'cafeteria',
        'cafeteria_payment' => 'cafeteria',
        'page_visit' => 'system'
    ];
    
    return $classes[$type] ?? '';
}

function getActivityTypeIcon($type) {
    $icons = [
        'login' => '<i class="fas fa-sign-in-alt text-success"></i>',
        'logout' => '<i class="fas fa-sign-out-alt text-danger"></i>',
        'session_start' => '<i class="fas fa-play text-primary"></i>',
        'session_end' => '<i class="fas fa-stop text-secondary"></i>',
        'session_update' => '<i class="fas fa-edit text-info"></i>',
        'customer_add' => '<i class="fas fa-user-plus text-warning"></i>',
        'customer_edit' => '<i class="fas fa-user-edit text-warning"></i>',
        'customer_delete' => '<i class="fas fa-user-minus text-danger"></i>',
        'cafeteria_order' => '<i class="fas fa-shopping-cart text-purple"></i>',
        'cafeteria_payment' => '<i class="fas fa-credit-card text-success"></i>',
        'device_update' => '<i class="fas fa-cog text-secondary"></i>',
        'page_visit' => '<i class="fas fa-eye text-muted"></i>',
        'financial_transaction' => '<i class="fas fa-money-bill text-success"></i>',
        'report_view' => '<i class="fas fa-chart-bar text-info"></i>',
        'settings_change' => '<i class="fas fa-wrench text-warning"></i>',
        'break_start' => '<i class="fas fa-pause text-warning"></i>',
        'break_end' => '<i class="fas fa-play text-success"></i>',
        'other' => '<i class="fas fa-question text-muted"></i>'
    ];
    
    return $icons[$type] ?? '<i class="fas fa-circle text-muted"></i>';
}
?>

<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف مدير
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات للموظفين
if (isset($_SESSION['employee_id'])) {
    if (!employeeCanAccessPage('shift_reports')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }
    
    if (!employeeHasPermission('view_reports') && !employeeHasPermission('manage_employees')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$is_employee = isset($_SESSION['employee_id']);

$page_title = "تقارير الشيفت";
$active_page = "shift_reports";

// معالجة الفلاتر
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$employee_filter = $_GET['employee_id'] ?? '';
$status_filter = $_GET['status'] ?? '';

// جلب قائمة الموظفين
$employees_stmt = $pdo->prepare("
    SELECT id, name, role 
    FROM employees 
    WHERE client_id = ? AND is_active = 1 
    ORDER BY name
");
$employees_stmt->execute([$client_id]);
$employees = $employees_stmt->fetchAll();

// بناء استعلام التقارير
$where_conditions = ["es.client_id = ?"];
$params = [$client_id];

if ($date_from) {
    $where_conditions[] = "DATE(es.shift_start_time) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(es.shift_start_time) <= ?";
    $params[] = $date_to;
}

if ($employee_filter) {
    $where_conditions[] = "es.employee_id = ?";
    $params[] = $employee_filter;
}

if ($status_filter) {
    $where_conditions[] = "es.shift_status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// جلب تقارير الشيفت
$shifts_stmt = $pdo->prepare("
    SELECT 
        es.*,
        e.name as employee_name,
        e.role as employee_role,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id AND activity_type = 'session_start') as sessions_started,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id AND activity_type = 'customer_add') as customers_added,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id AND activity_type = 'cafeteria_order') as orders_created
    FROM employee_shifts es
    JOIN employees e ON es.employee_id = e.id
    WHERE {$where_clause}
    ORDER BY es.shift_start_time DESC
    LIMIT 100
");
$shifts_stmt->execute($params);
$shifts = $shifts_stmt->fetchAll();

// حساب الإحصائيات العامة
$stats_stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_shifts,
        COUNT(CASE WHEN shift_status = 'completed' THEN 1 END) as completed_shifts,
        COUNT(CASE WHEN shift_status = 'active' THEN 1 END) as active_shifts,
        SUM(actual_duration_minutes) as total_minutes,
        AVG(actual_duration_minutes) as avg_duration,
        COUNT(DISTINCT employee_id) as unique_employees
    FROM employee_shifts es
    WHERE {$where_clause}
");
$stats_stmt->execute($params);
$stats = $stats_stmt->fetch();

// حساب إحصائيات الأنشطة
$activities_stmt = $pdo->prepare("
    SELECT 
        activity_type,
        COUNT(*) as count
    FROM employee_shift_activities esa
    JOIN employee_shifts es ON esa.shift_id = es.shift_id
    WHERE {$where_clause}
    GROUP BY activity_type
    ORDER BY count DESC
");
$activities_stmt->execute($params);
$activities_stats = $activities_stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-chart-line text-primary"></i>
                    تقارير الشيفت
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i> تصدير Excel
                        </button>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>" <?php echo $employee_filter == $employee['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($employee['name']); ?> (<?php echo htmlspecialchars($employee['role']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">حالة الشيفت</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                <option value="cancelled" <?php echo $status_filter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                <option value="on_break" <?php echo $status_filter == 'on_break' ? 'selected' : ''; ?>>في استراحة</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="employee_shift_reports.php" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات العامة -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary"><?php echo number_format($stats['total_shifts']); ?></h5>
                            <p class="card-text">إجمالي الشيفت</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success"><?php echo number_format($stats['completed_shifts']); ?></h5>
                            <p class="card-text">شيفت مكتمل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning"><?php echo number_format($stats['active_shifts']); ?></h5>
                            <p class="card-text">شيفت نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info"><?php echo number_format($stats['total_minutes'] / 60, 1); ?></h5>
                            <p class="card-text">إجمالي الساعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-secondary"><?php echo number_format($stats['avg_duration'] / 60, 1); ?></h5>
                            <p class="card-text">متوسط الساعات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-dark"><?php echo number_format($stats['unique_employees']); ?></h5>
                            <p class="card-text">عدد الموظفين</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الأنشطة -->
            <?php if (!empty($activities_stats)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i>
                        إحصائيات الأنشطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach (array_slice($activities_stats, 0, 6) as $activity): ?>
                        <div class="col-md-2 text-center mb-3">
                            <div class="border rounded p-3">
                                <h6 class="text-primary"><?php echo number_format($activity['count']); ?></h6>
                                <small><?php echo getActivityTypeLabel($activity['activity_type']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- جدول الشيفت -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i>
                        تفاصيل الشيفت (<?php echo count($shifts); ?> شيفت)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($shifts)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد شيفت في الفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الموظف</th>
                                        <th>التاريخ</th>
                                        <th>وقت البدء</th>
                                        <th>وقت الانتهاء</th>
                                        <th>المدة</th>
                                        <th>الحالة</th>
                                        <th>الأنشطة</th>
                                        <th>الجلسات</th>
                                        <th>العملاء</th>
                                        <th>الطلبات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($shifts as $shift): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($shift['employee_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($shift['employee_role']); ?></small>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($shift['shift_start_time'])); ?></td>
                                        <td><?php echo date('H:i', strtotime($shift['shift_start_time'])); ?></td>
                                        <td>
                                            <?php if ($shift['shift_end_time']): ?>
                                                <?php echo date('H:i', strtotime($shift['shift_end_time'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">جاري...</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($shift['actual_duration_minutes']): ?>
                                                <?php echo floor($shift['actual_duration_minutes'] / 60) . 'س ' . ($shift['actual_duration_minutes'] % 60) . 'د'; ?>
                                            <?php else: ?>
                                                <?php 
                                                $current_duration = strtotime('now') - strtotime($shift['shift_start_time']);
                                                $hours = floor($current_duration / 3600);
                                                $minutes = floor(($current_duration % 3600) / 60);
                                                echo $hours . 'س ' . $minutes . 'د';
                                                ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'active' => 'success',
                                                'completed' => 'primary',
                                                'cancelled' => 'danger',
                                                'on_break' => 'warning'
                                            ];
                                            $status_labels = [
                                                'active' => 'نشط',
                                                'completed' => 'مكتمل',
                                                'cancelled' => 'ملغي',
                                                'on_break' => 'استراحة'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_classes[$shift['shift_status']]; ?>">
                                                <?php echo $status_labels[$shift['shift_status']]; ?>
                                            </span>
                                        </td>
                                        <td><span class="badge bg-info"><?php echo $shift['total_activities']; ?></span></td>
                                        <td><span class="badge bg-success"><?php echo $shift['sessions_started']; ?></span></td>
                                        <td><span class="badge bg-warning"><?php echo $shift['customers_added']; ?></span></td>
                                        <td><span class="badge bg-secondary"><?php echo $shift['orders_created']; ?></span></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewShiftDetails(<?php echo $shift['shift_id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($shift['shift_status'] == 'completed'): ?>
                                                <button type="button" class="btn btn-outline-success" onclick="generateShiftReport(<?php echo $shift['shift_id']; ?>)">
                                                    <i class="fas fa-file-alt"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal لعرض تفاصيل الشيفت -->
<div class="modal fade" id="shiftDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الشيفت</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="shiftDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewShiftDetails(shiftId) {
    $('#shiftDetailsModal').modal('show');
    
    fetch(`api/get_shift_details.php?shift_id=${shiftId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('shiftDetailsContent').innerHTML = data.html;
            } else {
                document.getElementById('shiftDetailsContent').innerHTML = '<div class="alert alert-danger">خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            document.getElementById('shiftDetailsContent').innerHTML = '<div class="alert alert-danger">خطأ في الاتصال</div>';
        });
}

function generateShiftReport(shiftId) {
    window.open(`api/generate_shift_report.php?shift_id=${shiftId}`, '_blank');
}

function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = 'api/export_shift_reports.php?' + params.toString();
}
</script>

<?php
function getActivityTypeLabel($type) {
    $labels = [
        'login' => 'تسجيل دخول',
        'logout' => 'تسجيل خروج',
        'session_start' => 'بدء جلسة',
        'session_end' => 'إنهاء جلسة',
        'session_update' => 'تحديث جلسة',
        'customer_add' => 'إضافة عميل',
        'customer_edit' => 'تعديل عميل',
        'cafeteria_order' => 'طلب كافيتيريا',
        'page_visit' => 'زيارة صفحة',
        'other' => 'أخرى'
    ];
    
    return $labels[$type] ?? $type;
}

include 'includes/footer.php';
?>

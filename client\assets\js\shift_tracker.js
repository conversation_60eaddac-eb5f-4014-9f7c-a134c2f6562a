/**
 * نظام تتبع الأنشطة من جانب العميل
 * يتم تحميله تلقائياً للموظفين الذين لديهم شيفت نشط
 */

class ShiftTracker {
    constructor() {
        this.isEmployee = document.body.dataset.isEmployee === 'true';
        this.hasActiveShift = document.body.dataset.hasActiveShift === 'true';
        this.lastActivity = Date.now();
        this.activityQueue = [];
        this.isOnline = navigator.onLine;
        
        if (this.isEmployee && this.hasActiveShift) {
            this.init();
        }
    }
    
    init() {
        this.setupEventListeners();
        this.startHeartbeat();
        this.trackPageLoad();
    }
    
    setupEventListeners() {
        // تتبع النقرات على الأزرار والروابط
        document.addEventListener('click', (e) => {
            const target = e.target.closest('button, a, .btn');
            if (target) {
                this.trackClick(target);
            }
        });
        
        // تتبع إرسال النماذج
        document.addEventListener('submit', (e) => {
            this.trackFormSubmit(e.target);
        });
        
        // تتبع تغيير الصفحات
        window.addEventListener('beforeunload', () => {
            this.trackPageUnload();
        });
        
        // تتبع حالة الاتصال
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.flushActivityQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
        
        // تتبع عدم النشاط
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, () => {
                this.lastActivity = Date.now();
            }, { passive: true });
        });
    }
    
    trackPageLoad() {
        const pageName = this.getCurrentPageName();
        const pageTitle = document.title;
        
        this.logActivity('page_visit', `زيارة صفحة: ${pageTitle}`, 'تم تحميل الصفحة', {
            page_name: pageName,
            page_title: pageTitle,
            referrer: document.referrer,
            load_time: performance.now()
        });
    }
    
    trackPageUnload() {
        const timeOnPage = Date.now() - this.pageLoadTime;
        
        this.logActivity('page_visit', 'مغادرة الصفحة', `قضى ${Math.round(timeOnPage/1000)} ثانية في الصفحة`, {
            time_on_page: timeOnPage,
            page_name: this.getCurrentPageName()
        }, true); // إرسال فوري
    }
    
    trackClick(element) {
        const text = element.textContent?.trim() || element.title || element.alt || 'عنصر غير محدد';
        const href = element.href;
        const type = element.tagName.toLowerCase();
        
        let activityTitle = 'نقر على عنصر';
        let description = `تم النقر على: ${text}`;
        
        if (href) {
            activityTitle = 'نقر على رابط';
            description += ` (${href})`;
        }
        
        this.logActivity('system_access', activityTitle, description, {
            element_type: type,
            element_text: text,
            element_href: href,
            element_class: element.className
        });
    }
    
    trackFormSubmit(form) {
        const formName = form.name || form.id || 'نموذج غير محدد';
        const action = form.action || window.location.href;
        const method = form.method || 'GET';
        
        // تحديد نوع النشاط بناءً على النموذج
        let activityType = 'other';
        let activityTitle = `إرسال نموذج: ${formName}`;
        
        if (action.includes('session')) {
            activityType = 'session_update';
            activityTitle = 'تحديث جلسة';
        } else if (action.includes('customer')) {
            activityType = 'customer_edit';
            activityTitle = 'تحديث بيانات عميل';
        } else if (action.includes('order') || action.includes('cafeteria')) {
            activityType = 'cafeteria_order';
            activityTitle = 'إرسال طلب كافيتيريا';
        }
        
        this.logActivity(activityType, activityTitle, `تم إرسال النموذج إلى: ${action}`, {
            form_name: formName,
            form_action: action,
            form_method: method,
            form_elements: form.elements.length
        });
    }
    
    logActivity(type, title, description = '', data = {}, immediate = false) {
        const activity = {
            type: type,
            title: title,
            description: description,
            data: {
                ...data,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                screen_resolution: `${screen.width}x${screen.height}`,
                viewport_size: `${window.innerWidth}x${window.innerHeight}`,
                page_url: window.location.href
            }
        };
        
        if (immediate || this.isOnline) {
            this.sendActivity(activity);
        } else {
            this.activityQueue.push(activity);
        }
    }
    
    sendActivity(activity) {
        if (!this.isOnline) {
            this.activityQueue.push(activity);
            return;
        }
        
        const formData = new FormData();
        formData.append('activity_type', activity.type);
        formData.append('activity_title', activity.title);
        formData.append('activity_description', activity.description);
        formData.append('activity_data', JSON.stringify(activity.data));
        
        // إرسال غير متزامن
        fetch('api/log_shift_activity.php', {
            method: 'POST',
            body: formData,
            keepalive: true
        }).catch(error => {
            console.warn('فشل في إرسال نشاط الشيفت:', error);
            this.activityQueue.push(activity);
        });
    }
    
    flushActivityQueue() {
        if (this.activityQueue.length === 0) return;
        
        const activities = [...this.activityQueue];
        this.activityQueue = [];
        
        activities.forEach(activity => {
            this.sendActivity(activity);
        });
    }
    
    startHeartbeat() {
        // إرسال heartbeat كل 5 دقائق
        setInterval(() => {
            const inactiveTime = Date.now() - this.lastActivity;
            const isActive = inactiveTime < 300000; // 5 دقائق
            
            this.logActivity('system_access', 'heartbeat', `حالة النشاط: ${isActive ? 'نشط' : 'غير نشط'}`, {
                inactive_time: inactiveTime,
                is_active: isActive,
                memory_usage: performance.memory ? performance.memory.usedJSHeapSize : null
            });
        }, 300000); // كل 5 دقائق
    }
    
    getCurrentPageName() {
        const path = window.location.pathname;
        const fileName = path.split('/').pop();
        return fileName.replace('.php', '') || 'index';
    }
    
    // دوال مساعدة للاستخدام الخارجي
    trackCustomActivity(title, description = '', data = {}) {
        this.logActivity('other', title, description, data);
    }
    
    trackSessionAction(action, sessionId, data = {}) {
        const activityType = action === 'start' ? 'session_start' : 
                           action === 'end' ? 'session_end' : 'session_update';
        
        this.logActivity(activityType, `${action} جلسة`, `تم ${action} الجلسة #${sessionId}`, {
            session_id: sessionId,
            action: action,
            ...data
        });
    }
    
    trackCustomerAction(action, customerId, data = {}) {
        const activityType = action === 'add' ? 'customer_add' : 
                           action === 'edit' ? 'customer_edit' : 'customer_delete';
        
        this.logActivity(activityType, `${action} عميل`, `تم ${action} العميل #${customerId}`, {
            customer_id: customerId,
            action: action,
            ...data
        });
    }
    
    trackOrderAction(orderId, data = {}) {
        this.logActivity('cafeteria_order', 'طلب كافيتيريا', `تم إنشاء طلب #${orderId}`, {
            order_id: orderId,
            ...data
        });
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.shiftTracker = new ShiftTracker();
});

// دوال عامة للاستخدام في الصفحات الأخرى
window.trackShiftActivity = function(title, description = '', data = {}) {
    if (window.shiftTracker) {
        window.shiftTracker.trackCustomActivity(title, description, data);
    }
};

window.trackSessionAction = function(action, sessionId, data = {}) {
    if (window.shiftTracker) {
        window.shiftTracker.trackSessionAction(action, sessionId, data);
    }
};

window.trackCustomerAction = function(action, customerId, data = {}) {
    if (window.shiftTracker) {
        window.shiftTracker.trackCustomerAction(action, customerId, data);
    }
};

window.trackOrderAction = function(orderId, data = {}) {
    if (window.shiftTracker) {
        window.shiftTracker.trackOrderAction(orderId, data);
    }
};

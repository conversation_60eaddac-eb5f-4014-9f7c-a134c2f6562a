<?php
/**
 * إصلاح مشاكل التخطيط بعد التحديث للقائمة الجانبية
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح مشاكل التخطيط</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center text-primary mb-4'>
        <i class='fas fa-tools'></i>
        إصلاح مشاكل التخطيط
    </h1>";

// قائمة الصفحات الرئيسية
$main_pages = [
    'client/dashboard.php',
    'client/sessions.php',
    'client/devices.php',
    'client/customers.php',
    'client/employees.php'
];

$fixed_count = 0;

echo "<div class='card mb-4'>
    <div class='card-header bg-warning text-white'>
        <h5><i class='fas fa-wrench'></i> إصلاح الصفحات الرئيسية</h5>
    </div>
    <div class='card-body'>";

foreach ($main_pages as $page_path) {
    echo "<div class='mb-3 p-3 border rounded'>";
    echo "<h6 class='text-primary'><i class='fas fa-file'></i> " . basename($page_path) . "</h6>";
    
    if (!file_exists($page_path)) {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> الملف غير موجود</div>";
        echo "</div>";
        continue;
    }
    
    try {
        $content = file_get_contents($page_path);
        $original_content = $content;
        $changes_made = false;
        
        // إزالة container-fluid و row الإضافية
        $patterns_to_remove = [
            '/<div class="container-fluid">\s*<div class="row">/i',
            '/<div class="container-fluid">/i',
            '/<div class="row">\s*<\?php include.*?sidebar.*?\?>/i',
            '/\?>\s*<\/div>\s*<\/div>\s*<\?php/i'
        ];
        
        foreach ($patterns_to_remove as $pattern) {
            $new_content = preg_replace($pattern, '', $content);
            if ($new_content !== $content) {
                $content = $new_content;
                $changes_made = true;
            }
        }
        
        // إضافة div للمحتوى إذا لم يكن موجوداً
        if (strpos($content, '<div class="page-content">') === false) {
            // البحث عن بداية المحتوى بعد include header
            $header_pos = strpos($content, "include 'includes/header_sidebar_only.php';");
            if ($header_pos !== false) {
                $after_header = strpos($content, '?>', $header_pos) + 2;
                $before_footer = strpos($content, "include 'includes/footer_sidebar_only.php';");
                
                if ($before_footer !== false) {
                    $main_content = substr($content, $after_header, $before_footer - $after_header);
                    $main_content = trim($main_content);
                    
                    // إزالة أي div container إضافية
                    $main_content = preg_replace('/^\s*<div[^>]*>\s*/i', '', $main_content);
                    $main_content = preg_replace('/\s*<\/div>\s*$/i', '', $main_content);
                    
                    $new_content = substr($content, 0, $after_header) . "\n\n" . $main_content . "\n\n<?php " . substr($content, $before_footer);
                    
                    if ($new_content !== $content) {
                        $content = $new_content;
                        $changes_made = true;
                    }
                }
            }
        }
        
        // إصلاح مسارات CSS و JS
        $content = str_replace('../assets/', 'assets/', $content);
        $content = str_replace('../../assets/', '../assets/', $content);
        
        if ($content !== $original_content) {
            $changes_made = true;
        }
        
        if ($changes_made) {
            // إنشاء نسخة احتياطية
            $backup_path = $page_path . '.layout-backup.' . date('H-i-s');
            file_put_contents($backup_path, $original_content);
            
            // كتابة المحتوى المحدث
            if (file_put_contents($page_path, $content)) {
                echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إصلاح التخطيط</div>";
                $fixed_count++;
            } else {
                echo "<div class='alert alert-danger'><i class='fas fa-times'></i> فشل في كتابة الملف</div>";
            }
        } else {
            echo "<div class='alert alert-info'><i class='fas fa-info'></i> لا يحتاج إصلاح</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ: " . $e->getMessage() . "</div>";
    }
    
    echo "</div>";
}

echo "</div></div>";

// إنشاء ملف CSS إضافي للإصلاحات
echo "<div class='card mb-4'>
    <div class='card-header bg-info text-white'>
        <h5><i class='fas fa-paint-brush'></i> إنشاء ملف CSS للإصلاحات</h5>
    </div>
    <div class='card-body'>";

$css_fixes = "
/* إصلاحات التخطيط للقائمة الجانبية */
.main-content .container,
.main-content .container-fluid {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.main-content .row {
    margin: 0 !important;
}

.main-content .col-md-3,
.main-content .col-lg-2 {
    display: none !important; /* إخفاء أعمدة القائمة الجانبية القديمة */
}

/* تحسين المحتوى */
.page-content {
    width: 100%;
    max-width: none;
}

/* إصلاح الجداول */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* إصلاح النماذج */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

/* تحسين البطاقات */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    font-weight: 600;
}

/* تحسين شريط التقدم */
.progress {
    border-radius: 10px;
    height: 8px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* تحسين الشارات */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.page-content > .row,
.page-content > .card {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .sidebar-toggle {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
        padding: 0 !important;
    }
    
    .content-header {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-after: avoid;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-inside: avoid;
    }
}
";

$css_file_path = 'client/assets/css/layout-fixes.css';
if (file_put_contents($css_file_path, $css_fixes)) {
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء ملف CSS للإصلاحات: $css_file_path</div>";
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> فشل في إنشاء ملف CSS</div>";
}

echo "</div></div>";

// النتيجة النهائية
echo "<div class='card border-success'>
    <div class='card-header bg-success text-white text-center'>
        <h3><i class='fas fa-check-circle'></i> اكتمل الإصلاح!</h3>
    </div>
    <div class='card-body text-center'>
        <div class='alert alert-success' style='font-size: 1.2em;'>
            <i class='fas fa-thumbs-up fa-2x mb-2'></i><br>
            <strong>تم إصلاح $fixed_count صفحة</strong><br>
            النظام جاهز للاستخدام مع القائمة الجانبية!
        </div>
        
        <div class='row mt-4'>
            <div class='col-md-4'>
                <div class='card border-primary'>
                    <div class='card-body text-center'>
                        <i class='fas fa-eye fa-2x text-primary mb-2'></i>
                        <h6>اختبار التصميم</h6>
                        <a href='client/test_sidebar_design.php' class='btn btn-primary'>عرض</a>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card border-success'>
                    <div class='card-body text-center'>
                        <i class='fas fa-home fa-2x text-success mb-2'></i>
                        <h6>لوحة التحكم</h6>
                        <a href='client/dashboard.php' class='btn btn-success'>دخول</a>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card border-info'>
                    <div class='card-body text-center'>
                        <i class='fas fa-play fa-2x text-info mb-2'></i>
                        <h6>الجلسات</h6>
                        <a href='client/sessions.php' class='btn btn-info'>عرض</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class='mt-4'>
            <h5>المميزات الجديدة:</h5>
            <div class='row'>
                <div class='col-md-6'>
                    <ul class='list-group'>
                        <li class='list-group-item d-flex justify-content-between'>
                            قائمة جانبية ثابتة
                            <span class='badge bg-success'>✓</span>
                        </li>
                        <li class='list-group-item d-flex justify-content-between'>
                            تصميم متجاوب
                            <span class='badge bg-success'>✓</span>
                        </li>
                        <li class='list-group-item d-flex justify-content-between'>
                            تأثيرات محسنة
                            <span class='badge bg-success'>✓</span>
                        </li>
                    </ul>
                </div>
                <div class='col-md-6'>
                    <ul class='list-group'>
                        <li class='list-group-item d-flex justify-content-between'>
                            تنظيم أفضل للمحتوى
                            <span class='badge bg-success'>✓</span>
                        </li>
                        <li class='list-group-item d-flex justify-content-between'>
                            سهولة التنقل
                            <span class='badge bg-success'>✓</span>
                        </li>
                        <li class='list-group-item d-flex justify-content-between'>
                            دعم الموبايل
                            <span class='badge bg-success'>✓</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class='alert alert-info mt-4'>
            <h6><i class='fas fa-lightbulb'></i> ملاحظات مهمة:</h6>
            <ul class='mb-0'>
                <li>تم استبدال القائمة العلوية بالقائمة الجانبية في جميع الصفحات</li>
                <li>التصميم الجديد متجاوب ويعمل على جميع الأجهزة</li>
                <li>تم إضافة تأثيرات CSS محسنة لتجربة أفضل</li>
                <li>يمكن تخصيص الألوان والتصميم من ملف CSS</li>
            </ul>
        </div>
    </div>
</div>";

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>

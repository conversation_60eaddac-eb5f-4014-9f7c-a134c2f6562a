        </div> <!-- إغلاق page-content -->
    </div> <!-- إغلاق main-content -->

    <!-- Footer -->
    <div class="main-content">
        <footer class="mt-5 py-4 border-top">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        <i class="fas fa-copyright me-1"></i>
                        <?php echo date('Y'); ?> <?php echo htmlspecialchars($business_name ?? 'PlayGood'); ?>. 
                        جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        <i class="fas fa-code me-1"></i>
                        نظام إدارة محلات البلايستيشن
                        <span class="badge bg-primary ms-2">v2.0</span>
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript إضافي -->
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات التحميل
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // تحسين الجداول
            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                if (!table.closest('.table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });
            
            // تحسين النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                        
                        setTimeout(() => {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalText;
                        }, 3000);
                    }
                });
            });
            
            // تحسين الأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
        
        // إضافة tooltips للأيقونات
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // تحسين الإشعارات
        function showNotification(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // تحسين التنقل
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // إضافة تأثير التحميل للروابط الداخلية
                if (this.href && this.href.includes(window.location.hostname)) {
                    const icon = this.querySelector('i');
                    if (icon && !icon.classList.contains('fa-spin')) {
                        const originalClass = icon.className;
                        icon.className = 'fas fa-spinner fa-spin';
                        
                        setTimeout(() => {
                            icon.className = originalClass;
                        }, 1000);
                    }
                }
            });
        });
        
        // حفظ حالة القائمة الجانبية
        function saveSidebarState() {
            const sidebar = document.getElementById('sidebar');
            const isOpen = sidebar.classList.contains('show');
            localStorage.setItem('sidebarState', isOpen ? 'open' : 'closed');
        }
        
        function loadSidebarState() {
            const state = localStorage.getItem('sidebarState');
            const sidebar = document.getElementById('sidebar');
            
            if (state === 'open' && window.innerWidth <= 768) {
                sidebar.classList.add('show');
            }
        }
        
        // تحميل حالة القائمة عند بدء الصفحة
        loadSidebarState();
        
        // حفظ الحالة عند التغيير
        document.querySelector('.sidebar-toggle')?.addEventListener('click', saveSidebarState);
    </script>

    <!-- CSS إضافي للتحسينات -->
    <style>
        /* تحسينات إضافية للتصميم */
        .card {
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .btn {
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .badge {
            font-weight: 500;
        }
        
        /* تحسين الفورم */
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        /* تحسين الجداول */
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }
        
        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(52, 152, 219, 0.05);
        }
        
        /* تحسين الإحصائيات */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .stats-card .card-body {
            padding: 1.5rem;
        }
        
        /* تأثيرات التحميل */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

</body>
</html>

<?php
/**
 * إعداد نظام إدارة الشيفت للموظفين
 * تاريخ الإنشاء: 2025-08-01
 */

require_once 'config/database.php';

// تعيين الترميز
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد نظام إدارة الشيفت</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; }
        .progress { background: #e9ecef; height: 20px; border-radius: 10px; margin: 10px 0; }
        .progress-bar { background: #007bff; height: 100%; border-radius: 10px; transition: width 0.3s; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 إعداد نظام إدارة الشيفت للموظفين</h1>";

$steps = [
    'إنشاء جدول الشيفت النشط',
    'إنشاء جدول تسجيل الأنشطة',
    'إنشاء جدول ملخص الشيفت',
    'إنشاء جدول الإعدادات',
    'تحديث جدول الموظفين',
    'إنشاء Views الأساسية',
    'إنشاء جدول الإشعارات',
    'إنشاء جدول الأخطاء',
    'إدراج البيانات الافتراضية',
    'إنشاء فهارس إضافية',
    'إنشاء Views إضافية'
];

$total_steps = count($steps);
$current_step = 0;

try {
    // الخطوة 1: إنشاء جدول الشيفت النشط
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shifts (
        shift_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        shift_end_time TIMESTAMP NULL,
        planned_start_time TIME NULL COMMENT 'الوقت المخطط لبدء الشيفت',
        planned_end_time TIME NULL COMMENT 'الوقت المخطط لانتهاء الشيفت',
        actual_duration_minutes INT DEFAULT 0,
        break_start_time TIMESTAMP NULL,
        break_end_time TIMESTAMP NULL,
        break_duration_minutes INT DEFAULT 0,
        shift_status ENUM('active', 'completed', 'cancelled', 'on_break') DEFAULT 'active',
        shift_notes TEXT NULL,
        location_info VARCHAR(255) NULL COMMENT 'معلومات الموقع أو المحطة',
        ip_address VARCHAR(45) NULL,
        device_info TEXT NULL COMMENT 'معلومات الجهاز المستخدم',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_employee_shift (employee_id, shift_status),
        INDEX idx_client_shift (client_id, shift_start_time),
        INDEX idx_shift_status (shift_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shifts بنجاح</div>";
    echo "</div>";
    
    // الخطوة 2: إنشاء جدول تسجيل الأنشطة
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_activities (
        activity_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        activity_type ENUM(
            'login', 'logout', 'session_start', 'session_end', 'session_update',
            'customer_add', 'customer_edit', 'customer_delete',
            'device_update', 'device_maintenance',
            'cafeteria_order', 'cafeteria_payment',
            'inventory_update', 'inventory_check',
            'financial_transaction', 'report_view',
            'settings_change', 'employee_action',
            'break_start', 'break_end',
            'system_access', 'page_visit',
            'other'
        ) NOT NULL,
        activity_title VARCHAR(255) NOT NULL,
        activity_description TEXT NULL,
        target_type VARCHAR(50) NULL COMMENT 'نوع الهدف (session, customer, device, etc.)',
        target_id INT NULL COMMENT 'معرف الهدف',
        old_values JSON NULL COMMENT 'القيم القديمة قبل التغيير',
        new_values JSON NULL COMMENT 'القيم الجديدة بعد التغيير',
        activity_data JSON NULL COMMENT 'بيانات إضافية للنشاط',
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        page_url VARCHAR(500) NULL,
        http_method VARCHAR(10) NULL,
        response_status INT NULL,
        execution_time_ms INT NULL COMMENT 'وقت تنفيذ العملية بالميلي ثانية',
        activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_shift_activities (shift_id, activity_timestamp),
        INDEX idx_employee_activities (employee_id, activity_timestamp),
        INDEX idx_activity_type (activity_type),
        INDEX idx_target (target_type, target_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_activities بنجاح</div>";
    echo "</div>";
    
    // الخطوة 3: إنشاء جدول ملخص الشيفت
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_summaries (
        summary_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL UNIQUE,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_date DATE NOT NULL,
        total_duration_minutes INT NOT NULL,
        break_duration_minutes INT DEFAULT 0,
        productive_time_minutes INT NOT NULL,
        total_activities INT DEFAULT 0,
        sessions_handled INT DEFAULT 0,
        customers_served INT DEFAULT 0,
        revenue_generated DECIMAL(10,2) DEFAULT 0.00,
        activities_breakdown JSON NULL COMMENT 'تفصيل الأنشطة حسب النوع',
        performance_metrics JSON NULL COMMENT 'مقاييس الأداء',
        shift_rating ENUM('excellent', 'good', 'average', 'poor') NULL,
        manager_notes TEXT NULL,
        auto_generated_summary TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_employee_summaries (employee_id, shift_date),
        INDEX idx_client_summaries (client_id, shift_date),
        INDEX idx_shift_date (shift_date),
        INDEX idx_shift_rating (shift_rating)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_summaries بنجاح</div>";
    echo "</div>";
    
    // الخطوة 4: إنشاء جدول الإعدادات
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT NOT NULL,
        setting_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_client_setting (client_id, setting_key),
        INDEX idx_client_settings (client_id, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_system_settings بنجاح</div>";
    echo "</div>";
    
    // الخطوة 5: تحديث جدول الموظفين
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS current_shift_id INT NULL");
        echo "<div class='success'>✅ تم إضافة عمود current_shift_id</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود current_shift_id موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS shift_status ENUM('off_duty', 'on_duty', 'on_break') DEFAULT 'off_duty'");
        echo "<div class='success'>✅ تم إضافة عمود shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود shift_status موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS last_activity_time TIMESTAMP NULL");
        echo "<div class='success'>✅ تم إضافة عمود last_activity_time</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود last_activity_time موجود مسبقاً</div>";
    }
    
    echo "</div>";
    
    // الخطوة 6: إنشاء Views
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": {$steps[$current_step-1]}</h2>";
    
    $sql = "
    CREATE OR REPLACE VIEW active_employee_shifts AS
    SELECT 
        es.shift_id,
        es.employee_id,
        e.name as employee_name,
        e.role as employee_role,
        es.client_id,
        c.business_name,
        es.shift_start_time,
        es.planned_end_time,
        es.shift_status,
        es.location_info,
        TIMESTAMPDIFF(MINUTE, es.shift_start_time, NOW()) as current_duration_minutes,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities,
        (SELECT activity_timestamp FROM employee_shift_activities WHERE shift_id = es.shift_id ORDER BY activity_timestamp DESC LIMIT 1) as last_activity_time
    FROM employee_shifts es
    JOIN employees e ON es.employee_id = e.id
    JOIN clients c ON es.client_id = c.client_id
    WHERE es.shift_status IN ('active', 'on_break')";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء view active_employee_shifts</div>";
    
    echo "</div>";

    // الخطوة 7: إنشاء جدول الإشعارات
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": إنشاء جدول الإشعارات</h2>";

    $sql = "
    CREATE TABLE IF NOT EXISTS shift_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        employee_id INT NULL,
        shift_id INT NULL,
        notification_type ENUM('shift_start', 'shift_end', 'break_reminder', 'overtime_alert', 'system_alert') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,

        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE SET NULL,

        INDEX idx_client_notifications (client_id, is_read, created_at),
        INDEX idx_employee_notifications (employee_id, is_read),
        INDEX idx_notification_type (notification_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_notifications بنجاح</div>";
    echo "</div>";

    // الخطوة 8: إنشاء جدول الأخطاء
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": إنشاء جدول الأخطاء</h2>";

    $sql = "
    CREATE TABLE IF NOT EXISTS shift_system_errors (
        error_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NULL,
        shift_id INT NULL,
        error_type VARCHAR(100) NOT NULL,
        error_message TEXT NOT NULL,
        error_data JSON NULL,
        stack_trace TEXT NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        INDEX idx_error_type (error_type),
        INDEX idx_error_date (created_at),
        INDEX idx_employee_errors (employee_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_system_errors بنجاح</div>";
    echo "</div>";

    // الخطوة 9: إدراج الإعدادات الافتراضية
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": إدراج الإعدادات الافتراضية</h2>";

    $settings = [
        ['auto_track_activities', 'true', 'تفعيل تتبع الأنشطة التلقائي'],
        ['require_shift_notes', 'false', 'إجبار كتابة ملاحظات عند انتهاء الشيفت'],
        ['max_shift_duration_hours', '12', 'الحد الأقصى لمدة الشيفت بالساعات'],
        ['break_reminder_minutes', '240', 'تذكير بالاستراحة كل X دقيقة']
    ];

    $clients_stmt = $pdo->query("SELECT client_id FROM clients WHERE is_active = 1");
    $clients = $clients_stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach ($clients as $client_id) {
        foreach ($settings as $setting) {
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) VALUES (?, ?, ?, ?)");
                $stmt->execute([$client_id, $setting[0], $setting[1], $setting[2]]);
            } catch (Exception $e) {
                // تجاهل الأخطاء للإعدادات الموجودة
            }
        }
    }

    echo "<div class='success'>✅ تم إدراج الإعدادات الافتراضية لـ " . count($clients) . " عميل</div>";
    echo "</div>";

    // الخطوة 10: إنشاء فهارس إضافية
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": إنشاء فهارس إضافية</h2>";

    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_shifts_active ON employee_shifts(employee_id, shift_status, shift_start_time)");
        echo "<div class='success'>✅ تم إنشاء فهرس idx_employee_shifts_active</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ فهرس idx_employee_shifts_active موجود مسبقاً</div>";
    }

    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_shift_status ON employees(shift_status, current_shift_id)");
        echo "<div class='success'>✅ تم إنشاء فهرس idx_employee_shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ فهرس idx_employee_shift_status موجود مسبقاً</div>";
    }

    echo "</div>";

    // الخطوة 11: إنشاء Views إضافية
    echo "<div class='step'><h2>الخطوة " . (++$current_step) . ": إنشاء Views إضافية</h2>";

    $sql = "
    CREATE OR REPLACE VIEW shift_quick_stats AS
    SELECT
        'active_shifts' as stat_name,
        COUNT(*) as stat_value,
        'عدد الشيفت النشط حالياً' as stat_description
    FROM employee_shifts
    WHERE shift_status IN ('active', 'on_break')

    UNION ALL

    SELECT
        'total_activities_today' as stat_name,
        COUNT(*) as stat_value,
        'إجمالي الأنشطة اليوم' as stat_description
    FROM employee_shift_activities
    WHERE DATE(activity_timestamp) = CURDATE()

    UNION ALL

    SELECT
        'employees_on_duty' as stat_name,
        COUNT(*) as stat_value,
        'عدد الموظفين في الخدمة' as stat_description
    FROM employees
    WHERE shift_status = 'on_duty'

    UNION ALL

    SELECT
        'completed_shifts_today' as stat_name,
        COUNT(*) as stat_value,
        'الشيفت المكتملة اليوم' as stat_description
    FROM employee_shifts
    WHERE DATE(shift_start_time) = CURDATE()
    AND shift_status = 'completed'";

    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء view shift_quick_stats</div>";
    echo "</div>";

    // عرض شريط التقدم
    $progress = ($current_step / $total_steps) * 100;
    echo "<div class='progress'><div class='progress-bar' style='width: {$progress}%'></div></div>";
    echo "<p>تم إكمال {$current_step} من {$total_steps} خطوات ({$progress}%)</p>";

    // رسالة النجاح النهائية
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>
        🎉 <strong>تم إعداد نظام إدارة الشيفت بنجاح!</strong><br>
        يمكنك الآن استخدام النظام لإدارة شيفت الموظفين وتتبع أنشطتهم.
    </div>";

    echo "<div class='info'>
        <h3>الخطوات التالية:</h3>
        <ul>
            <li>إنشاء صفحة بدء الشيفت للموظفين</li>
            <li>إنشاء صفحة تقارير الشيفت للإدارة</li>
            <li>تحديث واجهة المستخدم لتشمل النظام الجديد</li>
        </ul>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div></body></html>";

<?php
/**
 * إصلاح مشكلة المراجع الخارجية (Foreign Keys)
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح المراجع الخارجية</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center text-primary mb-4'>
        <i class='fas fa-wrench'></i>
        إصلاح المراجع الخارجية
    </h1>";

try {
    // الخطوة 1: تعطيل فحص المراجع الخارجية مؤقتاً
    echo "<div class='card mb-3'>
        <div class='card-header bg-warning text-white'>
            <h5>الخطوة 1: تعطيل فحص المراجع الخارجية</h5>
        </div>
        <div class='card-body'>";
    
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تعطيل فحص المراجع الخارجية مؤقتاً</div>";
    echo "</div></div>";
    
    // الخطوة 2: حذف الجداول المتضاربة
    echo "<div class='card mb-3'>
        <div class='card-header bg-danger text-white'>
            <h5>الخطوة 2: حذف الجداول المتضاربة</h5>
        </div>
        <div class='card-body'>";
    
    $tables_to_drop = [
        'employee_shift_activities',
        'employee_shift_summaries', 
        'shift_notifications',
        'employee_shifts'
    ];
    
    foreach ($tables_to_drop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS $table");
            echo "<div class='alert alert-success'><i class='fas fa-trash'></i> تم حذف الجدول $table</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-info'><i class='fas fa-info'></i> الجدول $table غير موجود</div>";
        }
    }
    echo "</div></div>";
    
    // الخطوة 3: إنشاء جدول الشيفت الرئيسي
    echo "<div class='card mb-3'>
        <div class='card-header bg-primary text-white'>
            <h5>الخطوة 3: إنشاء جدول الشيفت الرئيسي</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE TABLE employee_shifts (
        shift_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        shift_end_time TIMESTAMP NULL,
        planned_start_time TIME NULL,
        planned_end_time TIME NULL,
        actual_duration_minutes INT DEFAULT 0,
        break_start_time TIMESTAMP NULL,
        break_end_time TIMESTAMP NULL,
        break_duration_minutes INT DEFAULT 0,
        shift_status ENUM('active', 'completed', 'cancelled', 'on_break') DEFAULT 'active',
        shift_notes TEXT NULL,
        location_info VARCHAR(255) NULL,
        ip_address VARCHAR(45) NULL,
        device_info TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_employee_shift (employee_id, shift_status),
        INDEX idx_client_shift (client_id, shift_start_time),
        INDEX idx_shift_status (shift_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء جدول employee_shifts</div>";
    echo "</div></div>";
    
    // الخطوة 4: إنشاء جدول الأنشطة
    echo "<div class='card mb-3'>
        <div class='card-header bg-success text-white'>
            <h5>الخطوة 4: إنشاء جدول الأنشطة</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE TABLE employee_shift_activities (
        activity_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        activity_type ENUM(
            'login', 'logout', 'session_start', 'session_end', 'session_update',
            'customer_add', 'customer_edit', 'customer_delete',
            'device_update', 'device_maintenance',
            'cafeteria_order', 'cafeteria_payment',
            'inventory_update', 'inventory_check',
            'financial_transaction', 'report_view',
            'settings_change', 'employee_action',
            'break_start', 'break_end',
            'system_access', 'page_visit',
            'other'
        ) NOT NULL,
        activity_title VARCHAR(255) NOT NULL,
        activity_description TEXT NULL,
        target_type VARCHAR(50) NULL,
        target_id INT NULL,
        old_values JSON NULL,
        new_values JSON NULL,
        activity_data JSON NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        page_url VARCHAR(500) NULL,
        http_method VARCHAR(10) NULL,
        response_status INT NULL,
        execution_time_ms INT NULL,
        activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_shift_activities (shift_id, activity_timestamp),
        INDEX idx_employee_activities (employee_id, activity_timestamp),
        INDEX idx_activity_type (activity_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء جدول employee_shift_activities</div>";
    echo "</div></div>";
    
    // الخطوة 5: إنشاء جدول الملخصات
    echo "<div class='card mb-3'>
        <div class='card-header bg-info text-white'>
            <h5>الخطوة 5: إنشاء جدول الملخصات</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE TABLE employee_shift_summaries (
        summary_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL UNIQUE,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_date DATE NOT NULL,
        total_duration_minutes INT NOT NULL,
        break_duration_minutes INT DEFAULT 0,
        productive_time_minutes INT NOT NULL,
        total_activities INT DEFAULT 0,
        sessions_handled INT DEFAULT 0,
        customers_served INT DEFAULT 0,
        revenue_generated DECIMAL(10,2) DEFAULT 0.00,
        activities_breakdown JSON NULL,
        performance_metrics JSON NULL,
        shift_rating ENUM('excellent', 'good', 'average', 'poor') NULL,
        manager_notes TEXT NULL,
        auto_generated_summary TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_employee_summaries (employee_id, shift_date),
        INDEX idx_client_summaries (client_id, shift_date),
        INDEX idx_shift_date (shift_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء جدول employee_shift_summaries</div>";
    echo "</div></div>";
    
    // الخطوة 6: إنشاء جدول الإشعارات
    echo "<div class='card mb-3'>
        <div class='card-header bg-secondary text-white'>
            <h5>الخطوة 6: إنشاء جدول الإشعارات</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE TABLE shift_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        employee_id INT NULL,
        shift_id INT NULL,
        notification_type ENUM('shift_start', 'shift_end', 'break_reminder', 'overtime_alert', 'system_alert') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        
        INDEX idx_client_notifications (client_id, is_read, created_at),
        INDEX idx_notification_type (notification_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء جدول shift_notifications</div>";
    echo "</div></div>";
    
    // الخطوة 7: إضافة المراجع الخارجية
    echo "<div class='card mb-3'>
        <div class='card-header bg-dark text-white'>
            <h5>الخطوة 7: إضافة المراجع الخارجية</h5>
        </div>
        <div class='card-body'>";
    
    // إضافة foreign keys للجدول الرئيسي
    try {
        $pdo->exec("ALTER TABLE employee_shifts ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE");
        echo "<div class='alert alert-success'><i class='fas fa-link'></i> تم ربط employee_shifts بجدول employees</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> الربط موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employee_shifts ADD FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE");
        echo "<div class='alert alert-success'><i class='fas fa-link'></i> تم ربط employee_shifts بجدول clients</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> الربط موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
    }
    
    // إضافة foreign keys لجدول الأنشطة
    try {
        $pdo->exec("ALTER TABLE employee_shift_activities ADD FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE");
        echo "<div class='alert alert-success'><i class='fas fa-link'></i> تم ربط employee_shift_activities بجدول employee_shifts</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> الربط موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employee_shift_activities ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE");
        echo "<div class='alert alert-success'><i class='fas fa-link'></i> تم ربط employee_shift_activities بجدول employees</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> الربط موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employee_shift_activities ADD FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE");
        echo "<div class='alert alert-success'><i class='fas fa-link'></i> تم ربط employee_shift_activities بجدول clients</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> الربط موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
    }
    
    echo "</div></div>";
    
    // الخطوة 8: إعادة تفعيل فحص المراجع الخارجية
    echo "<div class='card mb-3'>
        <div class='card-header bg-success text-white'>
            <h5>الخطوة 8: إعادة تفعيل فحص المراجع الخارجية</h5>
        </div>
        <div class='card-body'>";
    
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إعادة تفعيل فحص المراجع الخارجية</div>";
    echo "</div></div>";
    
    // الخطوة 9: إنشاء Views
    echo "<div class='card mb-3'>
        <div class='card-header bg-info text-white'>
            <h5>الخطوة 9: إنشاء Views</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE OR REPLACE VIEW active_employee_shifts AS
    SELECT 
        es.shift_id,
        es.employee_id,
        e.name as employee_name,
        e.role as employee_role,
        es.client_id,
        c.business_name,
        es.shift_start_time,
        es.planned_end_time,
        es.shift_status,
        es.location_info,
        TIMESTAMPDIFF(MINUTE, es.shift_start_time, NOW()) as current_duration_minutes,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities
    FROM employee_shifts es
    JOIN employees e ON es.employee_id = e.id
    JOIN clients c ON es.client_id = c.client_id
    WHERE es.shift_status IN ('active', 'on_break')";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-eye'></i> تم إنشاء view active_employee_shifts</div>";
    
    $sql = "
    CREATE OR REPLACE VIEW shift_quick_stats AS
    SELECT 
        'active_shifts' as stat_name,
        COUNT(*) as stat_value,
        'عدد الشيفت النشط حالياً' as stat_description
    FROM employee_shifts 
    WHERE shift_status IN ('active', 'on_break')
    
    UNION ALL
    
    SELECT 
        'total_activities_today' as stat_name,
        COUNT(*) as stat_value,
        'إجمالي الأنشطة اليوم' as stat_description
    FROM employee_shift_activities 
    WHERE DATE(activity_timestamp) = CURDATE()
    
    UNION ALL
    
    SELECT 
        'employees_on_duty' as stat_name,
        COUNT(*) as stat_value,
        'عدد الموظفين في الخدمة' as stat_description
    FROM employees 
    WHERE shift_status = 'on_duty'
    
    UNION ALL
    
    SELECT 
        'completed_shifts_today' as stat_name,
        COUNT(*) as stat_value,
        'الشيفت المكتملة اليوم' as stat_description
    FROM employee_shifts 
    WHERE DATE(shift_start_time) = CURDATE() 
    AND shift_status = 'completed'";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-chart-bar'></i> تم إنشاء view shift_quick_stats</div>";
    echo "</div></div>";
    
    // الخطوة 10: اختبار النظام
    echo "<div class='card mb-3'>
        <div class='card-header bg-warning text-white'>
            <h5>الخطوة 10: اختبار النظام</h5>
        </div>
        <div class='card-body'>";
    
    // اختبار إدراج شيفت
    $employee_stmt = $pdo->query("SELECT id, client_id FROM employees WHERE is_active = 1 LIMIT 1");
    $employee = $employee_stmt->fetch();
    
    if ($employee) {
        try {
            // إدراج شيفت تجريبي
            $stmt = $pdo->prepare("
                INSERT INTO employee_shifts (employee_id, client_id, shift_status, location_info) 
                VALUES (?, ?, 'active', 'اختبار إصلاح المراجع')
            ");
            $stmt->execute([$employee['id'], $employee['client_id']]);
            $test_shift_id = $pdo->lastInsertId();
            
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء شيفت تجريبي #$test_shift_id</div>";
            
            // إدراج نشاط تجريبي
            $stmt = $pdo->prepare("
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description
                ) VALUES (?, ?, ?, 'other', 'اختبار إصلاح المراجع', 'نشاط تجريبي بعد إصلاح المراجع')
            ");
            $stmt->execute([$test_shift_id, $employee['id'], $employee['client_id']]);
            
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تسجيل نشاط تجريبي</div>";
            
            // اختبار Views
            $result = $pdo->query("SELECT * FROM active_employee_shifts WHERE shift_id = $test_shift_id");
            $shift_data = $result->fetch();
            
            if ($shift_data) {
                echo "<div class='alert alert-success'><i class='fas fa-check'></i> view active_employee_shifts يعمل بشكل صحيح</div>";
                echo "<div class='alert alert-info'><i class='fas fa-info'></i> بيانات الشيفت: {$shift_data['employee_name']} - {$shift_data['total_activities']} نشاط</div>";
            }
            
            // حذف البيانات التجريبية
            $pdo->prepare("DELETE FROM employee_shift_activities WHERE shift_id = ?")->execute([$test_shift_id]);
            $pdo->prepare("DELETE FROM employee_shifts WHERE shift_id = ?")->execute([$test_shift_id]);
            
            echo "<div class='alert alert-info'><i class='fas fa-trash'></i> تم حذف البيانات التجريبية</div>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في الاختبار: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> لا يوجد موظفين لاختبار النظام</div>";
    }
    
    echo "</div></div>";
    
    // النتيجة النهائية
    echo "<div class='card border-success'>
        <div class='card-header bg-success text-white text-center'>
            <h3><i class='fas fa-check-circle'></i> تم الإصلاح بنجاح!</h3>
        </div>
        <div class='card-body text-center'>
            <div class='alert alert-success' style='font-size: 1.2em;'>
                <i class='fas fa-thumbs-up fa-2x mb-2'></i><br>
                <strong>نظام الشيفت جاهز للاستخدام!</strong><br>
                تم إصلاح جميع مشاكل المراجع الخارجية.
            </div>
            
            <div class='row mt-4'>
                <div class='col-md-6'>
                    <h5>للموظفين:</h5>
                    <a href='client/employee_shift_start.php' class='btn btn-primary btn-lg w-100 mb-2'>
                        <i class='fas fa-play'></i> بدء الشيفت
                    </a>
                </div>
                <div class='col-md-6'>
                    <h5>للإدارة:</h5>
                    <a href='client/employee_shift_reports.php' class='btn btn-success btn-lg w-100 mb-2'>
                        <i class='fas fa-chart-line'></i> تقارير الشيفت
                    </a>
                </div>
            </div>
            
            <div class='mt-3'>
                <a href='final_shift_system_test.php' class='btn btn-info'>
                    <i class='fas fa-vial'></i> اختبار شامل
                </a>
                <a href='client/test_shift_system.php' class='btn btn-secondary'>
                    <i class='fas fa-mouse-pointer'></i> اختبار تفاعلي
                </a>
            </div>
        </div>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>

<?php
/**
 * تنفيذ ملف SQL للشيفتات بشكل صحيح - PlayGood
 * يستخدم mysqli لتنفيذ الاستعلامات المتعددة بشكل صحيح
 */

require_once 'config/database.php';

echo "<h1>🔧 تنفيذ ملف SQL للشيفتات بشكل صحيح</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px;'>";

try {
    // الحصول على معلومات الاتصال من PDO
    $host = 'localhost';
    $dbname = 'station';
    $username = 'root';
    $password = '';
    
    echo "<h2>1. إنشاء اتصال MySQLi</h2>";
    
    // إنشاء اتصال mysqli للتعامل مع الاستعلامات المتعددة
    $mysqli = new mysqli($host, $username, $password, $dbname);
    
    if ($mysqli->connect_error) {
        throw new Exception("فشل الاتصال: " . $mysqli->connect_error);
    }
    
    // تعيين الترميز
    $mysqli->set_charset("utf8mb4");
    
    echo "<p style='color: green;'>✅ تم إنشاء اتصال MySQLi بنجاح</p>";
    
    echo "<h2>2. قراءة وتنفيذ ملف SQL</h2>";
    
    $sql_file = 'enhance_shifts_logging_system.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("ملف SQL غير موجود: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<p style='color: green;'>✅ تم قراءة ملف SQL بنجاح</p>";
    
    // تنفيذ الاستعلامات المتعددة
    echo "<h3>تنفيذ الاستعلامات:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    $success_count = 0;
    $error_count = 0;
    
    // تنفيذ الملف كاملاً باستخدام multi_query
    if ($mysqli->multi_query($sql_content)) {
        do {
            $success_count++;
            echo "<p style='color: green;'>✅ تم تنفيذ استعلام رقم $success_count</p>";
            
            // التخلص من النتائج إذا وجدت
            if ($result = $mysqli->store_result()) {
                $result->free();
            }
            
        } while ($mysqli->next_result());
        
        // فحص الأخطاء
        if ($mysqli->error) {
            $error_count++;
            echo "<p style='color: red;'>❌ خطأ: " . $mysqli->error . "</p>";
        }
    } else {
        $error_count++;
        echo "<p style='color: red;'>❌ خطأ في تنفيذ الاستعلامات: " . $mysqli->error . "</p>";
    }
    
    echo "</div>";
    
    echo "<h2>📊 نتائج التنفيذ:</h2>";
    echo "<div style='background: " . ($error_count > 0 ? '#fff3cd' : '#d1edff') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>الاستعلامات المنفذة:</strong> $success_count</p>";
    echo "<p><strong>الأخطاء:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 تم تنفيذ جميع الاستعلامات بنجاح!</p>";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ تم التنفيذ مع بعض الأخطاء</p>";
    }
    echo "</div>";
    
    // فحص الجداول المنشأة
    echo "<h2>3. فحص الجداول المنشأة</h2>";
    
    $tables_to_check = [
        'shift_events',
        'shift_summaries', 
        'shift_employee_performance',
        'shift_tasks',
        'admin_notifications'
    ];
    
    foreach ($tables_to_check as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ جدول <strong>$table</strong> موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول <strong>$table</strong> غير موجود</p>";
        }
    }
    
    // فحص الإجراءات المخزنة
    echo "<h2>4. فحص الإجراءات المخزنة</h2>";
    
    $procedures_to_check = [
        'GenerateShiftSummary'
    ];
    
    foreach ($procedures_to_check as $procedure) {
        $result = $mysqli->query("SHOW PROCEDURE STATUS WHERE Name = '$procedure'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ إجراء <strong>$procedure</strong> موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ إجراء <strong>$procedure</strong> غير موجود</p>";
        }
    }
    
    // فحص المشغلات
    echo "<h2>5. فحص المشغلات</h2>";
    
    $triggers_to_check = [
        'shift_attendance_events',
        'shift_completion_notification',
        'shift_summary_notification',
        'critical_event_notification'
    ];
    
    foreach ($triggers_to_check as $trigger) {
        $result = $mysqli->query("SHOW TRIGGERS LIKE '$trigger'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ مشغل <strong>$trigger</strong> موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ مشغل <strong>$trigger</strong> غير موجود</p>";
        }
    }
    
    // إغلاق الاتصال
    $mysqli->close();
    
    echo "<h2>✅ تم الانتهاء من التنفيذ</h2>";
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>الخطوات التالية:</h3>";
    echo "<ul>";
    echo "<li>تحقق من أن جميع الجداول والإجراءات تم إنشاؤها بنجاح</li>";
    echo "<li>اختبر وظائف نظام الشيفتات الجديد</li>";
    echo "<li>راجع الإشعارات والتقارير</li>";
    echo "<li>يمكنك الآن استخدام نظام إدارة الشيفتات المحسن</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في التنفيذ</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>وجود ملف enhance_shifts_logging_system.sql</li>";
    echo "<li>صحة اتصال قاعدة البيانات</li>";
    echo "<li>صلاحيات قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

// إضافة CSS للتنسيق
echo "<style>
    body { 
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
        background: #f8f9fa;
        margin: 0;
        padding: 20px;
    }
    h1 { 
        color: #2c3e50; 
        text-align: center; 
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    h2 { 
        color: #34495e; 
        border-bottom: 2px solid #3498db; 
        padding-bottom: 10px; 
        margin-top: 30px;
    }
    h3 { color: #27ae60; }
    p { line-height: 1.6; }
    ul { line-height: 1.8; }
    .container { 
        background: white; 
        padding: 30px; 
        border-radius: 10px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
    }
</style>";
?>

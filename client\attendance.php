<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';
require_once '../includes/shift_helpers.php';

// التحقق من تسجيل الدخول - إما عميل أو موظف
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// التحقق من الصلاحيات
if (isset($_SESSION['employee_id'])) {
    // التحقق من إمكانية الوصول للصفحة
    if (!employeeCanAccessPage('attendance')) {
        header('Location: dashboard.php?error=no_page_access');
        exit;
    }

    // التحقق من الصلاحيات المطلوبة
    if (!employeeHasPermission('manage_attendance') && !employeeHasPermission('view_attendance')) {
        header('Location: dashboard.php?error=no_permission');
        exit;
    }
}

// تحديد معرف العميل
$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$current_employee_id = $_SESSION['employee_id'] ?? null;

$page_title = "الحضور والانصراف";
$active_page = "attendance";

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'check_in':
                    // البحث عن الوردية النشطة للموظف
                    $stmt = $pdo->prepare("
                        SELECT esa.assignment_id, esa.shift_id, s.shift_name
                        FROM employee_shift_assignments esa
                        JOIN shifts s ON esa.shift_id = s.shift_id
                        WHERE esa.employee_id = ? AND s.shift_date = CURDATE()
                        AND s.status IN ('scheduled', 'active')
                        AND NOT EXISTS (
                            SELECT 1 FROM shift_attendance sa
                            WHERE sa.assignment_id = esa.assignment_id
                            AND sa.check_in_time IS NOT NULL
                        )
                        ORDER BY s.start_time ASC
                        LIMIT 1
                    ");
                    $stmt->execute([$_POST['employee_id']]);
                    $assignment = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($assignment) {
                        // إنشاء سجل حضور جديد
                        $stmt = $pdo->prepare("
                            INSERT INTO shift_attendance 
                            (assignment_id, shift_id, employee_id, check_in_time, status, recorded_by)
                            VALUES (?, ?, ?, CURRENT_TIMESTAMP, 'present', ?)
                        ");
                        $stmt->execute([
                            $assignment['assignment_id'],
                            $assignment['shift_id'],
                            $_POST['employee_id'],
                            $client_id
                        ]);
                        
                        // تحديث حالة الوردية إلى نشطة
                        $stmt = $pdo->prepare("UPDATE shifts SET status = 'active' WHERE shift_id = ?");
                        $stmt->execute([$assignment['shift_id']]);
                        
                        $_SESSION['success'] = "تم تسجيل الحضور بنجاح للوردية: " . $assignment['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا توجد وردية مجدولة اليوم لهذا الموظف أو تم تسجيل الحضور مسبقاً";
                    }
                    break;

                case 'check_out':
                    // البحث عن سجل الحضور النشط
                    $stmt = $pdo->prepare("
                        SELECT sa.attendance_id, sa.shift_id, s.shift_name
                        FROM shift_attendance sa
                        JOIN shifts s ON sa.shift_id = s.shift_id
                        WHERE sa.employee_id = ? AND sa.check_in_time IS NOT NULL 
                        AND sa.check_out_time IS NULL
                        ORDER BY sa.check_in_time DESC
                        LIMIT 1
                    ");
                    $stmt->execute([$_POST['employee_id']]);
                    $attendance = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($attendance) {
                        // تسجيل الانصراف
                        $stmt = $pdo->prepare("
                            UPDATE shift_attendance 
                            SET check_out_time = CURRENT_TIMESTAMP, recorded_by = ?
                            WHERE attendance_id = ?
                        ");
                        $stmt->execute([$client_id, $attendance['attendance_id']]);
                        
                        // حساب ساعات العمل باستخدام الدالة المساعدة
                        $calculation_result = calculateAttendanceHours($pdo, $attendance['attendance_id']);
                        if (isset($calculation_result['error'])) {
                            error_log('خطأ في حساب ساعات العمل: ' . $calculation_result['error']);
                        }
                        
                        $_SESSION['success'] = "تم تسجيل الانصراف بنجاح من الوردية: " . $attendance['shift_name'];
                    } else {
                        $_SESSION['error'] = "لا يوجد سجل حضور نشط لهذا الموظف";
                    }
                    break;

                case 'start_break':
                    $stmt = $pdo->prepare("
                        UPDATE shift_attendance 
                        SET break_start_time = CURRENT_TIMESTAMP
                        WHERE employee_id = ? AND check_in_time IS NOT NULL 
                        AND check_out_time IS NULL AND break_start_time IS NULL
                    ");
                    $stmt->execute([$_POST['employee_id']]);
                    
                    if ($stmt->rowCount() > 0) {
                        $_SESSION['success'] = "تم بدء الاستراحة";
                    } else {
                        $_SESSION['error'] = "لا يمكن بدء الاستراحة";
                    }
                    break;

                case 'end_break':
                    $stmt = $pdo->prepare("
                        UPDATE shift_attendance 
                        SET break_end_time = CURRENT_TIMESTAMP
                        WHERE employee_id = ? AND break_start_time IS NOT NULL 
                        AND break_end_time IS NULL
                    ");
                    $stmt->execute([$_POST['employee_id']]);
                    
                    if ($stmt->rowCount() > 0) {
                        $_SESSION['success'] = "تم انتهاء الاستراحة";
                    } else {
                        $_SESSION['error'] = "لا يمكن إنهاء الاستراحة";
                    }
                    break;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
    
    header('Location: attendance.php');
    exit;
}

// جلب الموظفين
$employees = $pdo->prepare("SELECT id, name, role, phone FROM employees WHERE client_id = ? AND is_active = 1 ORDER BY name");
$employees->execute([$client_id]);
$employees_data = $employees->fetchAll(PDO::FETCH_ASSOC);

// جلب الحضور اليوم
$today_attendance = $pdo->prepare("
    SELECT sa.*, e.name as employee_name, e.role, s.shift_name, s.start_time, s.end_time,
           CASE 
               WHEN sa.check_in_time IS NULL THEN 'لم يحضر'
               WHEN sa.check_out_time IS NULL AND sa.break_start_time IS NOT NULL AND sa.break_end_time IS NULL THEN 'في استراحة'
               WHEN sa.check_out_time IS NULL THEN 'حاضر'
               ELSE 'انصرف'
           END as current_status
    FROM shift_attendance sa
    JOIN employees e ON sa.employee_id = e.id
    JOIN shifts s ON sa.shift_id = s.shift_id
    WHERE s.client_id = ? AND s.shift_date = CURDATE()
    ORDER BY sa.check_in_time DESC
");
$today_attendance->execute([$client_id]);
$attendance_data = $today_attendance->fetchAll(PDO::FETCH_ASSOC);

// جلب الورديات اليوم
$today_shifts = $pdo->prepare("
    SELECT s.*, COUNT(esa.assignment_id) as assigned_count,
           COUNT(sa.attendance_id) as checked_in_count
    FROM shifts s
    LEFT JOIN employee_shift_assignments esa ON s.shift_id = esa.shift_id
    LEFT JOIN shift_attendance sa ON esa.assignment_id = sa.assignment_id AND sa.check_in_time IS NOT NULL
    WHERE s.client_id = ? AND s.shift_date = CURDATE()
    GROUP BY s.shift_id
    ORDER BY s.start_time
");
$today_shifts->execute([$client_id]);
$shifts_data = $today_shifts->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header_sidebar_only.php';
?>


        
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user-check me-2"></i><?php echo $page_title; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-primary" onclick="refreshPage()">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count($shifts_data); ?></h4>
                                    <p class="card-text">ورديات اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count(array_filter($attendance_data, function($a) { return $a['current_status'] == 'حاضر' || $a['current_status'] == 'في استراحة'; })); ?></h4>
                                    <p class="card-text">حاضرين الآن</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count(array_filter($attendance_data, function($a) { return $a['current_status'] == 'في استراحة'; })); ?></h4>
                                    <p class="card-text">في استراحة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-coffee fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title"><?php echo count(array_filter($attendance_data, function($a) { return $a['current_status'] == 'انصرف'; })); ?></h4>
                                    <p class="card-text">انصرفوا اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sign-out-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- تسجيل الحضور السريع -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">تسجيل سريع</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="quickAttendanceForm">
                                <div class="mb-3">
                                    <label for="employee_select" class="form-label">اختر الموظف</label>
                                    <select class="form-select" id="employee_select" required>
                                        <option value="">-- اختر موظف --</option>
                                        <?php foreach ($employees_data as $emp): ?>
                                            <option value="<?php echo $emp['id']; ?>">
                                                <?php echo htmlspecialchars($emp['name']); ?> (<?php echo $emp['role']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" onclick="quickAction('check_in')">
                                        <i class="fas fa-sign-in-alt"></i> تسجيل حضور
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="quickAction('check_out')">
                                        <i class="fas fa-sign-out-alt"></i> تسجيل انصراف
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="quickAction('start_break')">
                                        <i class="fas fa-coffee"></i> بدء استراحة
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="quickAction('end_break')">
                                        <i class="fas fa-play"></i> انتهاء استراحة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- ورديات اليوم -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ورديات اليوم</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($shifts_data)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> لا توجد ورديات مجدولة اليوم
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الوردية</th>
                                                <th>الوقت</th>
                                                <th>المخصصين</th>
                                                <th>الحاضرين</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($shifts_data as $shift): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($shift['shift_name']); ?></td>
                                                <td><?php echo date('H:i', strtotime($shift['start_time'])); ?> - <?php echo date('H:i', strtotime($shift['end_time'])); ?></td>
                                                <td><span class="badge bg-primary"><?php echo $shift['assigned_count']; ?></span></td>
                                                <td><span class="badge bg-success"><?php echo $shift['checked_in_count']; ?></span></td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'scheduled' => 'bg-warning',
                                                        'active' => 'bg-success',
                                                        'completed' => 'bg-secondary',
                                                        'cancelled' => 'bg-danger'
                                                    ];
                                                    $status_labels = [
                                                        'scheduled' => 'مجدولة',
                                                        'active' => 'نشطة',
                                                        'completed' => 'مكتملة',
                                                        'cancelled' => 'ملغية'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $status_classes[$shift['status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo $status_labels[$shift['status']] ?? $shift['status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الحضور اليوم -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">سجل الحضور اليوم</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($attendance_data)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> لا يوجد سجل حضور اليوم
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الوردية</th>
                                                <th>وقت الحضور</th>
                                                <th>وقت الانصراف</th>
                                                <th>ساعات العمل</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($attendance_data as $record): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($record['employee_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo $record['role']; ?></small>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($record['shift_name']); ?>
                                                    <br><small class="text-muted">
                                                        <?php echo date('H:i', strtotime($record['start_time'])); ?> -
                                                        <?php echo date('H:i', strtotime($record['end_time'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <?php if ($record['check_in_time']): ?>
                                                        <?php echo date('H:i:s', strtotime($record['check_in_time'])); ?>
                                                        <?php if ($record['late_minutes'] > 0): ?>
                                                            <br><small class="text-danger">تأخير: <?php echo $record['late_minutes']; ?> دقيقة</small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم يحضر</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['check_out_time']): ?>
                                                        <?php echo date('H:i:s', strtotime($record['check_out_time'])); ?>
                                                        <?php if ($record['early_leave_minutes'] > 0): ?>
                                                            <br><small class="text-warning">خروج مبكر: <?php echo $record['early_leave_minutes']; ?> دقيقة</small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم ينصرف</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['actual_hours'] > 0): ?>
                                                        <strong><?php echo number_format($record['actual_hours'], 2); ?></strong> ساعة
                                                        <?php if ($record['overtime_hours'] > 0): ?>
                                                            <br><small class="text-success">إضافي: <?php echo number_format($record['overtime_hours'], 2); ?> ساعة</small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'حاضر' => 'bg-success',
                                                        'في استراحة' => 'bg-warning',
                                                        'انصرف' => 'bg-info',
                                                        'لم يحضر' => 'bg-danger',
                                                        'present' => 'bg-success',
                                                        'late' => 'bg-warning',
                                                        'early_leave' => 'bg-warning',
                                                        'overtime' => 'bg-info',
                                                        'absent' => 'bg-danger'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $status_classes[$record['current_status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo $record['current_status']; ?>
                                                    </span>
                                                    <?php if ($record['break_start_time'] && !$record['break_end_time']): ?>
                                                        <br><small class="text-warning">في استراحة منذ <?php echo date('H:i', strtotime($record['break_start_time'])); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                                        <?php if (!$record['check_in_time']): ?>
                                                            <button type="button" class="btn btn-success btn-sm" onclick="employeeAction(<?php echo $record['employee_id']; ?>, 'check_in')">
                                                                <i class="fas fa-sign-in-alt"></i> حضور
                                                            </button>
                                                        <?php elseif (!$record['check_out_time']): ?>
                                                            <?php if (!$record['break_start_time']): ?>
                                                                <button type="button" class="btn btn-warning btn-sm" onclick="employeeAction(<?php echo $record['employee_id']; ?>, 'start_break')">
                                                                    <i class="fas fa-coffee"></i> استراحة
                                                                </button>
                                                            <?php elseif (!$record['break_end_time']): ?>
                                                                <button type="button" class="btn btn-info btn-sm" onclick="employeeAction(<?php echo $record['employee_id']; ?>, 'end_break')">
                                                                    <i class="fas fa-play"></i> عودة
                                                                </button>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-danger btn-sm" onclick="employeeAction(<?php echo $record['employee_id']; ?>, 'check_out')">
                                                                <i class="fas fa-sign-out-alt"></i> انصراف
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تحديث الصفحة
function refreshPage() {
    location.reload();
}

// إجراء سريع
function quickAction(action) {
    const employeeId = document.getElementById('employee_select').value;
    if (!employeeId) {
        alert('يرجى اختيار موظف أولاً');
        return;
    }

    if (confirm('هل أنت متأكد من هذا الإجراء؟')) {
        employeeAction(employeeId, action);
    }
}

// إجراء على موظف محدد
function employeeAction(employeeId, action) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="${action}">
        <input type="hidden" name="employee_id" value="${employeeId}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا بدلاً من إعادة تحميل الصفحة
    console.log('تحديث تلقائي...');
}, 30000);

// إضافة الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG');
    const dateString = now.toLocaleDateString('ar-EG');

    // يمكن إضافة عنصر لعرض الوقت الحالي
    document.title = `${timeString} - الحضور والانصراف`;
}

// تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);
updateCurrentTime();
</script>

<?php include 'includes/footer_sidebar_only.php'; ?>

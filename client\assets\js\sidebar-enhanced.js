/**
 * JavaScript محسن للقائمة الجانبية والتفاعلات
 * Enhanced Sidebar JavaScript
 */

class SidebarManager {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.toggle = document.querySelector('.sidebar-toggle');
        this.mainContent = document.querySelector('.main-content');
        this.isMobile = window.innerWidth <= 768;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSidebarState();
        this.enhanceNavigation();
        this.setupAnimations();
        this.setupNotifications();
    }
    
    setupEventListeners() {
        // تبديل القائمة الجانبية
        if (this.toggle) {
            this.toggle.addEventListener('click', () => this.toggleSidebar());
        }
        
        // إغلاق القائمة عند النقر خارجها في الموبايل
        document.addEventListener('click', (event) => {
            if (this.isMobile && this.sidebar && this.toggle) {
                if (!this.sidebar.contains(event.target) && !this.toggle.contains(event.target)) {
                    this.closeSidebar();
                }
            }
        });
        
        // تحديث حالة الموبايل عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            if (!this.isMobile) {
                this.closeSidebar();
            }
        });
        
        // منع إغلاق القائمة عند النقر داخلها
        if (this.sidebar) {
            this.sidebar.addEventListener('click', (event) => {
                event.stopPropagation();
            });
        }
    }
    
    toggleSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('show');
            this.saveSidebarState();
        }
    }
    
    closeSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.remove('show');
            this.saveSidebarState();
        }
    }
    
    saveSidebarState() {
        if (this.sidebar) {
            const isOpen = this.sidebar.classList.contains('show');
            localStorage.setItem('sidebarState', isOpen ? 'open' : 'closed');
        }
    }
    
    loadSidebarState() {
        const state = localStorage.getItem('sidebarState');
        if (state === 'open' && this.isMobile && this.sidebar) {
            this.sidebar.classList.add('show');
        }
    }
    
    enhanceNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            // إضافة تأثير التحميل للروابط الداخلية
            link.addEventListener('click', function(e) {
                if (this.href && this.href.includes(window.location.hostname)) {
                    const icon = this.querySelector('i');
                    if (icon && !icon.classList.contains('fa-spin')) {
                        const originalClass = icon.className;
                        icon.className = 'fas fa-spinner fa-spin';
                        
                        setTimeout(() => {
                            icon.className = originalClass;
                        }, 1000);
                    }
                }
            });
            
            // إضافة تأثير الضغط
            link.addEventListener('mousedown', function() {
                this.style.transform = 'translateX(-5px) scale(0.98)';
            });
            
            link.addEventListener('mouseup', function() {
                this.style.transform = '';
            });
        });
    }
    
    setupAnimations() {
        // تأثيرات تحميل البطاقات
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
        
        // تأثيرات الأزرار
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
        
        // تأثيرات الجداول
        const tableRows = document.querySelectorAll('.table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.zIndex = '10';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.zIndex = '';
            });
        });
    }
    
    setupNotifications() {
        // نظام الإشعارات المحسن
        window.showNotification = function(message, type = 'success', duration = 5000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = `
                top: 20px; 
                left: 20px; 
                z-index: 9999; 
                min-width: 350px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                animation: slideInLeft 0.3s ease;
            `;
            
            const icons = {
                'success': 'fas fa-check-circle',
                'danger': 'fas fa-times-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            
            alertDiv.innerHTML = `
                <i class="${icons[type] || icons.info} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.style.animation = 'slideOutLeft 0.3s ease';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 300);
            }, duration);
        };
        
        // تحديث عداد الإشعارات
        this.updateNotificationCount();
        setInterval(() => this.updateNotificationCount(), 30000); // كل 30 ثانية
    }
    
    updateNotificationCount() {
        const badge = document.getElementById('notification-count');
        if (badge) {
            // محاكاة جلب عدد الإشعارات
            fetch('api/get_notification_count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                })
                .catch(() => {
                    // في حالة عدم وجود API، إخفاء العداد
                    badge.style.display = 'none';
                });
        }
    }
}

// تحسينات النماذج
class FormEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.enhanceForms();
        this.setupValidation();
        this.setupAutoSave();
    }
    
    enhanceForms() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            // إضافة تأثير التحميل عند الإرسال
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    submitBtn.disabled = true;
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                    
                    // إعادة تفعيل الزر بعد 3 ثواني كحد أقصى
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }, 3000);
                }
            });
            
            // تحسين حقول الإدخال
            const inputs = form.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });
    }
    
    setupValidation() {
        // تحسين رسائل التحقق
        const inputs = document.querySelectorAll('.form-control[required]');
        inputs.forEach(input => {
            input.addEventListener('invalid', function() {
                this.classList.add('is-invalid');
            });
            
            input.addEventListener('input', function() {
                if (this.validity.valid) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    }
    
    setupAutoSave() {
        // حفظ تلقائي للنماذج الطويلة
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            const saveKey = `autosave_${textarea.id || 'textarea'}`;
            
            // استرجاع النص المحفوظ
            const savedText = localStorage.getItem(saveKey);
            if (savedText && !textarea.value) {
                textarea.value = savedText;
            }
            
            // حفظ تلقائي عند الكتابة
            let saveTimeout;
            textarea.addEventListener('input', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    localStorage.setItem(saveKey, this.value);
                }, 1000);
            });
            
            // مسح الحفظ التلقائي عند الإرسال
            const form = textarea.closest('form');
            if (form) {
                form.addEventListener('submit', () => {
                    localStorage.removeItem(saveKey);
                });
            }
        });
    }
}

// تحسينات الجداول
class TableEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.enhanceTables();
        this.setupSorting();
        this.setupFiltering();
    }
    
    enhanceTables() {
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            // إضافة wrapper responsive إذا لم يكن موجوداً
            if (!table.closest('.table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
            
            // إضافة تأثيرات للصفوف
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach((row, index) => {
                row.style.animationDelay = `${index * 50}ms`;
                row.classList.add('table-row-animated');
            });
        });
    }
    
    setupSorting() {
        // إضافة إمكانية الترتيب للجداول
        const headers = document.querySelectorAll('.table th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
            
            header.addEventListener('click', function() {
                // منطق الترتيب هنا
                console.log('ترتيب حسب:', this.textContent);
            });
        });
    }
    
    setupFiltering() {
        // إضافة بحث سريع للجداول
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'form-control mb-3';
            searchInput.placeholder = 'بحث سريع...';
            
            table.parentElement.insertBefore(searchInput, table);
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
    }
}

// تحسينات الأداء
class PerformanceOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.lazyLoadImages();
        this.optimizeAnimations();
        this.setupIntersectionObserver();
    }
    
    lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
    
    optimizeAnimations() {
        // تقليل الحركات للمستخدمين الذين يفضلون ذلك
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--transition', 'none');
        }
    }
    
    setupIntersectionObserver() {
        // تأثيرات الظهور عند التمرير
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });
        
        const elements = document.querySelectorAll('.card, .alert, .table');
        elements.forEach(el => observer.observe(el));
    }
}

// إدارة الحالة
class StateManager {
    constructor() {
        this.state = {
            currentPage: this.getCurrentPage(),
            userPreferences: this.loadUserPreferences()
        };
        this.init();
    }
    
    init() {
        this.savePageVisit();
        this.setupPreferences();
    }
    
    getCurrentPage() {
        return window.location.pathname.split('/').pop();
    }
    
    savePageVisit() {
        const visits = JSON.parse(localStorage.getItem('pageVisits') || '{}');
        const currentPage = this.getCurrentPage();
        visits[currentPage] = (visits[currentPage] || 0) + 1;
        localStorage.setItem('pageVisits', JSON.stringify(visits));
    }
    
    loadUserPreferences() {
        return JSON.parse(localStorage.getItem('userPreferences') || '{}');
    }
    
    saveUserPreferences(preferences) {
        this.state.userPreferences = { ...this.state.userPreferences, ...preferences };
        localStorage.setItem('userPreferences', JSON.stringify(this.state.userPreferences));
    }
    
    setupPreferences() {
        // تطبيق تفضيلات المستخدم المحفوظة
        if (this.state.userPreferences.theme) {
            document.body.classList.add(`theme-${this.state.userPreferences.theme}`);
        }
    }
}

// تحسينات الأمان
class SecurityEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupCSRFProtection();
        this.setupSessionTimeout();
        this.preventXSS();
    }
    
    setupCSRFProtection() {
        // إضافة CSRF token للنماذج
        const forms = document.querySelectorAll('form[method="post"]');
        forms.forEach(form => {
            if (!form.querySelector('input[name="csrf_token"]')) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrf_token';
                csrfInput.value = this.generateCSRFToken();
                form.appendChild(csrfInput);
            }
        });
    }
    
    generateCSRFToken() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    
    setupSessionTimeout() {
        // تحذير انتهاء الجلسة
        let lastActivity = Date.now();
        const sessionTimeout = 30 * 60 * 1000; // 30 دقيقة
        
        document.addEventListener('click', () => lastActivity = Date.now());
        document.addEventListener('keypress', () => lastActivity = Date.now());
        
        setInterval(() => {
            if (Date.now() - lastActivity > sessionTimeout) {
                if (confirm('انتهت مدة الجلسة. هل تريد تجديدها؟')) {
                    window.location.reload();
                } else {
                    window.location.href = 'logout.php';
                }
            }
        }, 60000); // فحص كل دقيقة
    }
    
    preventXSS() {
        // منع تنفيذ سكريبت ضار
        const scripts = document.querySelectorAll('script[src*="javascript:"]');
        scripts.forEach(script => script.remove());
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة جميع المكونات
    new SidebarManager();
    new FormEnhancer();
    new TableEnhancer();
    new PerformanceOptimizer();
    new StateManager();
    new SecurityEnhancer();
    
    // إضافة CSS للحركات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInLeft {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutLeft {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(-100%); opacity: 0; }
        }
        
        .table-row-animated {
            animation: fadeInUp 0.3s ease forwards;
        }
        
        .animate-in {
            animation: fadeInUp 0.6s ease forwards;
        }
        
        .focused {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }
    `;
    document.head.appendChild(style);
    
    // رسالة تأكيد التحميل
    console.log('🚀 تم تحميل نظام القائمة الجانبية المحسن بنجاح!');
    
    // إظهار إشعار ترحيب
    setTimeout(() => {
        if (typeof showNotification === 'function') {
            showNotification('مرحباً بك في التصميم الجديد! 🎉', 'info', 3000);
        }
    }, 1000);
});

// تصدير الكلاسات للاستخدام الخارجي
window.SidebarManager = SidebarManager;
window.FormEnhancer = FormEnhancer;
window.TableEnhancer = TableEnhancer;

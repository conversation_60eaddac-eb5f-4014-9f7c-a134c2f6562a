<?php
/**
 * إصلاح تضارب جداول الشيفت
 * حل مشكلة assignment_id vs الجدول الجديد
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح تضارب جداول الشيفت</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { text-align: center; color: #333; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 إصلاح تضارب جداول الشيفت</h1>";

try {
    // الخطوة 1: فحص الجداول الموجودة
    echo "<div class='step'><h2>الخطوة 1: فحص الجداول الموجودة</h2>";
    
    // فحص جدول employee_shifts القديم
    $old_table_exists = false;
    try {
        $columns = $pdo->query("DESCRIBE employee_shifts")->fetchAll();
        $has_assignment_id = false;
        $has_client_id = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] == 'assignment_id') {
                $has_assignment_id = true;
            }
            if ($column['Field'] == 'client_id') {
                $has_client_id = true;
            }
        }
        
        if ($has_assignment_id && !$has_client_id) {
            echo "<div class='warning'>⚠️ جدول employee_shifts القديم موجود (مع assignment_id)</div>";
            $old_table_exists = true;
        } elseif ($has_client_id) {
            echo "<div class='success'>✅ جدول employee_shifts الجديد موجود (مع client_id)</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ جدول employee_shifts غير موجود</div>";
    }
    
    echo "</div>";
    
    // الخطوة 2: إعادة تسمية الجدول القديم
    if ($old_table_exists) {
        echo "<div class='step'><h2>الخطوة 2: إعادة تسمية الجدول القديم</h2>";
        
        try {
            // نسخ احتياطي للجدول القديم
            $pdo->exec("DROP TABLE IF EXISTS employee_shifts_old_assignments");
            $pdo->exec("CREATE TABLE employee_shifts_old_assignments AS SELECT * FROM employee_shifts");
            echo "<div class='success'>✅ تم إنشاء نسخة احتياطية: employee_shifts_old_assignments</div>";
            
            // حذف الجدول القديم
            $pdo->exec("DROP TABLE employee_shifts");
            echo "<div class='success'>✅ تم حذف الجدول القديم</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إعادة تسمية الجدول: " . $e->getMessage() . "</div>";
        }
        
        echo "</div>";
    }
    
    // الخطوة 3: إنشاء الجدول الجديد
    echo "<div class='step'><h2>الخطوة 3: إنشاء جدول الشيفت الجديد</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shifts (
        shift_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        shift_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        shift_end_time TIMESTAMP NULL,
        planned_start_time TIME NULL COMMENT 'الوقت المخطط لبدء الشيفت',
        planned_end_time TIME NULL COMMENT 'الوقت المخطط لانتهاء الشيفت',
        actual_duration_minutes INT DEFAULT 0,
        break_start_time TIMESTAMP NULL,
        break_end_time TIMESTAMP NULL,
        break_duration_minutes INT DEFAULT 0,
        shift_status ENUM('active', 'completed', 'cancelled', 'on_break') DEFAULT 'active',
        shift_notes TEXT NULL,
        location_info VARCHAR(255) NULL COMMENT 'معلومات الموقع أو المحطة',
        ip_address VARCHAR(45) NULL,
        device_info TEXT NULL COMMENT 'معلومات الجهاز المستخدم',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_employee_shift (employee_id, shift_status),
        INDEX idx_client_shift (client_id, shift_start_time),
        INDEX idx_shift_status (shift_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shifts الجديد</div>";
    echo "</div>";
    
    // الخطوة 4: إنشاء جدول منفصل للتخصيصات القديمة
    echo "<div class='step'><h2>الخطوة 4: إنشاء جدول التخصيصات المنفصل</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_assignments (
        assignment_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL,
        employee_id INT NOT NULL,
        role_in_shift ENUM('supervisor', 'regular', 'backup') DEFAULT 'regular',
        is_mandatory BOOLEAN DEFAULT FALSE COMMENT 'هل الحضور إجباري',
        assigned_by INT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('assigned', 'confirmed', 'declined', 'cancelled') DEFAULT 'assigned',
        notes TEXT,
        
        FOREIGN KEY (shift_id) REFERENCES shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_employee_shift (shift_id, employee_id),
        INDEX idx_shift_assignments (shift_id, status),
        INDEX idx_employee_assignments (employee_id, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_assignments للتخصيصات</div>";
    
    // نقل البيانات من النسخة الاحتياطية إذا كانت موجودة
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM employee_shifts_old_assignments");
        $old_count = $stmt->fetch()['count'];
        
        if ($old_count > 0) {
            $pdo->exec("
                INSERT INTO employee_shift_assignments 
                (shift_id, employee_id, role_in_shift, is_mandatory, assigned_by, assigned_at, status, notes)
                SELECT shift_id, employee_id, role_in_shift, is_mandatory, assigned_by, assigned_at, status, notes
                FROM employee_shifts_old_assignments
            ");
            echo "<div class='success'>✅ تم نقل $old_count تخصيص من الجدول القديم</div>";
        }
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ لا توجد بيانات قديمة لنقلها</div>";
    }
    
    echo "</div>";
    
    // الخطوة 5: إنشاء الجداول المتبقية
    echo "<div class='step'><h2>الخطوة 5: إنشاء الجداول المتبقية</h2>";
    
    // جدول الأنشطة
    $sql = "
    CREATE TABLE IF NOT EXISTS employee_shift_activities (
        activity_id INT AUTO_INCREMENT PRIMARY KEY,
        shift_id INT NOT NULL,
        employee_id INT NOT NULL,
        client_id INT NOT NULL,
        activity_type ENUM(
            'login', 'logout', 'session_start', 'session_end', 'session_update',
            'customer_add', 'customer_edit', 'customer_delete',
            'device_update', 'device_maintenance',
            'cafeteria_order', 'cafeteria_payment',
            'inventory_update', 'inventory_check',
            'financial_transaction', 'report_view',
            'settings_change', 'employee_action',
            'break_start', 'break_end',
            'system_access', 'page_visit',
            'other'
        ) NOT NULL,
        activity_title VARCHAR(255) NOT NULL,
        activity_description TEXT NULL,
        target_type VARCHAR(50) NULL,
        target_id INT NULL,
        old_values JSON NULL,
        new_values JSON NULL,
        activity_data JSON NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        page_url VARCHAR(500) NULL,
        http_method VARCHAR(10) NULL,
        response_status INT NULL,
        execution_time_ms INT NULL,
        activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        INDEX idx_shift_activities (shift_id, activity_timestamp),
        INDEX idx_employee_activities (employee_id, activity_timestamp),
        INDEX idx_activity_type (activity_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول employee_shift_activities</div>";
    
    // جدول الإشعارات
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        employee_id INT NULL,
        shift_id INT NULL,
        notification_type ENUM('shift_start', 'shift_end', 'break_reminder', 'overtime_alert', 'system_alert') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE SET NULL,
        
        INDEX idx_client_notifications (client_id, is_read, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_notifications</div>";
    
    echo "</div>";
    
    // الخطوة 6: تحديث جدول الموظفين
    echo "<div class='step'><h2>الخطوة 6: تحديث جدول الموظفين</h2>";
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS current_shift_id INT NULL");
        echo "<div class='success'>✅ تم إضافة عمود current_shift_id</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود current_shift_id موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS shift_status ENUM('off_duty', 'on_duty', 'on_break') DEFAULT 'off_duty'");
        echo "<div class='success'>✅ تم إضافة عمود shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود shift_status موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("ALTER TABLE employees ADD COLUMN IF NOT EXISTS last_activity_time TIMESTAMP NULL");
        echo "<div class='success'>✅ تم إضافة عمود last_activity_time</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ عمود last_activity_time موجود مسبقاً</div>";
    }
    
    echo "</div>";
    
    // الخطوة 7: إنشاء Views
    echo "<div class='step'><h2>الخطوة 7: إنشاء Views</h2>";
    
    $sql = "
    CREATE OR REPLACE VIEW active_employee_shifts AS
    SELECT 
        es.shift_id,
        es.employee_id,
        e.name as employee_name,
        e.role as employee_role,
        es.client_id,
        c.business_name,
        es.shift_start_time,
        es.planned_end_time,
        es.shift_status,
        es.location_info,
        TIMESTAMPDIFF(MINUTE, es.shift_start_time, NOW()) as current_duration_minutes,
        (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities
    FROM employee_shifts es
    JOIN employees e ON es.employee_id = e.id
    JOIN clients c ON es.client_id = c.client_id
    WHERE es.shift_status IN ('active', 'on_break')";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء view active_employee_shifts</div>";
    
    echo "</div>";
    
    // الخطوة 8: اختبار النظام
    echo "<div class='step'><h2>الخطوة 8: اختبار النظام الجديد</h2>";
    
    try {
        // اختبار view
        $result = $pdo->query("SELECT * FROM active_employee_shifts LIMIT 1");
        echo "<div class='success'>✅ view active_employee_shifts يعمل بشكل صحيح</div>";
        
        // اختبار إدراج شيفت
        $employee_stmt = $pdo->query("SELECT id, client_id FROM employees WHERE is_active = 1 LIMIT 1");
        $employee = $employee_stmt->fetch();
        
        if ($employee) {
            $stmt = $pdo->prepare("
                INSERT INTO employee_shifts (employee_id, client_id, shift_status, location_info) 
                VALUES (?, ?, 'active', 'اختبار النظام الجديد')
            ");
            $stmt->execute([$employee['id'], $employee['client_id']]);
            $test_shift_id = $pdo->lastInsertId();
            
            echo "<div class='success'>✅ تم إنشاء شيفت تجريبي #$test_shift_id</div>";
            
            // حذف الشيفت التجريبي
            $pdo->prepare("DELETE FROM employee_shifts WHERE shift_id = ?")->execute([$test_shift_id]);
            echo "<div class='info'>ℹ️ تم حذف الشيفت التجريبي</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في اختبار النظام: " . $e->getMessage() . "</div>";
    }
    
    echo "</div>";
    
    // رسالة النجاح
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>
        🎉 <strong>تم إصلاح تضارب الجداول بنجاح!</strong><br>
        يمكنك الآن استخدام نظام الشيفت الجديد بدون مشاكل.
    </div>";
    
    echo "<div class='info'>
        <h3>ملاحظات مهمة:</h3>
        <ul>
            <li>تم الاحتفاظ بالبيانات القديمة في جدول employee_shifts_old_assignments</li>
            <li>النظام الجديد منفصل تماماً عن نظام الورديات القديم</li>
            <li>يمكن استخدام كلا النظامين بشكل متوازي</li>
            <li>النظام الجديد مخصص لتتبع الأنشطة اليومية للموظفين</li>
        </ul>
        
        <h3>الروابط:</h3>
        <ul>
            <li><a href='client/employee_shift_start.php'>نظام الشيفت الجديد - للموظفين</a></li>
            <li><a href='client/employee_shift_reports.php'>تقارير الشيفت الجديد - للإدارة</a></li>
            <li><a href='client/shifts.php'>نظام الورديات القديم - للإدارة</a></li>
            <li><a href='test_shift_system_quick.php'>اختبار النظام</a></li>
        </ul>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div></body></html>";
?>

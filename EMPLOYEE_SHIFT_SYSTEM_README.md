# نظام إدارة الشيفت للموظفين

## نظرة عامة

تم إنشاء نظام شامل لإدارة شيفت الموظفين مع تسجيل تلقائي لجميع العمليات والأنشطة التي يقوم بها الموظف أثناء فترة العمل. النظام يوفر تتبعاً دقيقاً للأداء وإنتاجية الموظفين مع إمكانية إنشاء تقارير مفصلة.

## المميزات الرئيسية

### 1. إدارة الشيفت
- **بدء الشيفت**: يمكن للموظف بدء شيفت جديد مع تحديد الأوقات المخططة والموقع
- **إنهاء الشيفت**: إنهاء الشيفت مع حساب المدة الفعلية وإضافة ملاحظات
- **حالة الشيفت**: تتبع حالة الشيفت (نشط، مكتمل، في استراحة، ملغي)
- **معلومات الموقع**: تسجيل موقع العمل ومعلومات الجهاز المستخدم

### 2. تسجيل الأنشطة التلقائي
- **تتبع تلقائي**: تسجيل جميع العمليات تلقائياً دون تدخل من الموظف
- **أنواع الأنشطة المختلفة**:
  - بدء وإنهاء الجلسات
  - إضافة وتعديل العملاء
  - طلبات الكافيتيريا
  - زيارة الصفحات
  - المعاملات المالية
  - تحديث الأجهزة
  - عرض التقارير

### 3. التتبع من جانب العميل (JavaScript)
- **تتبع النقرات**: تسجيل النقرات على الأزرار والروابط
- **تتبع النماذج**: تسجيل إرسال النماذج والبيانات
- **Heartbeat**: إرسال إشارات حياة دورية كل 5 دقائق
- **تتبع عدم النشاط**: مراقبة فترات عدم النشاط
- **العمل بدون اتصال**: حفظ الأنشطة محلياً وإرسالها عند عودة الاتصال

### 4. التقارير والإحصائيات
- **تقارير مفصلة**: تقارير شاملة لكل شيفت مع جميع الأنشطة
- **إحصائيات الأداء**: مقاييس الأداء والإنتاجية
- **تصدير التقارير**: إمكانية طباعة وتصدير التقارير
- **فلاتر متقدمة**: فلترة التقارير حسب التاريخ والموظف والحالة

## الملفات المنشأة

### 1. قاعدة البيانات
- `create_employee_shift_system.sql` - سكريبت إنشاء الجداول والـ procedures
- `setup_employee_shift_system.php` - واجهة ويب لتنفيذ الإعداد

### 2. الجداول الجديدة
- `employee_shifts` - جدول الشيفت النشط
- `employee_shift_activities` - جدول تسجيل الأنشطة
- `employee_shift_summaries` - جدول ملخصات الشيفت
- `shift_system_settings` - جدول إعدادات النظام
- `shift_notifications` - جدول الإشعارات
- `shift_system_errors` - جدول تسجيل الأخطاء

### 3. الصفحات الجديدة
- `client/employee_shift_start.php` - صفحة بدء/إنهاء الشيفت للموظفين
- `client/employee_shift_reports.php` - صفحة تقارير الشيفت للإدارة
- `client/test_shift_system.php` - صفحة اختبار النظام

### 4. نظام التتبع
- `client/includes/shift_activity_tracker.php` - نظام التتبع من جانب الخادم
- `client/assets/js/shift_tracker.js` - نظام التتبع من جانب العميل
- `client/api/log_shift_activity.php` - API لتسجيل الأنشطة
- `client/api/get_shift_details.php` - API لجلب تفاصيل الشيفت
- `client/api/generate_shift_report.php` - API لتوليد التقارير

## طريقة الاستخدام

### 1. الإعداد الأولي
1. تشغيل `setup_employee_shift_system.php` لإنشاء الجداول
2. التأكد من وجود الموظفين في النظام
3. تعيين الصلاحيات المناسبة للموظفين

### 2. للموظفين
1. تسجيل الدخول كموظف
2. الذهاب إلى "إدارة الشيفت" من القائمة الجانبية
3. النقر على "بدء الشيفت" وملء البيانات المطلوبة
4. العمل بشكل طبيعي - سيتم تسجيل جميع الأنشطة تلقائياً
5. إنهاء الشيفت عند الانتهاء

### 3. للإدارة
1. الذهاب إلى "تقارير الشيفت" من القائمة الجانبية
2. استخدام الفلاتر لتحديد الفترة والموظف المطلوب
3. عرض التقارير المفصلة والإحصائيات
4. طباعة أو تصدير التقارير حسب الحاجة

## المميزات التقنية

### 1. الأمان
- التحقق من الصلاحيات لكل عملية
- تسجيل معلومات الجهاز والـ IP
- حماية من الوصول غير المصرح

### 2. الأداء
- فهارس محسنة لقاعدة البيانات
- تنظيف تلقائي للبيانات القديمة
- ضغط البيانات باستخدام JSON

### 3. المرونة
- إعدادات قابلة للتخصيص لكل عميل
- دعم أنواع أنشطة مختلفة
- إمكانية إضافة أنشطة مخصصة

## Views والـ Procedures المنشأة

### Views
- `active_employee_shifts` - الشيفت النشط مع تفاصيل الموظف
- `daily_shift_report` - تقرير الشيفت اليومي
- `employee_performance_summary` - ملخص أداء الموظفين
- `shift_quick_stats` - الإحصائيات السريعة

### Stored Procedures
- `StartEmployeeShift()` - بدء شيفت جديد
- `EndEmployeeShift()` - إنهاء الشيفت
- `GenerateShiftSummary()` - توليد ملخص الشيفت
- `SendShiftNotification()` - إرسال إشعار

### Functions
- `GetShiftStatistics()` - الحصول على إحصائيات الشيفت
- `LogEmployeeActivity()` - تسجيل نشاط مخصص

## الإعدادات المتاحة

- `auto_track_activities` - تفعيل التتبع التلقائي
- `require_shift_notes` - إجبار كتابة ملاحظات عند الانتهاء
- `max_shift_duration_hours` - الحد الأقصى لمدة الشيفت
- `break_reminder_minutes` - تذكير الاستراحة

## الاختبار

استخدم صفحة `test_shift_system.php` لاختبار:
- تسجيل الأنشطة التلقائي
- عمل JavaScript
- الاتصال بقاعدة البيانات
- عرض الإحصائيات المباشرة

## الصيانة

### تنظيف البيانات
- يتم حذف الأنشطة الأقدم من 6 أشهر تلقائياً
- يتم حذف الشيفت المكتمل الأقدم من سنة
- يتم حذف الأخطاء الأقدم من 3 أشهر

### مراقبة الأداء
- مراقبة حجم جدول `employee_shift_activities`
- فحص سجلات الأخطاء في `shift_system_errors`
- مراجعة الإحصائيات الدورية

## الدعم والتطوير

النظام قابل للتوسع ويمكن إضافة المميزات التالية مستقبلاً:
- تتبع الموقع الجغرافي (GPS)
- تحليلات متقدمة للأداء
- تكامل مع أنظمة الرواتب
- إشعارات فورية للإدارة
- تطبيق موبايل للموظفين

## ملاحظات مهمة

1. **الخصوصية**: النظام يسجل جميع الأنشطة، يجب إعلام الموظفين بذلك
2. **الأداء**: في حالة وجود عدد كبير من الموظفين، قد تحتاج لتحسين الاستعلامات
3. **النسخ الاحتياطي**: تأكد من عمل نسخ احتياطية دورية للبيانات
4. **التحديثات**: راجع النظام دورياً وقم بالتحديثات اللازمة

---

تم إنشاء هذا النظام في 2025-08-01 كجزء من تطوير نظام إدارة محلات البلايستيشن.

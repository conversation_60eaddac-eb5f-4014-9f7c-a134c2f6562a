<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/employee-auth.php';

// تضمين ملف التحقق من صلاحيات الصفحات للعملاء
// نحتاج لتضمينه هنا لاستخدام دالة hasPagePermission في القائمة الجانبية
if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    // للعملاء فقط - تضمين نظام صلاحيات الصفحات
    require_once __DIR__ . '/../../config/database.php';

    /**
     * التحقق من صلاحية الوصول لصفحة معينة
     * @param string $page_name اسم الصفحة
     * @return bool هل العميل لديه صلاحية الوصول
     */
    if (!function_exists('hasPagePermission')) {
        function hasPagePermission($page_name) {
            global $pdo;

            if (!isset($_SESSION['client_id'])) {
                return false;
            }

            try {
                // التحقق من وجود جدول صلاحيات الصفحات
                $table_check = $pdo->query("SHOW TABLES LIKE 'client_pages'");
                if ($table_check->rowCount() == 0) {
                    // إذا لم يكن النظام مفعل، السماح بالوصول لجميع الصفحات
                    return true;
                }

                $stmt = $pdo->prepare("
                    SELECT
                        COALESCE(cpp.is_enabled, cp.is_default) as has_permission
                    FROM client_pages cp
                    LEFT JOIN client_page_permissions cpp ON cp.page_id = cpp.page_id AND cpp.client_id = ?
                    WHERE cp.page_name = ? AND cp.is_active = TRUE
                ");
                $stmt->execute([$_SESSION['client_id'], $page_name]);
                $result = $stmt->fetch();

                return $result ? (bool)$result['has_permission'] : false;

            } catch (Exception $e) {
                // في حالة حدوث خطأ، السماح بالوصول (للأمان)
                return true;
            }
        }
    }
} else {
    // للموظفين - دالة بديلة تستخدم النظام القديم
    if (!function_exists('hasPagePermission')) {
        function hasPagePermission($page_name) {
            // تحويل أسماء الصفحات إلى صلاحيات الموظفين
            $page_to_permission = [
                'sessions' => 'manage_sessions',
                'invoices' => 'manage_sessions',
                'devices' => 'manage_devices',
                'rooms' => 'manage_rooms',
                'customers' => 'manage_customers',
                'cafeteria' => 'manage_cafeteria',
                'orders' => 'manage_orders',
                'inventory' => 'manage_inventory',
                'reservations' => 'view_sessions',
                'employees' => 'manage_employees',
                'attendance' => 'manage_employees',
                'shifts' => 'manage_employees',
                'finances' => 'view_finances',
                'reports' => 'view_reports',
                'settings' => 'manage_settings',
                'invoice_settings' => 'manage_settings'
            ];

            $permission = $page_to_permission[$page_name] ?? 'view_dashboard';
            return hasPermission($permission);
        }
    }
}

// تحديد نوع المستخدم المسجل دخوله
$is_employee = isset($_SESSION['employee_id']);
$is_client = isset($_SESSION['client_id']) && !$is_employee;

// جلب بيانات المحل من قاعدة البيانات
$business_name = 'لوحة التحكم';
$owner_name = '';
$user_name = '';
$user_role = '';

try {
    require_once __DIR__ . '/../../config/database.php';

    if ($is_employee) {
        // جلب بيانات الموظف مع بيانات المحل
        $employee_id = $_SESSION['employee_id'];
        $stmt = $pdo->prepare("
            SELECT e.name as employee_name, e.role, c.business_name, c.owner_name
            FROM employees e
            JOIN clients c ON e.client_id = c.client_id
            WHERE e.employee_id = ?
        ");
        $stmt->execute([$employee_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($data) {
            $user_name = $data['employee_name'] ?? 'موظف';
            $user_role = $data['role'] ?? '';
            $business_name = $data['business_name'] ?? 'لوحة التحكم';
            $owner_name = $data['owner_name'] ?? '';
        } else {
            // بيانات افتراضية للموظف
            $user_name = $_SESSION['employee_name'] ?? 'موظف';
            $user_role = $_SESSION['employee_role'] ?? '';
            $business_name = $_SESSION['business_name'] ?? 'لوحة التحكم';
            $owner_name = $_SESSION['owner_name'] ?? '';
        }
    } else {
        // جلب بيانات صاحب المحل
        $client_id = $_SESSION['client_id'];
        $stmt = $pdo->prepare("
            SELECT business_name, owner_name, name
            FROM clients
            WHERE client_id = ?
        ");
        $stmt->execute([$client_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($data) {
            $business_name = $data['business_name'] ?? $data['name'] ?? 'لوحة التحكم';
            $owner_name = $data['owner_name'] ?? 'مالك المحل';
            $user_name = $owner_name;
            $user_role = 'owner';
        } else {
            // بيانات افتراضية لصاحب المحل
            $user_name = $_SESSION['owner_name'] ?? $_SESSION['client_name'] ?? 'مالك المحل';
            $user_role = 'owner';
            $business_name = $_SESSION['client_name'] ?? $_SESSION['business_name'] ?? 'لوحة التحكم';
            $owner_name = $_SESSION['owner_name'] ?? '';
        }
    }

} catch (Exception $e) {
    // في حالة فشل الاتصال بقاعدة البيانات، استخدم بيانات الجلسة
    if ($is_employee) {
        $user_name = $_SESSION['employee_name'] ?? 'موظف';
        $user_role = $_SESSION['employee_role'] ?? '';
        $business_name = $_SESSION['business_name'] ?? 'لوحة التحكم';
        $owner_name = $_SESSION['owner_name'] ?? '';
    } else {
        $user_name = $_SESSION['owner_name'] ?? $_SESSION['client_name'] ?? 'مالك المحل';
        $user_role = 'owner';
        $business_name = $_SESSION['client_name'] ?? $_SESSION['business_name'] ?? 'لوحة التحكم';
        $owner_name = $_SESSION['owner_name'] ?? '';
    }
}

// دالة للتحقق من الصلاحيات مع دعم المالك
function hasPermission($permission) {
    global $is_employee, $is_client;

    // المالك له جميع الصلاحيات
    if ($is_client) {
        return true;
    }

    // الموظف يحتاج للتحقق من الصلاحيات
    if ($is_employee) {
        return employeeHasPermission($permission);
    }

    return false;
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - PlayGood' : 'PlayGood'; ?></title>
    <link rel="icon" type="image/png" href="../../assets/images/favicon.png">
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- CSS مخصص للمظهر -->
    <link rel="stylesheet" href="assets/css/custom-theme.css?v=<?php echo time(); ?>" type="text/css">
    <!-- CSS ديناميكي للمظهر (احتياطي) -->
    <link rel="stylesheet" href="api/theme-css.php?v=<?php echo time(); ?>" type="text/css">

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
        }
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            min-width: 250px;
            border-radius: 10px;
            padding: 0.5rem 0;
            z-index: 1050;
        }

        .dropdown-menu.show {
            display: block !important;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .dropdown-menu:not(.show) {
            display: none !important;
        }

        .dropdown-toggle::after {
            margin-left: 0.5rem;
        }

        .dropdown-item {
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(-2px);
        }

        .dropdown-header {
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
            margin: -0.5rem -0rem 0.5rem -0rem;
        }

        .dropdown-divider {
            margin: 0.5rem 0;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-left: 8px;
        }
        .navbar-nav .nav-link {
            transition: all 0.3s ease;
            border-radius: 5px;
            margin: 0 2px;
        }
        .navbar-nav .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-dark bg-primary shadow">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gamepad me-2"></i>
                <?php echo htmlspecialchars($business_name); ?>
            </a>

            <!-- Mobile Toggle Button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navbar Content -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Dashboard - متاح للجميع -->
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'dashboard') ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>

                    <!-- الجلسات -->
                    <?php if (hasPagePermission('sessions')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'sessions') ? 'active' : ''; ?>" href="sessions.php">
                            <i class="fas fa-play-circle me-1"></i>الجلسات
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الفواتير -->
                    <?php if (hasPagePermission('invoices')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'invoices') ? 'active' : ''; ?>" href="invoices.php">
                            <i class="fas fa-file-invoice-dollar me-1"></i>الفواتير
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الأجهزة -->
                    <?php if (hasPagePermission('devices')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'devices') ? 'active' : ''; ?>" href="devices.php">
                            <i class="fas fa-desktop me-1"></i>الأجهزة
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الغرف -->
                    <?php if (hasPagePermission('rooms')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'rooms') ? 'active' : ''; ?>" href="rooms.php">
                            <i class="fas fa-door-open me-1"></i>الغرف
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- العملاء -->
                    <?php if (hasPagePermission('customers')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'customers') ? 'active' : ''; ?>" href="customers.php">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الكافتيريا -->
                    <?php if (hasPagePermission('cafeteria')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'cafeteria') ? 'active' : ''; ?>" href="cafeteria.php">
                            <i class="fas fa-coffee me-1"></i>الكافتيريا
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الأوردرات -->
                    <?php if (hasPagePermission('orders')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'orders') ? 'active' : ''; ?>" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>الأوردرات
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- المخزن -->
                    <?php if (hasPagePermission('inventory')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'inventory') ? 'active' : ''; ?>" href="inventory.php">
                            <i class="fas fa-warehouse me-1"></i>إدارة المخزون
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الحجوزات -->
                    <!--<?php if (hasPagePermission('reservations')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'reservations') ? 'active' : ''; ?>" href="reservations.php">
                            <i class="fas fa-calendar-check me-1"></i>الحجوزات
                        </a>
                    </li>
                    <?php endif; ?>-->

                    <!-- الموظفين -->
                    <?php if (hasPagePermission('employees')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'employees') ? 'active' : ''; ?>" href="employees.php">
                            <i class="fas fa-user-tie me-1"></i>الموظفين
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الحضور والانصراف -->
                    <?php if (hasPagePermission('attendance')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'attendance') ? 'active' : ''; ?>" href="attendance.php">
                            <i class="fas fa-clock me-1"></i>الحضور
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الورديات -->
                    <?php if (hasPagePermission('shifts')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'shifts') ? 'active' : ''; ?>" href="shifts.php">
                            <i class="fas fa-calendar-alt me-1"></i>الورديات
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- الماليات -->
                    <?php if (hasPagePermission('finances')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'finances') ? 'active' : ''; ?>" href="finances.php">
                            <i class="fas fa-coins me-1"></i>الماليات
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- التقارير -->
                    <?php if (hasPagePermission('reports')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (isset($active_page) && $active_page === 'reports') ? 'active' : ''; ?>" href="reports.php">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>

                <!-- User Menu -->
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white d-flex align-items-center" href="#"
                           id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar">
                                <?php echo strtoupper(substr($user_name, 0, 1)); ?>
                            </div>
                            <div class="d-flex flex-column align-items-start">
                                <span class="fw-bold"><?php echo htmlspecialchars($user_name); ?></span>
                                <small class="text-light opacity-75">
                                    <?php
                                    if ($is_employee) {
                                        echo match($user_role) {
                                            'manager' => 'مدير',
                                            'cashier' => 'كاشير',
                                            'waiter' => 'ويتر',
                                            'cleaner' => 'عامل نظافة',
                                            default => 'موظف'
                                        };
                                    } else {
                                        echo 'مالك المحل';
                                    }
                                    ?>
                                </small>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">
                                <div class="text-center">
                                    <div class="user-avatar mx-auto mb-2" style="width: 48px; height: 48px;">
                                        <?php echo strtoupper(substr($user_name, 0, 1)); ?>
                                    </div>
                                    <h6 class="mb-0"><?php echo htmlspecialchars($business_name); ?></h6>
                                    <?php if ($owner_name): ?>
                                        <small class="text-muted"><?php echo htmlspecialchars($owner_name); ?></small>
                                    <?php endif; ?>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user me-2"></i>الملف الشخصي
                                </a>
                            </li>
                            <?php if (hasPagePermission('settings') || $is_client): ?>
                            <li>
                                <a class="dropdown-item" href="settings.php">
                                    <i class="fas fa-cog me-2"></i>إعدادات المحل
                                </a>
                            </li>
                            <?php endif; ?>
                            <?php if (hasPagePermission('invoice_settings')): ?>
                            <li>
                                <a class="dropdown-item" href="invoice_settings.php">
                                    <i class="fas fa-file-alt me-2"></i>إعدادات الفواتير
                                </a>
                            </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo $is_employee ? 'employee-logout.php' : 'logout.php'; ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Spacer for fixed navbar -->
    <div style="margin-top: 80px;"></div>    <!-- Alert Container for JavaScript notifications -->
    <div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055; margin-top: 80px;"></div>

    <!-- JavaScript for Header Updates -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
    // تحديث معلومات الـ header
    function updateHeaderInfo() {
        fetch('api/update_header_info.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث اسم المحل في الـ navbar
                    const businessNameElement = document.querySelector('.navbar-brand');
                    if (businessNameElement && data.business_name) {
                        businessNameElement.innerHTML = '<i class="fas fa-gamepad me-2"></i>' + data.business_name;
                    }

                    // تحديث اسم المستخدم في القائمة المنسدلة
                    const userNameElements = document.querySelectorAll('.fw-bold');
                    userNameElements.forEach(element => {
                        if (element.textContent.trim() && data.user_name) {
                            element.textContent = data.user_name;
                        }
                    });

                    // تحديث اسم المحل في dropdown header
                    const businessNameInDropdown = document.querySelector('.dropdown-header h6');
                    if (businessNameInDropdown && data.business_name) {
                        businessNameInDropdown.textContent = data.business_name;
                    }

                    // تحديث اسم المالك في dropdown
                    const ownerNameInDropdown = document.querySelector('.dropdown-header small');
                    if (ownerNameInDropdown && data.owner_name) {
                        ownerNameInDropdown.textContent = data.owner_name;
                    }

                    // تحديث عنوان الصفحة
                    if (data.business_name) {
                        const currentTitle = document.title;
                        const titleParts = currentTitle.split(' - ');
                        if (titleParts.length > 1) {
                            document.title = titleParts[0] + ' - ' + data.business_name;
                        } else {
                            document.title = data.business_name + ' - لوحة التحكم';
                        }
                    }

                    // تحديث الأحرف الأولى في الأفاتار
                    if (data.user_name) {
                        const avatarElements = document.querySelectorAll('.user-avatar');
                        avatarElements.forEach(element => {
                            element.textContent = data.user_name.charAt(0).toUpperCase();
                        });
                    }
                }
            })
            .catch(error => {
                console.log('خطأ في تحديث معلومات الـ header:', error);
            });
    }

    // تحديث المعلومات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث فوري عند تحميل الصفحة
        setTimeout(updateHeaderInfo, 500);

        // تحديث كل 30 ثانية
        setInterval(updateHeaderInfo, 30000);

        // إصلاح القائمة المنسدلة للمستخدم - تأخير أقل
        setTimeout(function() {
            initializeUserDropdown();
        }, 100);
    });

    // وظيفة إصلاح القائمة المنسدلة
    function initializeUserDropdown() {
        console.log('بدء تهيئة القائمة المنسدلة...');

        const dropdownToggle = document.querySelector('#userDropdown');
        const dropdownMenu = document.querySelector('.dropdown-menu');

        console.log('عنصر التبديل:', dropdownToggle);
        console.log('القائمة المنسدلة:', dropdownMenu);

        if (dropdownToggle && dropdownMenu) {
            // التأكد من تحميل Bootstrap
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap متاح، محاولة إنشاء dropdown...');
                try {
                    // إنشاء dropdown instance جديد
                    const dropdown = new bootstrap.Dropdown(dropdownToggle);
                    console.log('✅ تم تهيئة القائمة المنسدلة بنجاح');

                    // إضافة معالج للنقر على الروابط داخل القائمة
                    dropdownMenu.addEventListener('click', function(e) {
                        if (e.target.tagName === 'A' || e.target.closest('a')) {
                            console.log('تم النقر على رابط، إغلاق القائمة...');
                            dropdown.hide();
                        }
                    });
                } catch (error) {
                    console.error('❌ خطأ في Bootstrap dropdown:', error);
                    useAlternativeDropdown(dropdownToggle, dropdownMenu);
                }
            } else {
                console.log('⚠️ Bootstrap غير محمل، استخدام حل بديل');
                useAlternativeDropdown(dropdownToggle, dropdownMenu);
            }
        } else {
            console.error('❌ لم يتم العثور على عناصر القائمة المنسدلة');
            console.log('dropdownToggle:', dropdownToggle);
            console.log('dropdownMenu:', dropdownMenu);
        }
    }

    // وظيفة الحل البديل للقائمة المنسدلة
    function useAlternativeDropdown(toggle, menu) {
        console.log('استخدام الحل البديل للقائمة المنسدلة');

        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('تم النقر على القائمة المنسدلة (حل بديل)');

            // إغلاق جميع القوائم المنسدلة الأخرى
            document.querySelectorAll('.dropdown-menu.show').forEach(function(otherMenu) {
                if (otherMenu !== menu) {
                    otherMenu.classList.remove('show');
                    otherMenu.style.display = 'none';
                }
            });

            // تبديل حالة القائمة الحالية
            if (menu.classList.contains('show')) {
                menu.classList.remove('show');
                menu.style.display = 'none';
                toggle.setAttribute('aria-expanded', 'false');
            } else {
                menu.classList.add('show');
                menu.style.display = 'block';
                toggle.setAttribute('aria-expanded', 'true');
            }
        });

        // إغلاق القائمة عند النقر خارجها أو على رابط داخلها
        document.addEventListener('click', function(e) {
            // إذا كان النقر على رابط داخل القائمة، اتركه يعمل وأغلق القائمة
            if (menu.contains(e.target) && e.target.tagName === 'A') {
                menu.classList.remove('show');
                menu.style.display = 'none';
                toggle.setAttribute('aria-expanded', 'false');
                return; // اترك الرابط يعمل
            }

            // إغلاق القائمة عند النقر خارجها
            if (!toggle.contains(e.target) && !menu.contains(e.target)) {
                menu.classList.remove('show');
                menu.style.display = 'none';
                toggle.setAttribute('aria-expanded', 'false');
            }
        });

        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && menu.classList.contains('show')) {
                menu.classList.remove('show');
                menu.style.display = 'none';
                toggle.setAttribute('aria-expanded', 'false');
                toggle.focus();
            }
        });
    }



    // تحديث عند العودة للصفحة (عند تغيير التبويب)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(updateHeaderInfo, 200);
        }
    });

    // تحديث عند النقر على أي رابط داخلي
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a');
        if (link && link.href && link.href.includes(window.location.hostname)) {
            setTimeout(updateHeaderInfo, 1000);
        }
    });
    </script>

    <!-- نظام الإشعارات -->
    <script src="assets/js/notifications.js"></script>

    <?php
    // تضمين نظام تتبع الأنشطة للموظفين
    if (isset($_SESSION['employee_id'])) {
        // تعريف الصفحة الحالية لتجنب التتبع التلقائي في بعض الصفحات
        if (!defined('SKIP_AUTO_PAGE_TRACKING')) {
            require_once __DIR__ . '/shift_activity_tracker.php';
        }

        // التحقق من وجود شيفت نشط
        $has_active_shift = false;
        try {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM employee_shifts
                WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
            ");
            $stmt->execute([$_SESSION['employee_id']]);
            $result = $stmt->fetch();
            $has_active_shift = $result['count'] > 0;
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }

        // إضافة بيانات للـ JavaScript
        echo '<script>
            document.body.dataset.isEmployee = "true";
            document.body.dataset.hasActiveShift = "' . ($has_active_shift ? 'true' : 'false') . '";
        </script>';

        // تحميل JavaScript للتتبع
        if ($has_active_shift) {
            echo '<script src="assets/js/shift_tracker.js"></script>';
        }
    }
    ?>
<?php
/**
 * مقارنة بين التصميم القديم والجديد
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>مقارنة التصاميم</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', sans-serif; background: #f8f9fa; }
        .comparison-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .design-preview { border: 2px solid #ddd; border-radius: 10px; overflow: hidden; height: 400px; }
        .design-preview iframe { width: 100%; height: 100%; border: none; }
        .feature-list { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .feature-item { padding: 10px 0; border-bottom: 1px solid #eee; }
        .feature-item:last-child { border-bottom: none; }
        .old-design { border-color: #dc3545; }
        .new-design { border-color: #28a745; }
        .comparison-header { text-align: center; margin-bottom: 40px; }
        .vs-badge { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                   background: #007bff; color: white; padding: 10px 20px; border-radius: 50px; 
                   font-weight: bold; z-index: 10; }
    </style>
</head>
<body>
<div class='comparison-container'>
    <div class='comparison-header'>
        <h1 class='text-primary mb-3'>
            <i class='fas fa-exchange-alt'></i>
            مقارنة التصاميم
        </h1>
        <p class='lead text-muted'>مقارنة بين التصميم القديم (القائمة العلوية) والتصميم الجديد (القائمة الجانبية)</p>
    </div>
    
    <div class='row position-relative mb-5'>
        <div class='vs-badge'>
            <i class='fas fa-arrows-alt-h'></i> VS
        </div>
        
        <!-- التصميم القديم -->
        <div class='col-md-6 mb-4'>
            <div class='card'>
                <div class='card-header bg-danger text-white text-center'>
                    <h4><i class='fas fa-window-maximize'></i> التصميم القديم</h4>
                    <small>القائمة العلوية التقليدية</small>
                </div>
                <div class='card-body p-0'>
                    <div class='design-preview old-design'>
                        <div style='background: #007bff; color: white; padding: 10px; text-align: center;'>
                            <strong>القائمة العلوية</strong><br>
                            <small>الرئيسية | الجلسات | الأجهزة | العملاء | الإعدادات</small>
                        </div>
                        <div style='padding: 20px; height: calc(100% - 50px); background: white;'>
                            <h5>المحتوى الرئيسي</h5>
                            <p>المحتوى يظهر تحت القائمة العلوية</p>
                            <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                                بطاقة تجريبية
                            </div>
                            <div style='background: #e9ecef; padding: 15px; border-radius: 5px;'>
                                بطاقة أخرى
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- التصميم الجديد -->
        <div class='col-md-6 mb-4'>
            <div class='card'>
                <div class='card-header bg-success text-white text-center'>
                    <h4><i class='fas fa-sidebar'></i> التصميم الجديد</h4>
                    <small>القائمة الجانبية المحسنة</small>
                </div>
                <div class='card-body p-0'>
                    <div class='design-preview new-design' style='display: flex;'>
                        <div style='width: 200px; background: linear-gradient(180deg, #2c3e50, #34495e); color: white; padding: 15px; font-size: 0.9rem;'>
                            <div style='text-align: center; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid rgba(255,255,255,0.2);'>
                                <strong>PlayGood</strong><br>
                                <small style='color: #bdc3c7;'>مالك المحل</small>
                            </div>
                            <div style='margin-bottom: 10px; color: #bdc3c7; font-size: 0.7rem; text-transform: uppercase;'>القسم الرئيسي</div>
                            <div style='padding: 8px 0; border-right: 3px solid #3498db; padding-right: 10px; background: rgba(52,152,219,0.2);'>
                                <i class='fas fa-home'></i> لوحة التحكم
                            </div>
                            <div style='padding: 8px 0;'><i class='fas fa-play'></i> الجلسات</div>
                            <div style='padding: 8px 0;'><i class='fas fa-gamepad'></i> الأجهزة</div>
                            <div style='padding: 8px 0;'><i class='fas fa-users'></i> العملاء</div>
                        </div>
                        <div style='flex: 1; padding: 15px; background: white;'>
                            <h6 style='color: #2c3e50; margin-bottom: 15px;'>
                                <i class='fas fa-home'></i> لوحة التحكم
                            </h6>
                            <div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 15px; border-radius: 8px; margin: 8px 0; text-align: center;'>
                                <strong>150</strong><br>العملاء
                            </div>
                            <div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 8px 0;'>
                                بطاقة محسنة
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مقارنة المميزات -->
    <div class='row'>
        <div class='col-md-6 mb-4'>
            <div class='feature-list'>
                <h5 class='text-danger mb-3'>
                    <i class='fas fa-times-circle'></i> التصميم القديم
                </h5>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>القائمة العلوية:</strong> تأخذ مساحة من الشاشة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>التنقل:</strong> صعوبة في الوصول للصفحات
                </div>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>الموبايل:</strong> قائمة منسدلة معقدة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>التنظيم:</strong> عشوائي وغير مرتب
                </div>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>المساحة:</strong> استغلال ضعيف للشاشة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-minus text-warning'></i>
                    <strong>التفاعل:</strong> تأثيرات بسيطة
                </div>
            </div>
        </div>
        
        <div class='col-md-6 mb-4'>
            <div class='feature-list'>
                <h5 class='text-success mb-3'>
                    <i class='fas fa-check-circle'></i> التصميم الجديد
                </h5>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>القائمة الجانبية:</strong> مساحة أكبر للمحتوى
                </div>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>التنقل:</strong> سهولة وسرعة في الوصول
                </div>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>الموبايل:</strong> قائمة منزلقة سهلة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>التنظيم:</strong> مجموعات منطقية ومرتبة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>المساحة:</strong> استغلال مثالي للشاشة
                </div>
                <div class='feature-item'>
                    <i class='fas fa-check text-success'></i>
                    <strong>التفاعل:</strong> تأثيرات متقدمة وجذابة
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات التحسين -->
    <div class='row mb-4'>
        <div class='col-md-12'>
            <div class='card border-primary'>
                <div class='card-header bg-primary text-white text-center'>
                    <h4><i class='fas fa-chart-line'></i> إحصائيات التحسين</h4>
                </div>
                <div class='card-body'>
                    <div class='row text-center'>
                        <div class='col-md-3'>
                            <div class='card border-success'>
                                <div class='card-body'>
                                    <i class='fas fa-expand fa-2x text-success mb-2'></i>
                                    <h3 class='text-success'>+25%</h3>
                                    <p>مساحة أكبر للمحتوى</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-3'>
                            <div class='card border-info'>
                                <div class='card-body'>
                                    <i class='fas fa-mouse-pointer fa-2x text-info mb-2'></i>
                                    <h3 class='text-info'>+40%</h3>
                                    <p>سهولة التنقل</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-3'>
                            <div class='card border-warning'>
                                <div class='card-body'>
                                    <i class='fas fa-mobile-alt fa-2x text-warning mb-2'></i>
                                    <h3 class='text-warning'>+60%</h3>
                                    <p>تحسن تجربة الموبايل</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-3'>
                            <div class='card border-primary'>
                                <div class='card-body'>
                                    <i class='fas fa-star fa-2x text-primary mb-2'></i>
                                    <h3 class='text-primary'>+50%</h3>
                                    <p>تحسن التجربة العامة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار الاختبار -->
    <div class='row'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-dark text-white text-center'>
                    <h4><i class='fas fa-rocket'></i> اختبار التصميم الجديد</h4>
                </div>
                <div class='card-body text-center'>
                    <div class='alert alert-success'>
                        <i class='fas fa-check-circle fa-2x mb-2'></i><br>
                        <strong>تم تحديث جميع صفحات الموقع بنجاح!</strong><br>
                        يمكنك الآن تصفح الموقع بالتصميم الجديد المحسن.
                    </div>
                    
                    <div class='row'>
                        <div class='col-md-3 mb-3'>
                            <a href='client/test_sidebar_design.php' class='btn btn-primary btn-lg w-100'>
                                <i class='fas fa-palette'></i><br>
                                صفحة اختبار التصميم
                            </a>
                        </div>
                        <div class='col-md-3 mb-3'>
                            <a href='client/dashboard.php' class='btn btn-success btn-lg w-100'>
                                <i class='fas fa-home'></i><br>
                                لوحة التحكم
                            </a>
                        </div>
                        <div class='col-md-3 mb-3'>
                            <a href='client/sessions.php' class='btn btn-info btn-lg w-100'>
                                <i class='fas fa-play'></i><br>
                                إدارة الجلسات
                            </a>
                        </div>
                        <div class='col-md-3 mb-3'>
                            <a href='client/employees.php' class='btn btn-warning btn-lg w-100'>
                                <i class='fas fa-users'></i><br>
                                إدارة الموظفين
                            </a>
                        </div>
                    </div>
                    
                    <div class='mt-4'>
                        <h5>المميزات الجديدة المضافة:</h5>
                        <div class='row'>
                            <div class='col-md-6'>
                                <ul class='list-group text-start'>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        قائمة جانبية ثابتة ومنظمة
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        تصميم متجاوب للموبايل والتابلت
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        تأثيرات CSS متقدمة وجذابة
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        تنظيم المحتوى في مجموعات منطقية
                                    </li>
                                </ul>
                            </div>
                            <div class='col-md-6'>
                                <ul class='list-group text-start'>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        استغلال أمثل لمساحة الشاشة
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        تفاعلات JavaScript محسنة
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        نظام إشعارات متطور
                                    </li>
                                    <li class='list-group-item'>
                                        <i class='fas fa-check text-success me-2'></i>
                                        دعم كامل لجميع المتصفحات
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class='alert alert-info mt-4'>
                        <h6><i class='fas fa-lightbulb'></i> نصائح للاستخدام:</h6>
                        <ul class='text-start mb-0'>
                            <li>في الموبايل: اضغط على زر القائمة (☰) لفتح القائمة الجانبية</li>
                            <li>يمكن تخصيص الألوان والتصميم من ملفات CSS</li>
                            <li>جميع الصفحات تم تحديثها تلقائياً للتصميم الجديد</li>
                            <li>النظام يحفظ تفضيلاتك تلقائياً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات تقنية -->
    <div class='row'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-secondary text-white'>
                    <h5><i class='fas fa-code'></i> المعلومات التقنية</h5>
                </div>
                <div class='card-body'>
                    <div class='row'>
                        <div class='col-md-6'>
                            <h6>الملفات المنشأة:</h6>
                            <ul>
                                <li><code>includes/header_sidebar_only.php</code> - Header جديد</li>
                                <li><code>includes/footer_sidebar_only.php</code> - Footer جديد</li>
                                <li><code>assets/css/sidebar-enhanced.css</code> - CSS محسن</li>
                                <li><code>assets/js/sidebar-enhanced.js</code> - JavaScript محسن</li>
                            </ul>
                        </div>
                        <div class='col-md-6'>
                            <h6>التحسينات المطبقة:</h6>
                            <ul>
                                <li>استبدال القائمة العلوية بالجانبية</li>
                                <li>تحسين تجربة المستخدم</li>
                                <li>إضافة تأثيرات تفاعلية</li>
                                <li>تحسين الأداء والسرعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
<script>
// تأثيرات تفاعلية للمقارنة
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 150);
    });
});
</script>

</body>
</html>";
?>

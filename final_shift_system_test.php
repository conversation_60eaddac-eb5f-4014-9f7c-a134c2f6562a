<?php
/**
 * اختبار نهائي شامل لنظام الشيفت
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار نهائي لنظام الشيفت</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center text-primary mb-4'>
        <i class='fas fa-clipboard-check'></i>
        اختبار نهائي شامل لنظام الشيفت
    </h1>";

$tests = [];
$total_score = 0;

// اختبار 1: فحص الجداول
echo "<div class='card mb-3'>
    <div class='card-header bg-primary text-white'>
        <h5><i class='fas fa-database'></i> اختبار 1: فحص الجداول</h5>
    </div>
    <div class='card-body'>";

$required_tables = [
    'employee_shifts' => 'جدول الشيفت الجديد',
    'employee_shift_assignments' => 'جدول تخصيصات الورديات',
    'employee_shift_activities' => 'جدول الأنشطة',
    'shift_notifications' => 'جدول الإشعارات'
];

$tables_score = 0;
foreach ($required_tables as $table => $description) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> $description موجود</div>";
            $tables_score++;
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> $description غير موجود</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في فحص $description</div>";
    }
}

$tests['tables'] = ($tables_score / count($required_tables)) * 100;
echo "<div class='alert alert-info'>النتيجة: $tables_score/" . count($required_tables) . " (" . round($tests['tables']) . "%)</div>";
echo "</div></div>";

// اختبار 2: فحص Views
echo "<div class='card mb-3'>
    <div class='card-header bg-success text-white'>
        <h5><i class='fas fa-eye'></i> اختبار 2: فحص Views</h5>
    </div>
    <div class='card-body'>";

$views_score = 0;
try {
    $pdo->query("SELECT * FROM active_employee_shifts LIMIT 1");
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> view active_employee_shifts يعمل</div>";
    $views_score++;
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> view active_employee_shifts لا يعمل: " . $e->getMessage() . "</div>";
}

try {
    $result = $pdo->query("SELECT * FROM shift_quick_stats");
    $stats = $result->fetchAll();
    echo "<div class='alert alert-success'><i class='fas fa-check'></i> view shift_quick_stats يعمل</div>";
    
    foreach ($stats as $stat) {
        echo "<small class='text-muted'>• {$stat['stat_description']}: {$stat['stat_value']}</small><br>";
    }
    $views_score++;
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> view shift_quick_stats لا يعمل: " . $e->getMessage() . "</div>";
}

$tests['views'] = ($views_score / 2) * 100;
echo "<div class='alert alert-info'>النتيجة: $views_score/2 (" . round($tests['views']) . "%)</div>";
echo "</div></div>";

// اختبار 3: فحص الصفحات
echo "<div class='card mb-3'>
    <div class='card-header bg-warning text-white'>
        <h5><i class='fas fa-file'></i> اختبار 3: فحص الصفحات</h5>
    </div>
    <div class='card-body'>";

$required_files = [
    'client/employee_shift_start.php' => 'صفحة إدارة الشيفت',
    'client/employee_shift_reports.php' => 'صفحة التقارير',
    'client/api/log_shift_activity.php' => 'API تسجيل الأنشطة',
    'client/api/get_shift_details.php' => 'API تفاصيل الشيفت',
    'client/assets/js/shift_tracker.js' => 'JavaScript التتبع'
];

$files_score = 0;
foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> $description موجود</div>";
        $files_score++;
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> $description غير موجود</div>";
    }
}

$tests['files'] = ($files_score / count($required_files)) * 100;
echo "<div class='alert alert-info'>النتيجة: $files_score/" . count($required_files) . " (" . round($tests['files']) . "%)</div>";
echo "</div></div>";

// اختبار 4: اختبار وظيفي
echo "<div class='card mb-3'>
    <div class='card-header bg-info text-white'>
        <h5><i class='fas fa-cogs'></i> اختبار 4: اختبار وظيفي</h5>
    </div>
    <div class='card-body'>";

$functional_score = 0;

// اختبار إدراج شيفت
try {
    $employee_stmt = $pdo->query("SELECT id, client_id FROM employees WHERE is_active = 1 LIMIT 1");
    $employee = $employee_stmt->fetch();
    
    if ($employee) {
        // إدراج شيفت تجريبي
        $stmt = $pdo->prepare("
            INSERT INTO employee_shifts (employee_id, client_id, shift_status, location_info) 
            VALUES (?, ?, 'active', 'اختبار وظيفي')
        ");
        $stmt->execute([$employee['id'], $employee['client_id']]);
        $test_shift_id = $pdo->lastInsertId();
        
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنشاء شيفت تجريبي #$test_shift_id</div>";
        $functional_score++;
        
        // اختبار إدراج نشاط
        $stmt = $pdo->prepare("
            INSERT INTO employee_shift_activities (
                shift_id, employee_id, client_id, activity_type,
                activity_title, activity_description
            ) VALUES (?, ?, ?, 'other', 'اختبار وظيفي', 'نشاط تجريبي')
        ");
        $stmt->execute([$test_shift_id, $employee['id'], $employee['client_id']]);
        
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تسجيل نشاط تجريبي</div>";
        $functional_score++;
        
        // اختبار إنهاء الشيفت
        $stmt = $pdo->prepare("
            UPDATE employee_shifts 
            SET shift_end_time = NOW(), actual_duration_minutes = 5, shift_status = 'completed'
            WHERE shift_id = ?
        ");
        $stmt->execute([$test_shift_id]);
        
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم إنهاء الشيفت التجريبي</div>";
        $functional_score++;
        
        // حذف البيانات التجريبية
        $pdo->prepare("DELETE FROM employee_shift_activities WHERE shift_id = ?")->execute([$test_shift_id]);
        $pdo->prepare("DELETE FROM employee_shifts WHERE shift_id = ?")->execute([$test_shift_id]);
        
        echo "<div class='alert alert-info'><i class='fas fa-trash'></i> تم حذف البيانات التجريبية</div>";
        
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> لا يوجد موظفين لاختبار النظام</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في الاختبار الوظيفي: " . $e->getMessage() . "</div>";
}

$tests['functional'] = ($functional_score / 3) * 100;
echo "<div class='alert alert-info'>النتيجة: $functional_score/3 (" . round($tests['functional']) . "%)</div>";
echo "</div></div>";

// النتيجة النهائية
$total_score = array_sum($tests) / count($tests);

echo "<div class='card border-primary'>
    <div class='card-header bg-primary text-white text-center'>
        <h3><i class='fas fa-trophy'></i> النتيجة النهائية</h3>
    </div>
    <div class='card-body text-center'>";

if ($total_score >= 90) {
    echo "<div class='alert alert-success' style='font-size: 1.2em;'>
        <i class='fas fa-check-circle fa-2x mb-2'></i><br>
        <strong>ممتاز! (" . round($total_score) . "%)</strong><br>
        نظام الشيفت يعمل بشكل مثالي!
    </div>";
} elseif ($total_score >= 70) {
    echo "<div class='alert alert-warning' style='font-size: 1.2em;'>
        <i class='fas fa-exclamation-circle fa-2x mb-2'></i><br>
        <strong>جيد (" . round($total_score) . "%)</strong><br>
        النظام يعمل مع بعض المشاكل البسيطة
    </div>";
} else {
    echo "<div class='alert alert-danger' style='font-size: 1.2em;'>
        <i class='fas fa-times-circle fa-2x mb-2'></i><br>
        <strong>يحتاج إصلاح (" . round($total_score) . "%)</strong><br>
        يرجى مراجعة الأخطاء أعلاه
    </div>";
}

echo "<div class='row mt-4'>
    <div class='col-md-6'>
        <h5>تفاصيل النتائج:</h5>
        <ul class='list-group'>
            <li class='list-group-item d-flex justify-content-between'>
                الجداول <span class='badge bg-primary'>" . round($tests['tables']) . "%</span>
            </li>
            <li class='list-group-item d-flex justify-content-between'>
                Views <span class='badge bg-success'>" . round($tests['views']) . "%</span>
            </li>
            <li class='list-group-item d-flex justify-content-between'>
                الملفات <span class='badge bg-warning'>" . round($tests['files']) . "%</span>
            </li>
            <li class='list-group-item d-flex justify-content-between'>
                الوظائف <span class='badge bg-info'>" . round($tests['functional']) . "%</span>
            </li>
        </ul>
    </div>
    <div class='col-md-6'>
        <h5>روابط مفيدة:</h5>
        <div class='d-grid gap-2'>
            <a href='client/employee_shift_start.php' class='btn btn-primary'>
                <i class='fas fa-play'></i> إدارة الشيفت
            </a>
            <a href='client/employee_shift_reports.php' class='btn btn-success'>
                <i class='fas fa-chart-line'></i> تقارير الشيفت
            </a>
            <a href='client/test_shift_system.php' class='btn btn-info'>
                <i class='fas fa-vial'></i> اختبار تفاعلي
            </a>
            <a href='client/dashboard.php' class='btn btn-secondary'>
                <i class='fas fa-home'></i> لوحة التحكم
            </a>
        </div>
    </div>
</div>";

echo "</div></div>";

// معلومات إضافية
echo "<div class='card mt-4'>
    <div class='card-header'>
        <h5><i class='fas fa-info-circle'></i> معلومات النظام</h5>
    </div>
    <div class='card-body'>
        <div class='row'>
            <div class='col-md-6'>
                <h6>الجداول المنشأة:</h6>
                <ul>
                    <li><code>employee_shifts</code> - الشيفت النشط</li>
                    <li><code>employee_shift_activities</code> - تسجيل الأنشطة</li>
                    <li><code>employee_shift_assignments</code> - تخصيصات الورديات</li>
                    <li><code>shift_notifications</code> - الإشعارات</li>
                </ul>
            </div>
            <div class='col-md-6'>
                <h6>المميزات:</h6>
                <ul>
                    <li>تسجيل تلقائي للأنشطة</li>
                    <li>تقارير مفصلة</li>
                    <li>واجهة سهلة للموظفين</li>
                    <li>إحصائيات شاملة للإدارة</li>
                </ul>
            </div>
        </div>
        
        <div class='alert alert-info mt-3'>
            <h6><i class='fas fa-lightbulb'></i> ملاحظات مهمة:</h6>
            <ul class='mb-0'>
                <li>النظام الجديد منفصل عن نظام الورديات القديم</li>
                <li>يمكن استخدام كلا النظامين بشكل متوازي</li>
                <li>النظام الجديد مخصص للتتبع اليومي للموظفين</li>
                <li>النظام القديم مخصص لجدولة الورديات المسبقة</li>
            </ul>
        </div>
    </div>
</div>";

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>

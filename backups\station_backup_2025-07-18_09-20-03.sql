-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaD<PERSON>, for Win64 (AMD64)
--
-- Host: localhost    Database: station
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `additional_income`
--

DROP TABLE IF EXISTS `additional_income`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `additional_income` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `income_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `income_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_income_client` (`client_id`),
  KEY `idx_income_type` (`income_type_id`),
  KEY `idx_income_date` (`income_date`),
  CONSTRAINT `fk_income_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_income_type` FOREIGN KEY (`income_type_id`) REFERENCES `income_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الإيرادات الإضافية';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `additional_income`
--

LOCK TABLES `additional_income` WRITE;
/*!40000 ALTER TABLE `additional_income` DISABLE KEYS */;
/*!40000 ALTER TABLE `additional_income` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_notifications`
--

DROP TABLE IF EXISTS `admin_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `notification_type` enum('shift_completed','shift_summary_ready','critical_event','unresolved_issues','attendance_alert','performance_alert','system_alert','maintenance_reminder','other') NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `related_id` int(11) DEFAULT NULL COMMENT 'معرف العنصر المرتبط (shift_id, event_id, etc)',
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `is_read` tinyint(1) DEFAULT 0,
  `is_dismissed` tinyint(1) DEFAULT 0,
  `action_url` varchar(500) DEFAULT NULL COMMENT 'رابط الإجراء المطلوب',
  `action_label` varchar(100) DEFAULT NULL COMMENT 'نص زر الإجراء',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL,
  `dismissed_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء صلاحية الإشعار',
  PRIMARY KEY (`notification_id`),
  KEY `idx_notifications_client` (`client_id`),
  KEY `idx_notifications_type` (`notification_type`),
  KEY `idx_notifications_priority` (`priority`),
  KEY `idx_notifications_status` (`is_read`,`is_dismissed`),
  KEY `idx_notifications_date` (`created_at`),
  CONSTRAINT `admin_notifications_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_notifications`
--

LOCK TABLES `admin_notifications` WRITE;
/*!40000 ALTER TABLE `admin_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_page_permissions`
--

DROP TABLE IF EXISTS `admin_page_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_page_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_page` (`admin_id`,`page_id`),
  KEY `granted_by` (`granted_by`),
  KEY `idx_admin_page_permissions_admin` (`admin_id`),
  KEY `idx_admin_page_permissions_page` (`page_id`),
  CONSTRAINT `admin_page_permissions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE,
  CONSTRAINT `admin_page_permissions_ibfk_2` FOREIGN KEY (`page_id`) REFERENCES `admin_pages` (`page_id`) ON DELETE CASCADE,
  CONSTRAINT `admin_page_permissions_ibfk_3` FOREIGN KEY (`granted_by`) REFERENCES `admins` (`admin_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول صلاحيات الوصول لصفحات الإدمن';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_page_permissions`
--

LOCK TABLES `admin_page_permissions` WRITE;
/*!40000 ALTER TABLE `admin_page_permissions` DISABLE KEYS */;
INSERT INTO `admin_page_permissions` VALUES (16,1,12,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(17,1,13,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(18,1,14,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(19,1,15,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(20,1,16,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(21,1,17,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(22,1,18,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(23,1,19,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(24,1,20,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(25,1,21,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(26,1,22,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(27,1,23,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(28,1,24,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58'),(29,1,25,1,NULL,'2025-06-20 12:31:58','2025-06-20 12:31:58');
/*!40000 ALTER TABLE `admin_page_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `admin_page_permissions_detailed`
--

DROP TABLE IF EXISTS `admin_page_permissions_detailed`;
/*!50001 DROP VIEW IF EXISTS `admin_page_permissions_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `admin_page_permissions_detailed` AS SELECT
 1 AS `admin_id`,
  1 AS `username`,
  1 AS `full_name`,
  1 AS `role`,
  1 AS `admin_active`,
  1 AS `page_id`,
  1 AS `page_name`,
  1 AS `page_label`,
  1 AS `page_url`,
  1 AS `page_icon`,
  1 AS `category`,
  1 AS `description`,
  1 AS `is_default`,
  1 AS `required_role`,
  1 AS `has_permission`,
  1 AS `granted_at`,
  1 AS `updated_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `admin_pages`
--

DROP TABLE IF EXISTS `admin_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للمديرين الجدد',
  `required_role` enum('super_admin','admin','any') DEFAULT 'any' COMMENT 'الدور المطلوب للوصول للصفحة',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `page_name` (`page_name`),
  KEY `idx_admin_pages_name` (`page_name`),
  KEY `idx_admin_pages_category` (`category`),
  KEY `idx_admin_pages_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='جدول الصفحات المتاحة في لوحة تحكم الإدمن';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_pages`
--

LOCK TABLES `admin_pages` WRITE;
/*!40000 ALTER TABLE `admin_pages` DISABLE KEYS */;
INSERT INTO `admin_pages` VALUES (12,'dashboard','لوحة التحكم الرئيسية','dashboard.php','fas fa-dashboard','main','لوحة التحكم الرئيسية مع الإحصائيات والمعلومات العامة',1,1,'any','2025-06-20 12:31:58'),(13,'profile','الملف الشخصي','profile.php','fas fa-user','main','إدارة الملف الشخصي للإدمن وتعديل البيانات',1,1,'any','2025-06-20 12:31:58'),(14,'clients','إدارة العملاء','clients.php','fas fa-users','clients','عرض وإدارة قائمة العملاء وإضافة عملاء جدد',1,1,'any','2025-06-20 12:31:58'),(15,'client_permissions','صلاحيات العملاء','client_permissions.php','fas fa-user-shield','clients','إدارة صلاحيات الوصول للصفحات للعملاء',1,1,'admin','2025-06-20 12:31:58'),(16,'client_devices','أجهزة العملاء','client_devices.php','fas fa-desktop','clients','إدارة وعرض أجهزة العملاء المختلفة',1,1,'any','2025-06-20 12:31:58'),(17,'reports','التقارير والإحصائيات','reports.php','fas fa-chart-bar','reports','عرض التقارير المالية والإحصائيات المفصلة',1,1,'any','2025-06-20 12:31:58'),(18,'settings','إعدادات النظام','settings.php','fas fa-cog','settings','إعدادات النظام العامة والتحكم في الميزات',1,1,'admin','2025-06-20 12:31:58'),(19,'admin_permissions','صلاحيات المديرين','admin_permissions.php','fas fa-user-cog','admin','إدارة صلاحيات المديرين للوصول للصفحات',1,0,'super_admin','2025-06-20 12:31:58'),(20,'download_backup','تحميل النسخ الاحتياطية','download_backup.php','fas fa-download','system','تحميل ملفات النسخ الاحتياطية',1,0,'admin','2025-06-20 12:31:58'),(21,'test_client_devices','اختبار أجهزة العملاء','test_client_devices.php','fas fa-vial','testing','صفحة اختبار وظائف أجهزة العملاء',1,0,'super_admin','2025-06-20 12:31:58'),(22,'ensure_device_columns','صيانة أعمدة الأجهزة','ensure_device_columns.php','fas fa-tools','maintenance','صفحة صيانة وإصلاح أعمدة جدول الأجهزة',1,0,'super_admin','2025-06-20 12:31:58'),(23,'system_logs','سجلات النظام','system_logs.php','fas fa-file-alt','admin','عرض سجلات النظام والأنشطة (للتطوير المستقبلي)',1,0,'super_admin','2025-06-20 12:31:58'),(24,'admins','إدارة المديرين','admins.php','fas fa-user-tie','admin','إضافة وإدارة المديرين (للتطوير المستقبلي)',1,0,'super_admin','2025-06-20 12:31:58'),(25,'backup','إدارة النسخ الاحتياطية','backup.php','fas fa-hdd','system','إنشاء وإدارة النسخ الاحتياطية (للتطوير المستقبلي)',1,0,'admin','2025-06-20 12:31:58');
/*!40000 ALTER TABLE `admin_pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_settings`
--

DROP TABLE IF EXISTS `admin_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات النظام العامة';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_settings`
--

LOCK TABLES `admin_settings` WRITE;
/*!40000 ALTER TABLE `admin_settings` DISABLE KEYS */;
INSERT INTO `admin_settings` VALUES (1,'system_name','نظام إدارة مراكز الألعاب','اسم النظام','2025-06-20 08:34:30','2025-06-20 08:34:30'),(2,'system_version','2.0','إصدار النظام','2025-06-20 08:34:30','2025-06-20 08:34:30'),(3,'backup_enabled','1','تفعيل النسخ الاحتياطي','2025-06-20 08:34:30','2025-06-20 08:34:30'),(4,'system_maintenance','0','وضع الصيانة','2025-06-20 08:34:30','2025-07-05 10:29:11'),(5,'max_backup_files','10','الحد الأقصى لملفات النسخ الاحتياطي','2025-06-20 08:34:30','2025-06-20 08:34:30'),(6,'timezone','Asia/Riyadh','المنطقة الزمنية','2025-06-20 08:34:30','2025-06-20 08:34:30'),(7,'language','ar','اللغة الافتراضية','2025-06-20 08:34:30','2025-06-20 08:34:30'),(8,'currency','EGP','العملة الافتراضية','2025-06-20 08:34:30','2025-07-05 10:29:23'),(9,'date_format','Y-m-d','تنسيق التاريخ','2025-06-20 08:34:30','2025-06-20 08:34:30'),(10,'time_format','H:i','تنسيق الوقت','2025-06-20 08:34:30','2025-06-20 08:34:30');
/*!40000 ALTER TABLE `admin_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_admin_username` (`username`),
  KEY `idx_admin_email` (`email`),
  KEY `idx_admin_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المديرين';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES (1,'admin','<EMAIL>','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','مدير النظام','super_admin',1,'2025-06-20 08:34:30','2025-07-11 11:23:26');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blocked_ips`
--

DROP TABLE IF EXISTS `blocked_ips`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blocked_ips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `reason` text DEFAULT NULL,
  `blocked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `blocked_until` timestamp NULL DEFAULT NULL,
  `permanent` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_address` (`ip_address`),
  KEY `idx_ip_until` (`ip_address`,`blocked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blocked_ips`
--

LOCK TABLES `blocked_ips` WRITE;
/*!40000 ALTER TABLE `blocked_ips` DISABLE KEYS */;
/*!40000 ALTER TABLE `blocked_ips` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_settings`
--

DROP TABLE IF EXISTS `business_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `business_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `uk_business_setting` (`client_id`,`setting_key`),
  KEY `idx_business_setting_client` (`client_id`),
  CONSTRAINT `fk_business_setting_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات العملاء';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_settings`
--

LOCK TABLES `business_settings` WRITE;
/*!40000 ALTER TABLE `business_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cafeteria_items`
--

DROP TABLE IF EXISTS `cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `category` varchar(100) DEFAULT NULL,
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT 'سعر التكلفة',
  `stock_quantity` int(11) DEFAULT 0 COMMENT 'الكمية المتوفرة',
  `min_stock_level` int(11) DEFAULT 5 COMMENT 'الحد الأدنى للمخزن',
  `max_stock_level` int(11) DEFAULT 100 COMMENT 'الحد الأقصى للمخزن',
  `unit` varchar(20) DEFAULT 'قطعة' COMMENT 'وحدة القياس',
  `barcode` varchar(100) DEFAULT NULL COMMENT 'الباركود',
  `supplier` varchar(200) DEFAULT NULL COMMENT 'المورد',
  `status` enum('available','low_stock','out_of_stock','discontinued') DEFAULT 'available',
  `image_url` varchar(500) DEFAULT NULL,
  `last_restock_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_item_client` (`client_id`),
  KEY `idx_item_category` (`category_id`),
  KEY `idx_item_status` (`status`),
  KEY `idx_item_barcode` (`barcode`),
  KEY `idx_cafeteria_category` (`category`),
  KEY `idx_cafeteria_client` (`client_id`),
  KEY `idx_cafeteria_status` (`status`),
  KEY `idx_cafeteria_items_client_id` (`client_id`),
  KEY `idx_cafeteria_items_category` (`category`),
  CONSTRAINT `fk_item_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_item_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='منتجات الكافتيريا';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cafeteria_items`
--

LOCK TABLES `cafeteria_items` WRITE;
/*!40000 ALTER TABLE `cafeteria_items` DISABLE KEYS */;
INSERT INTO `cafeteria_items` VALUES (11,2,NULL,'كشرى','',10.00,'ماكولات',0.00,0,5,100,'قطعة',NULL,NULL,'available',NULL,NULL,'2025-06-21 15:50:42','2025-06-21 15:50:42'),(12,1,NULL,'شاي','',10.00,'1',0.00,100,5,100,'قطعة',NULL,NULL,'available',NULL,NULL,'2025-06-23 18:26:45','2025-07-05 16:30:32');
/*!40000 ALTER TABLE `cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-tag',
  `color` varchar(7) DEFAULT '#007bff',
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`category_id`),
  KEY `idx_category_client` (`client_id`),
  KEY `idx_category_active` (`is_active`),
  CONSTRAINT `fk_category_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='فئات المنتجات';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
INSERT INTO `categories` VALUES (22,1,'1',NULL,'fas fa-tag','#007bff',0,1,'2025-06-20 09:20:40','2025-06-20 09:20:40'),(23,2,'ماكولات',NULL,'fas fa-tag','#007bff',0,1,'2025-06-21 15:50:37','2025-06-21 15:50:37');
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_page_permissions`
--

DROP TABLE IF EXISTS `client_page_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_page_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `page_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الصلاحية',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_page_permission` (`client_id`,`page_id`),
  KEY `idx_client_page_perm_client` (`client_id`),
  KEY `idx_client_page_perm_page` (`page_id`),
  CONSTRAINT `fk_client_page_perm_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_client_page_perm_page` FOREIGN KEY (`page_id`) REFERENCES `client_pages` (`page_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات صفحات العملاء';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_page_permissions`
--

LOCK TABLES `client_page_permissions` WRITE;
/*!40000 ALTER TABLE `client_page_permissions` DISABLE KEYS */;
INSERT INTO `client_page_permissions` VALUES (2,1,1,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(3,1,2,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(4,1,3,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(5,1,4,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(6,1,5,1,1,'2025-06-20 14:44:21','2025-07-05 10:30:15'),(7,1,7,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(8,1,8,0,1,'2025-06-20 14:44:21','2025-06-21 06:38:26'),(9,1,9,1,1,'2025-06-20 14:44:21','2025-06-20 14:44:21'),(10,1,6,1,1,'2025-06-21 05:22:44','2025-06-21 05:22:44'),(12,1,13,1,1,'2025-06-21 06:27:29','2025-06-21 06:27:29'),(13,1,29,0,1,'2025-06-21 06:27:48','2025-07-11 08:12:46'),(14,1,33,1,1,'2025-06-21 06:40:11','2025-06-21 06:40:11'),(15,1,21,1,1,'2025-06-21 07:23:21','2025-07-05 10:35:10'),(16,2,6,1,1,'2025-06-21 15:46:14','2025-06-21 15:46:14'),(17,2,13,1,1,'2025-06-21 15:46:19','2025-06-21 15:46:19'),(18,2,29,1,1,'2025-06-21 15:46:22','2025-06-21 15:46:22'),(19,2,34,1,1,'2025-06-21 15:46:24','2025-06-21 15:46:24'),(20,2,21,1,1,'2025-06-21 15:46:30','2025-06-21 15:46:30'),(21,2,33,1,1,'2025-06-21 20:08:37','2025-06-21 20:08:37'),(22,2,22,0,1,'2025-06-21 20:22:25','2025-06-21 20:22:59'),(23,2,24,0,1,'2025-06-21 20:22:27','2025-06-21 20:23:00'),(24,1,22,1,1,'2025-06-21 20:23:15','2025-06-21 20:23:15'),(25,1,24,1,1,'2025-06-21 20:23:19','2025-06-21 20:23:19'),(26,1,23,1,1,'2025-07-05 10:04:37','2025-07-05 10:04:37'),(27,1,25,1,1,'2025-07-05 10:05:04','2025-07-05 10:35:33'),(28,1,26,1,1,'2025-07-05 10:05:05','2025-07-05 10:35:31'),(29,1,34,1,1,'2025-07-05 10:30:09','2025-07-11 08:12:46'),(30,1,27,1,1,'2025-07-05 10:35:23','2025-07-05 10:35:23'),(31,2,1,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(32,2,2,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(33,2,3,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(34,2,4,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(35,2,5,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(36,2,7,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(37,2,8,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05'),(38,2,9,1,NULL,'2025-07-11 11:24:05','2025-07-11 11:24:05');
/*!40000 ALTER TABLE `client_page_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_pages`
--

DROP TABLE IF EXISTS `client_pages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL,
  `page_label` varchar(200) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_icon` varchar(50) DEFAULT 'fas fa-file',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0 COMMENT 'هل هذه الصفحة متاحة افتراضياً للعملاء الجدد',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `uk_page_name` (`page_name`),
  KEY `idx_page_active` (`is_active`),
  KEY `idx_page_default` (`is_default`)
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صفحات النظام المتاحة للعملاء';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_pages`
--

LOCK TABLES `client_pages` WRITE;
/*!40000 ALTER TABLE `client_pages` DISABLE KEYS */;
INSERT INTO `client_pages` VALUES (1,'dashboard','لوحة التحكم','dashboard.php','fas fa-tachometer-alt','main','الصفحة الرئيسية وإحصائيات المحل',1,1,'2025-06-20 08:34:49'),(2,'profile','الملف الشخصي','profile.php','fas fa-user','main','إدارة بيانات الحساب الشخصي',1,1,'2025-06-20 08:34:49'),(3,'devices','إدارة الأجهزة','devices.php','fas fa-gamepad','devices','إضافة وإدارة أجهزة الألعاب',1,1,'2025-06-20 08:34:49'),(4,'sessions','إدارة الجلسات','sessions.php','fas fa-play-circle','sessions','بدء وإنهاء جلسات اللعب',1,1,'2025-06-20 08:34:49'),(5,'customers','إدارة العملاء','customers.php','fas fa-users','customers','إدارة بيانات عملاء المحل',1,1,'2025-06-20 08:34:49'),(6,'cafeteria','إدارة الكافتيريا','cafeteria.php','fas fa-coffee','cafeteria','إدارة منتجات الكافتيريا',1,0,'2025-06-20 08:34:49'),(7,'invoices','الفواتير','invoices.php','fas fa-file-invoice','invoices','إدارة وطباعة الفواتير',1,1,'2025-06-20 08:34:49'),(8,'reports','التقارير','reports.php','fas fa-chart-bar','reports','تقارير مالية وإحصائية',1,1,'2025-06-20 08:34:49'),(9,'settings','الإعدادات','settings.php','fas fa-cog','settings','إعدادات النظام والمحل',1,1,'2025-06-20 08:34:49'),(13,'rooms','إدارة الغرف','rooms.php','fas fa-door-open','devices','تنظيم الأجهزة في غرف',1,0,'2025-06-21 06:26:23'),(15,'check_active_sessions','فحص الجلسات النشطة','check_active_sessions.php','fas fa-search','sessions','فحص حالة الجلسات النشطة',1,0,'2025-06-21 06:26:23'),(17,'customer_details','تفاصيل العميل','customer_details.php','fas fa-user-circle','customers','عرض تفاصيل عميل محدد',1,0,'2025-06-21 06:26:23'),(18,'edit_customer','تعديل العميل','edit_customer.php','fas fa-user-edit','customers','تعديل بيانات العميل',1,0,'2025-06-21 06:26:23'),(19,'delete_customer','حذف العميل','delete_customer.php','fas fa-user-times','customers','حذف عميل من النظام',1,0,'2025-06-21 06:26:24'),(21,'orders','إدارة الأوردرات','orders.php','fas fa-shopping-cart','orders','إدارة الطلبات والأوردرات المستقلة',1,0,'2025-06-21 06:26:24'),(22,'employees','إدارة الموظفين','employees.php','fas fa-user-tie','employees','إدارة بيانات الموظفين',1,0,'2025-06-21 06:26:24'),(23,'employee_permissions','صلاحيات الموظفين','employee_permissions.php','fas fa-user-shield','permissions','إدارة صلاحيات الموظفين',1,0,'2025-06-21 06:26:24'),(24,'employee-login','تسجيل دخول الموظف','employee-login.php','fas fa-sign-in-alt','employees','صفحة تسجيل دخول الموظفين',1,0,'2025-06-21 06:26:24'),(25,'attendance','الحضور والانصراف','attendance.php','fas fa-user-check','attendance','تسجيل حضور وانصراف الموظفين',1,0,'2025-06-21 06:26:24'),(26,'quick_attendance','الحضور السريع','quick_attendance.php','fas fa-clock','attendance','تسجيل حضور سريع للموظفين',1,0,'2025-06-21 06:26:24'),(27,'shifts','إدارة الورديات','shifts.php','fas fa-calendar-alt','shifts','تنظيم ورديات العمل',1,0,'2025-06-21 06:26:24'),(28,'shift_reports','تقارير الورديات','shift_reports.php','fas fa-calendar-check','shifts','تقارير مفصلة عن الورديات',1,0,'2025-06-21 06:26:24'),(29,'finances','الإدارة المالية','finances.php','fas fa-money-bill-wave','finances','إدارة الأمور المالية العامة',1,0,'2025-06-21 06:26:24'),(32,'invoice','عرض الفاتورة','invoice.php','fas fa-file-invoice-dollar','invoices','عرض فاتورة محددة',1,0,'2025-06-21 06:26:24'),(33,'invoice_settings','إعدادات الفواتير','invoice_settings.php','fas fa-cog','invoices','تخصيص شكل ومحتوى الفواتير',1,0,'2025-06-21 06:26:24'),(34,'inventory','إدارة المخزون','inventory.php','fas fa-boxes','inventory','إدارة مخزون المنتجات',1,0,'2025-06-21 06:26:24'),(35,'reservations','الحجوزات','reservations.php','fas fa-calendar-check','reservations','إدارة حجوزات العملاء',1,0,'2025-06-21 06:26:24'),(37,'backup_handler','النسخ الاحتياطي','backup_handler.php','fas fa-database','backup','إدارة النسخ الاحتياطية',1,0,'2025-06-21 06:26:24');
/*!40000 ALTER TABLE `client_pages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_plan_limits`
--

DROP TABLE IF EXISTS `client_plan_limits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_plan_limits` (
  `limit_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `feature_type` enum('devices','products','pages','employees','customers','sessions','storage') NOT NULL,
  `feature_name` varchar(100) NOT NULL,
  `custom_limit` int(11) DEFAULT -1 COMMENT 'حد مخصص يتجاوز حد الخطة الأساسية',
  `is_active` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL COMMENT 'معرف الأدمن الذي منح الحد المخصص',
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ انتهاء الحد المخصص',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`limit_id`),
  UNIQUE KEY `uk_client_feature_limit` (`client_id`,`feature_type`,`feature_name`),
  KEY `idx_client_limits_client` (`client_id`),
  KEY `idx_client_limits_active` (`is_active`),
  CONSTRAINT `fk_client_limits_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حدود العملاء المخصصة';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_plan_limits`
--

LOCK TABLES `client_plan_limits` WRITE;
/*!40000 ALTER TABLE `client_plan_limits` DISABLE KEYS */;
/*!40000 ALTER TABLE `client_plan_limits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_theme_settings`
--

DROP TABLE IF EXISTS `client_theme_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_theme_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `primary_color` varchar(7) DEFAULT '#0d6efd',
  `secondary_color` varchar(7) DEFAULT '#6c757d',
  `accent_color` varchar(7) DEFAULT '#20c997',
  `background_color` varchar(7) DEFAULT '#ffffff',
  `text_color` varchar(7) DEFAULT '#212529',
  `header_style` enum('top','sidebar') DEFAULT 'top',
  `sidebar_position` enum('right','left') DEFAULT 'right',
  `theme_mode` enum('light','dark','auto') DEFAULT 'light',
  `font_family` varchar(100) DEFAULT 'Cairo, sans-serif',
  `font_size` enum('small','medium','large') DEFAULT 'medium',
  `custom_css` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_theme_client` (`client_id`),
  CONSTRAINT `fk_theme_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الثيم';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_theme_settings`
--

LOCK TABLES `client_theme_settings` WRITE;
/*!40000 ALTER TABLE `client_theme_settings` DISABLE KEYS */;
INSERT INTO `client_theme_settings` VALUES (1,1,'#0d6efd','#6c757d','#20c997','#ffffff','#212529','top','right','light','Cairo, sans-serif','medium',NULL,'2025-06-21 15:38:45','2025-06-21 15:38:45'),(2,2,'#0d6efd','#6c757d','#20c997','#ffffff','#212529','top','right','light','Cairo, sans-serif','medium',NULL,'2025-06-21 15:44:28','2025-06-21 20:09:24');
/*!40000 ALTER TABLE `client_theme_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `client_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `subscription_plan` enum('basic','premium','enterprise') DEFAULT 'basic',
  `subscription_start` date DEFAULT NULL,
  `subscription_end` date DEFAULT NULL,
  `business_type` varchar(50) DEFAULT 'gaming_center',
  `description` text DEFAULT NULL,
  `working_hours` varchar(255) DEFAULT 'من 9 صباحاً إلى 12 منتصف الليل',
  `is_active` tinyint(1) DEFAULT 1,
  `backup_enabled` tinyint(1) DEFAULT 1 COMMENT 'صلاحية النسخ الاحتياطي',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`client_id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_client_email` (`email`),
  KEY `idx_client_phone` (`phone`),
  KEY `idx_client_active` (`is_active`),
  KEY `idx_client_subscription` (`subscription_plan`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العملاء (أصحاب المحلات)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'ahmedeltarek','ahmed','<EMAIL>','01141453034','$2y$10$xLGf1BOu.yt9o.6ciFeXYeMkNuYu3z8tmJRQSPBU6YmFZ0wHgmOBy','5','الجيزة','basic','2025-06-20','2025-07-20','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل',1,0,'2025-06-20 09:18:20','2025-06-20 10:53:48'),(2,'gdf','gfdg','<EMAIL>','dfgfd','$2y$10$OwuUwPQyUecvXq8s0G4ToupLKtrhIxZM.Qgi51U.TzxSunw0M/IcG','','','basic','2025-06-21','2025-07-21','gaming_center',NULL,'من 9 صباحاً إلى 12 منتصف الليل',1,0,'2025-06-21 15:41:35','2025-06-21 20:31:50');
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `address` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `total_visits` int(11) DEFAULT 0,
  `total_spent` decimal(10,2) DEFAULT 0.00,
  `last_visit` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`customer_id`),
  KEY `idx_customer_client` (`client_id`),
  KEY `idx_customer_phone` (`phone`),
  KEY `idx_customer_active` (`is_active`),
  CONSTRAINT `fk_customer_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول عملاء المحل';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES (1,1,'mazen','***********',NULL,NULL,NULL,NULL,NULL,0,0.00,NULL,1,'2025-06-21 05:16:53','2025-06-21 05:16:53');
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `customers_stats`
--

DROP TABLE IF EXISTS `customers_stats`;
/*!50001 DROP VIEW IF EXISTS `customers_stats`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `customers_stats` AS SELECT
 1 AS `customer_id`,
  1 AS `client_id`,
  1 AS `business_name`,
  1 AS `name`,
  1 AS `phone`,
  1 AS `email`,
  1 AS `total_sessions`,
  1 AS `total_spent`,
  1 AS `avg_session_cost`,
  1 AS `last_visit`,
  1 AS `first_visit`,
  1 AS `registration_date` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC','Nintendo') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 0.00,
  `single_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الفردي',
  `multi_rate` decimal(10,2) DEFAULT NULL COMMENT 'سعر اللعب الجماعي',
  `status` enum('available','occupied','maintenance','inactive') DEFAULT 'available',
  `room_id` int(11) DEFAULT NULL,
  `specifications` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'مواصفات الجهاز' CHECK (json_valid(`specifications`)),
  `maintenance_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `idx_device_client` (`client_id`),
  KEY `idx_device_status` (`status`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_device_room` (`room_id`),
  CONSTRAINT `fk_device_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_device_room` FOREIGN KEY (`room_id`) REFERENCES `rooms` (`room_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأجهزة';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
INSERT INTO `devices` VALUES (1,1,'جهاز 1','PS4',10.00,10.00,20.00,'available',NULL,NULL,NULL,'2025-06-20 17:19:03','2025-07-05 09:11:20'),(2,1,'جهاز تجريبي','PS5',15.00,10.00,20.00,'maintenance',NULL,NULL,NULL,'2025-06-21 05:34:05','2025-07-05 09:24:09'),(4,2,'434','PS4',4.00,5.00,3.00,'occupied',NULL,NULL,NULL,'2025-06-21 15:42:56','2025-06-21 20:24:03'),(5,1,'جهاز 3','PS4',15.00,15.00,20.00,'available',NULL,NULL,NULL,'2025-06-21 16:59:10','2025-07-05 17:17:18'),(6,1,'جهاز 4','Xbox',15.00,15.00,25.00,'available',NULL,NULL,NULL,'2025-06-21 17:22:30','2025-06-28 10:43:56'),(7,1,'جهاز 5','PS5',15.00,15.00,20.00,'available',NULL,NULL,NULL,'2025-06-21 17:22:43','2025-06-23 19:13:24');
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `devices_stats`
--

DROP TABLE IF EXISTS `devices_stats`;
/*!50001 DROP VIEW IF EXISTS `devices_stats`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `devices_stats` AS SELECT
 1 AS `device_id`,
  1 AS `client_id`,
  1 AS `business_name`,
  1 AS `device_name`,
  1 AS `device_type`,
  1 AS `status`,
  1 AS `hourly_rate`,
  1 AS `total_sessions`,
  1 AS `total_minutes`,
  1 AS `total_revenue`,
  1 AS `avg_session_duration`,
  1 AS `last_used` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `employee_permissions`
--

DROP TABLE IF EXISTS `employee_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_permission` (`employee_id`,`permission_id`),
  KEY `idx_emp_perm_employee` (`employee_id`),
  KEY `idx_emp_perm_permission` (`permission_id`),
  KEY `idx_employee_permissions_permission` (`permission_id`),
  CONSTRAINT `fk_emp_perm_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_emp_perm_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات الموظفين';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_permissions`
--

LOCK TABLES `employee_permissions` WRITE;
/*!40000 ALTER TABLE `employee_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employee_shifts`
--

DROP TABLE IF EXISTS `employee_shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_shifts` (
  `assignment_id` int(11) NOT NULL AUTO_INCREMENT,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `role_in_shift` enum('supervisor','regular','backup') DEFAULT 'regular',
  `is_mandatory` tinyint(1) DEFAULT 0 COMMENT 'هل الحضور إجباري',
  `assigned_by` int(11) DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('assigned','confirmed','declined','cancelled') DEFAULT 'assigned',
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`assignment_id`),
  UNIQUE KEY `unique_employee_shift` (`shift_id`,`employee_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `employee_shifts_ibfk_1` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `employee_shifts_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_shifts`
--

LOCK TABLES `employee_shifts` WRITE;
/*!40000 ALTER TABLE `employee_shifts` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employees`
--

DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text DEFAULT NULL,
  `role` enum('manager','cashier','waiter','cleaner','technician') NOT NULL DEFAULT 'cashier',
  `salary` decimal(10,2) NOT NULL DEFAULT 0.00,
  `hire_date` date NOT NULL,
  `birth_date` date DEFAULT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `emergency_contact` varchar(100) DEFAULT NULL,
  `emergency_phone` varchar(20) DEFAULT NULL,
  `custom_permissions` tinyint(1) DEFAULT 0 COMMENT 'صلاحيات مخصصة',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_employee_username_client` (`client_id`,`username`),
  KEY `idx_employee_client` (`client_id`),
  KEY `idx_employee_role` (`role`),
  KEY `idx_employee_active` (`is_active`),
  CONSTRAINT `fk_employee_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الموظفين';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employees`
--

LOCK TABLES `employees` WRITE;
/*!40000 ALTER TABLE `employees` DISABLE KEYS */;
INSERT INTO `employees` VALUES (1,1,'mido','$2y$10$nu3ktx6aRUb3CbYA.8m9Gu0J9qnWImZSCOgOXC7aSaWxMR3NRl0P2','شادي','<EMAIL>','01141453034','','cashier',1000.00,'2025-06-01',NULL,NULL,NULL,NULL,0,1,'2025-07-11 11:33:47','2025-06-28 10:53:23','2025-07-11 11:33:47');
/*!40000 ALTER TABLE `employees` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expense_types`
--

DROP TABLE IF EXISTS `expense_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expense_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-money-bill',
  `color` varchar(7) DEFAULT '#dc3545',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expense_type_client` (`client_id`),
  KEY `idx_expense_type_active` (`is_active`),
  CONSTRAINT `fk_expense_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع المصروفات';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expense_types`
--

LOCK TABLES `expense_types` WRITE;
/*!40000 ALTER TABLE `expense_types` DISABLE KEYS */;
INSERT INTO `expense_types` VALUES (1,2,'كهرباء','','fas fa-money-bill','#dc3545',1,'2025-06-21 20:04:19','2025-06-21 20:04:19'),(2,2,'كهرباء','','fas fa-money-bill','#dc3545',1,'2025-06-21 20:04:37','2025-06-21 20:04:37');
/*!40000 ALTER TABLE `expense_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `expenses`
--

DROP TABLE IF EXISTS `expenses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `expense_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `expense_date` date NOT NULL,
  `receipt_number` varchar(50) DEFAULT NULL,
  `receipt_image` varchar(500) DEFAULT NULL,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_expense_client` (`client_id`),
  KEY `idx_expense_type` (`expense_type_id`),
  KEY `idx_expense_date` (`expense_date`),
  CONSTRAINT `fk_expense_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_expense_type` FOREIGN KEY (`expense_type_id`) REFERENCES `expense_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المصروفات';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `expenses`
--

LOCK TABLES `expenses` WRITE;
/*!40000 ALTER TABLE `expenses` DISABLE KEYS */;
/*!40000 ALTER TABLE `expenses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `income_types`
--

DROP TABLE IF EXISTS `income_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `income_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-coins',
  `color` varchar(7) DEFAULT '#28a745',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_income_type_client` (`client_id`),
  KEY `idx_income_type_active` (`is_active`),
  CONSTRAINT `fk_income_type_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أنواع الإيرادات';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `income_types`
--

LOCK TABLES `income_types` WRITE;
/*!40000 ALTER TABLE `income_types` DISABLE KEYS */;
/*!40000 ALTER TABLE `income_types` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `inventory_detailed`
--

DROP TABLE IF EXISTS `inventory_detailed`;
/*!50001 DROP VIEW IF EXISTS `inventory_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `inventory_detailed` AS SELECT
 1 AS `id`,
  1 AS `client_id`,
  1 AS `business_name`,
  1 AS `name`,
  1 AS `description`,
  1 AS `price`,
  1 AS `cost_price`,
  1 AS `profit_per_unit`,
  1 AS `profit_percentage`,
  1 AS `stock_quantity`,
  1 AS `min_stock_level`,
  1 AS `max_stock_level`,
  1 AS `stock_status`,
  1 AS `status`,
  1 AS `category_name`,
  1 AS `barcode`,
  1 AS `supplier`,
  1 AS `last_restock_date`,
  1 AS `created_at`,
  1 AS `updated_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `inventory_movements`
--

DROP TABLE IF EXISTS `inventory_movements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('in','out','adjustment','expired','damaged','returned') NOT NULL,
  `quantity` int(11) NOT NULL,
  `previous_quantity` int(11) NOT NULL,
  `new_quantity` int(11) NOT NULL,
  `unit_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `reference_type` enum('purchase','sale','session','order','manual','system') DEFAULT 'manual',
  `reference_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_movement_client` (`client_id`),
  KEY `idx_movement_product` (`product_id`),
  KEY `idx_movement_type` (`movement_type`),
  KEY `idx_movement_date` (`created_at`),
  CONSTRAINT `fk_movement_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_movement_product` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حركات المخزون';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_movements`
--

LOCK TABLES `inventory_movements` WRITE;
/*!40000 ALTER TABLE `inventory_movements` DISABLE KEYS */;
/*!40000 ALTER TABLE `inventory_movements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice_settings`
--

DROP TABLE IF EXISTS `invoice_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `company_logo` varchar(500) DEFAULT NULL,
  `header_color` varchar(7) DEFAULT '#dc3545',
  `footer_text` text DEFAULT 'شكراً لاختياركم خدماتنا',
  `footer_color` varchar(7) DEFAULT '#000000',
  `company_address` text DEFAULT NULL,
  `company_phone` varchar(20) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `show_qr_code` tinyint(1) DEFAULT 1,
  `show_barcode` tinyint(1) DEFAULT 0,
  `auto_print` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `billing_method` enum('actual_time','hourly_rounding','first_minute_full_hour') DEFAULT 'actual_time' COMMENT 'طريقة حساب التكلفة',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_invoice_settings_client` (`client_id`),
  CONSTRAINT `fk_invoice_settings_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات الفواتير';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice_settings`
--

LOCK TABLES `invoice_settings` WRITE;
/*!40000 ALTER TABLE `invoice_settings` DISABLE KEYS */;
INSERT INTO `invoice_settings` VALUES (1,1,NULL,'#000000','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى تحياتي احمد يحيي','#000000','الجيزة','01026362111',NULL,0,0,0,'2025-06-21 06:40:35','2025-07-05 09:11:10','actual_time'),(3,2,NULL,'#dc3545','شكراً لاختياركم خدماتنا - نتطلع لخدمتكم مرة أخرى','#000000','شارع فريد - الأحساء','01026362111',NULL,1,0,0,'2025-06-21 20:10:13','2025-06-21 20:10:42','actual_time');
/*!40000 ALTER TABLE `invoice_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoices`
--

DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `session_id` int(11) DEFAULT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `time_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `products_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','transfer','other') DEFAULT 'cash',
  `payment_status` enum('pending','paid','cancelled','refunded') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`invoice_id`),
  UNIQUE KEY `uk_invoice_number_client` (`client_id`,`invoice_number`),
  KEY `idx_invoice_client` (`client_id`),
  KEY `idx_invoice_session` (`session_id`),
  KEY `idx_invoice_customer` (`customer_id`),
  KEY `idx_invoice_status` (`payment_status`),
  KEY `idx_invoice_date` (`created_at`),
  CONSTRAINT `fk_invoice_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invoice_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_invoice_session` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الفواتير';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoices`
--

LOCK TABLES `invoices` WRITE;
/*!40000 ALTER TABLE `invoices` DISABLE KEYS */;
INSERT INTO `invoices` VALUES (1,1,7,'202506210007',NULL,20.00,10.00,0.00,0.00,30.00,'cash','pending',NULL,1,'2025-06-21 06:39:09','2025-06-21 06:39:09'),(2,1,2,'202506210002',NULL,20.00,10.00,0.00,0.00,30.00,'cash','pending',NULL,1,'2025-06-21 06:39:31','2025-06-21 06:39:31'),(3,2,8,'202506210008',NULL,5.00,60.00,0.00,0.00,65.00,'cash','pending',NULL,2,'2025-06-21 15:54:01','2025-06-21 15:54:01'),(4,1,10,'202506210010',NULL,10.00,0.00,0.00,0.00,10.00,'cash','pending',NULL,1,'2025-06-21 17:00:16','2025-06-21 17:00:16'),(5,1,9,'202506210009',NULL,10.00,10.00,0.00,0.00,20.00,'cash','pending',NULL,1,'2025-06-21 17:00:20','2025-06-21 17:00:20'),(6,1,11,'202506210011',NULL,15.00,0.00,0.00,0.00,15.00,'cash','pending',NULL,1,'2025-06-21 17:00:23','2025-06-21 17:00:23'),(7,1,16,'202506210016',NULL,10.00,0.00,0.00,0.00,10.00,'cash','pending',NULL,1,'2025-06-21 17:26:56','2025-06-21 17:26:56'),(8,2,17,'202506210017',NULL,0.83,20.00,0.00,0.00,20.83,'cash','pending',NULL,2,'2025-06-21 20:12:12','2025-06-21 20:12:12'),(9,1,14,'202506230014',NULL,735.00,0.00,0.00,0.00,735.00,'cash','pending',NULL,1,'2025-06-23 18:23:18','2025-06-23 18:23:18'),(10,1,15,'202506230015',NULL,747.50,0.00,0.00,0.00,747.50,'cash','pending',NULL,1,'2025-06-23 19:13:24','2025-06-23 19:13:24'),(11,1,13,'202506280013',NULL,2420.00,0.00,0.00,0.00,2420.00,'cash','pending',NULL,1,'2025-06-28 10:43:56','2025-06-28 10:43:56'),(12,1,12,'202506280012',NULL,1614.33,0.00,0.00,0.00,1614.33,'cash','pending',NULL,1,'2025-06-28 10:47:28','2025-06-28 10:47:28'),(13,1,20,'202506280020',NULL,7.25,0.00,0.00,0.00,7.25,'cash','pending',NULL,1,'2025-06-28 11:31:12','2025-06-28 11:31:12'),(14,1,19,'202506280019',NULL,6.83,0.00,0.00,0.00,6.83,'cash','pending',NULL,1,'2025-06-28 11:32:08','2025-06-28 11:32:08'),(15,1,21,'202507030021',NULL,1160.67,10.00,0.00,0.00,1170.67,'cash','pending',NULL,1,'2025-07-03 07:36:42','2025-07-03 07:36:42'),(16,1,22,'202507030022',NULL,0.00,10.00,0.00,0.00,10.00,'cash','pending',NULL,1,'2025-07-03 07:40:54','2025-07-03 07:40:54'),(17,1,23,'202507050023',NULL,0.50,0.00,0.00,0.00,0.50,'cash','pending',NULL,1,'2025-07-05 09:11:20','2025-07-05 09:11:20'),(18,1,24,'202507050024',NULL,1.75,20.00,0.00,0.00,21.75,'cash','pending',NULL,1,'2025-07-05 09:19:58','2025-07-05 09:19:58'),(19,1,25,'202507050025',NULL,0.75,10.00,0.00,0.00,10.75,'cash','pending',NULL,1,'2025-07-05 17:17:18','2025-07-05 17:17:18');
/*!40000 ALTER TABLE `invoices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_attempts`
--

DROP TABLE IF EXISTS `login_attempts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `user_type` enum('admin','client','employee') NOT NULL,
  `username` varchar(255) DEFAULT NULL,
  `attempt_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `success` tinyint(1) DEFAULT 0,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ip_time` (`ip_address`,`attempt_time`),
  KEY `idx_username_time` (`username`,`attempt_time`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_attempts`
--

LOCK TABLES `login_attempts` WRITE;
/*!40000 ALTER TABLE `login_attempts` DISABLE KEYS */;
INSERT INTO `login_attempts` VALUES (1,'::1','client','<EMAIL>','2025-06-23 18:05:17',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(2,'::1','client','<EMAIL>','2025-06-23 18:41:58',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(3,'::1','client','<EMAIL>','2025-06-23 18:42:14',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(4,'::1','client','<EMAIL>','2025-06-23 18:52:06',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(5,'::1','client','<EMAIL>','2025-06-23 18:56:48',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(6,'::1','client','<EMAIL>','2025-06-23 18:57:11',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(7,'::1','client','<EMAIL>','2025-06-23 18:57:16',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(8,'::1','client','<EMAIL>','2025-06-23 18:57:30',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(9,'::1','client','<EMAIL>','2025-06-23 18:57:42',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(10,'::1','client','<EMAIL>','2025-06-23 19:01:40',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(11,'::1','client','<EMAIL>','2025-06-23 19:02:02',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(12,'::1','client','<EMAIL>','2025-06-28 10:43:48',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'),(13,'::1','client','<EMAIL>','2025-07-03 07:31:30',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(14,'::1','client','<EMAIL>','2025-07-05 09:06:54',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(15,'::1','admin','admin','2025-07-05 10:03:55',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'),(16,'::1','client','<EMAIL>','2025-07-05 10:28:48',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(17,'::1','client','<EMAIL>','2025-07-05 10:29:05',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(18,'::1','client','<EMAIL>','2025-07-05 10:35:36',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(19,'::1','client','<EMAIL>','2025-07-05 10:44:02',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(20,'::1','client','<EMAIL>','2025-07-05 10:45:46',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(21,'::1','client','<EMAIL>','2025-07-05 16:29:19',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(22,'41.237.153.214','client','<EMAIL>','2025-07-05 17:13:38',1,'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36'),(23,'::1','client','<EMAIL>','2025-07-11 07:59:45',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(24,'::1','admin','admin','2025-07-11 08:11:30',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'),(25,'::1','client','<EMAIL>','2025-07-11 11:13:24',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'),(26,'::1','admin','admin','2025-07-11 11:23:26',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'),(27,'::1','client','<EMAIL>','2025-07-11 11:37:25',1,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0');
/*!40000 ALTER TABLE `login_attempts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `monthly_financial_summary`
--

DROP TABLE IF EXISTS `monthly_financial_summary`;
/*!50001 DROP VIEW IF EXISTS `monthly_financial_summary`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `monthly_financial_summary` AS SELECT
 1 AS `client_id`,
  1 AS `year`,
  1 AS `month`,
  1 AS `type`,
  1 AS `total_amount` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_order_items_order_id` (`order_id`),
  KEY `idx_order_items_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES (1,1,12,1,10.00,10.00,'2025-07-05 09:07:23');
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` int(11) DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` enum('cash','card','other') DEFAULT 'cash',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_orders_client_id` (`client_id`),
  KEY `idx_orders_customer_id` (`customer_id`),
  KEY `idx_orders_status` (`status`),
  KEY `idx_orders_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES (1,1,1,NULL,'ORD-20250705-9037',10.00,'pending','cash','',1,0.00,0.00,'2025-07-05 09:07:23','2025-07-05 09:07:23');
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_name` varchar(100) NOT NULL,
  `permission_label` varchar(200) NOT NULL,
  `permission_category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `permission_name` (`permission_name`),
  KEY `idx_permission_category` (`permission_category`),
  KEY `idx_permission_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='صلاحيات النظام';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'dashboard_view','عرض لوحة التحكم','dashboard','صلاحية عرض لوحة التحكم الرئيسية',1,'2025-06-20 08:34:30'),(2,'devices_view','عرض الأجهزة','devices','صلاحية عرض قائمة الأجهزة',1,'2025-06-20 08:34:30'),(3,'devices_add','إضافة أجهزة','devices','صلاحية إضافة أجهزة جديدة',1,'2025-06-20 08:34:30'),(4,'devices_edit','تعديل الأجهزة','devices','صلاحية تعديل بيانات الأجهزة',1,'2025-06-20 08:34:30'),(5,'devices_delete','حذف الأجهزة','devices','صلاحية حذف الأجهزة',1,'2025-06-20 08:34:30'),(6,'sessions_view','عرض الجلسات','sessions','صلاحية عرض جلسات اللعب',1,'2025-06-20 08:34:30'),(7,'sessions_start','بدء الجلسات','sessions','صلاحية بدء جلسات جديدة',1,'2025-06-20 08:34:30'),(8,'sessions_end','إنهاء الجلسات','sessions','صلاحية إنهاء الجلسات',1,'2025-06-20 08:34:30'),(9,'customers_view','عرض العملاء','customers','صلاحية عرض قائمة العملاء',1,'2025-06-20 08:34:30'),(10,'customers_add','إضافة عملاء','customers','صلاحية إضافة عملاء جدد',1,'2025-06-20 08:34:30'),(11,'customers_edit','تعديل العملاء','customers','صلاحية تعديل بيانات العملاء',1,'2025-06-20 08:34:30'),(12,'customers_delete','حذف العملاء','customers','صلاحية حذف العملاء',1,'2025-06-20 08:34:30'),(13,'products_view','عرض المنتجات','products','صلاحية عرض منتجات الكافتيريا',1,'2025-06-20 08:34:30'),(14,'products_add','إضافة منتجات','products','صلاحية إضافة منتجات جديدة',1,'2025-06-20 08:34:30'),(15,'products_edit','تعديل المنتجات','products','صلاحية تعديل المنتجات',1,'2025-06-20 08:34:30'),(16,'products_delete','حذف المنتجات','products','صلاحية حذف المنتجات',1,'2025-06-20 08:34:30'),(17,'invoices_view','عرض الفواتير','invoices','صلاحية عرض الفواتير',1,'2025-06-20 08:34:30'),(18,'invoices_create','إنشاء فواتير','invoices','صلاحية إنشاء فواتير جديدة',1,'2025-06-20 08:34:30'),(19,'reports_view','عرض التقارير','reports','صلاحية عرض التقارير المالية',1,'2025-06-20 08:34:30'),(20,'settings_view','عرض الإعدادات','settings','صلاحية عرض إعدادات النظام',1,'2025-06-20 08:34:30'),(21,'settings_edit','تعديل الإعدادات','settings','صلاحية تعديل إعدادات النظام',1,'2025-06-20 08:34:30');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rooms`
--

DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rooms` (
  `room_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `room_name` varchar(100) NOT NULL,
  `room_description` text DEFAULT NULL,
  `capacity` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`room_id`),
  KEY `idx_room_client` (`client_id`),
  KEY `idx_room_active` (`is_active`),
  CONSTRAINT `fk_room_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الغرف';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rooms`
--

LOCK TABLES `rooms` WRITE;
/*!40000 ALTER TABLE `rooms` DISABLE KEYS */;
/*!40000 ALTER TABLE `rooms` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `secure_sessions`
--

DROP TABLE IF EXISTS `secure_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `secure_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` enum('admin','client','employee') NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent_hash` varchar(64) NOT NULL,
  `security_token` varchar(64) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_valid` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`,`user_type`),
  KEY `idx_security_token` (`security_token`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `secure_sessions`
--

LOCK TABLES `secure_sessions` WRITE;
/*!40000 ALTER TABLE `secure_sessions` DISABLE KEYS */;
INSERT INTO `secure_sessions` VALUES (1,'elh97usm4dqvkm2lljn3j5n3bv',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','63c2b3477c69070488128482d10998ce743abf8783bbd0063b004d6d3ef34bd0','2025-06-23 18:41:58','2025-06-23 18:42:14',0),(2,'h9cdfhuo2s9osr2h1nhjl6lujr',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','120d89382040b3785781216f0d67a982c6d8fe30bc41bea8aef2460f9d4fdbbf','2025-06-23 18:42:14','2025-06-23 18:52:06',0),(3,'c294idcliephqnp9via1kj0ci9',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','f008779da7615607b4ed9826cbec0d413b204a84642c53b7bf90ddb7afbafaed','2025-06-23 18:52:06','2025-06-23 18:56:48',0),(4,'j7bordh99dduup7fdrds3v1crr',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','e34d9db8d5ce68b1620c78b5dab023d757f8ba7c3783cec304f43f0cc1306d02','2025-06-23 18:56:48','2025-06-23 18:57:11',0),(5,'o3guunfftmqim3h2je5r8qla4d',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','74f6162fc0be7a497c74fa1c5eb172c5deb6f45bc0e80a77e3d3d60c7d738252','2025-06-23 18:57:11','2025-06-23 18:57:16',0),(6,'fr66qu2p93fcamq15g6c5vkkci',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','059b41c8a717c95b53435428b6d98e3ccfa3c111cd470df18d41c118e825ff80','2025-06-23 18:57:16','2025-06-23 18:57:30',0),(7,'fnikit5suvkrhps6dqerkt1rr6',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','7b2243596e598e68446c56ab769f735a561337563a5f63188ea43c34473aa050','2025-06-23 18:57:30','2025-06-23 18:57:42',0),(8,'8a018p9mjitq32pt87p6umku0n',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','63bff0c27b1ae3b33ecc5caf25ef840481d15edbdaf92e6b0b29a22d1b5cb980','2025-06-23 18:57:42','2025-06-23 19:01:40',0),(9,'57knaghmgif51lrcmvvrmrl5fr',1,'client','::1','9a6cd0cf7add6678cb3a110bd2448f4f5a561aa101f07758fad812afdaab09ad','01835acc90951f4bd6a862b749ca2221384653e59fe4cd704d7dde7c78631c3a','2025-06-23 19:01:40','2025-07-05 17:13:38',0);
/*!40000 ALTER TABLE `secure_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `session_products`
--

DROP TABLE IF EXISTS `session_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `session_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_session_products_session_id` (`session_id`),
  KEY `idx_session_products_product_id` (`product_id`),
  CONSTRAINT `session_products_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `sessions` (`session_id`) ON DELETE CASCADE,
  CONSTRAINT `session_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `cafeteria_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `session_products`
--

LOCK TABLES `session_products` WRITE;
/*!40000 ALTER TABLE `session_products` DISABLE KEYS */;
INSERT INTO `session_products` VALUES (1,18,12,2,10.00,'2025-07-05 09:17:04'),(2,24,12,1,10.00,'2025-07-05 09:19:23'),(4,24,12,1,10.00,'2025-07-05 09:19:41'),(5,25,12,1,10.00,'2025-07-05 17:14:26');
/*!40000 ALTER TABLE `session_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `session_security_logs`
--

DROP TABLE IF EXISTS `session_security_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `session_security_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(128) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `attack_type` enum('hijack_attempt','invalid_token','ip_mismatch','agent_mismatch','expired_session') NOT NULL,
  `details` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_attack_type` (`attack_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `session_security_logs`
--

LOCK TABLES `session_security_logs` WRITE;
/*!40000 ALTER TABLE `session_security_logs` DISABLE KEYS */;
INSERT INTO `session_security_logs` VALUES (1,'0nkgi8hhbisq0c6rdosbd6qhab','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','invalid_token','رمز الأمان غير موجود','2025-06-23 18:39:41'),(2,'jqeoi8e3u4bpiuivf4hpf14pof','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','hijack_attempt','جلسة غير صحيحة أو منتهية الصلاحية','2025-06-23 18:41:58'),(3,'r6dchji6gmgrbrpnnoe43pnmn6','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','hijack_attempt','جلسة غير صحيحة أو منتهية الصلاحية','2025-06-23 18:42:14'),(4,'2btrmog5vfka8j4jhm6bra43vj','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','hijack_attempt','جلسة غير صحيحة أو منتهية الصلاحية','2025-06-23 18:52:06'),(5,'0ah4pudck1b7dc31a8gs0lroi5','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','hijack_attempt','جلسة غير صحيحة أو منتهية الصلاحية','2025-06-23 18:56:48'),(6,'f7m6j2savl0ml8askrr84d0r61','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','hijack_attempt','جلسة غير صحيحة أو منتهية الصلاحية','2025-06-23 18:57:11');
/*!40000 ALTER TABLE `session_security_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `device_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_type` enum('hourly','single','multi') DEFAULT 'hourly',
  `game_type` varchar(100) DEFAULT NULL,
  `players_count` int(11) DEFAULT 1,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` timestamp NULL DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `hourly_rate` decimal(8,2) DEFAULT 0.00,
  `time_cost` decimal(10,2) DEFAULT 0.00,
  `products_cost` decimal(10,2) DEFAULT 0.00,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `payment_status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `updated_by` int(11) DEFAULT NULL,
  `expected_end_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`session_id`),
  KEY `idx_session_client` (`client_id`),
  KEY `idx_session_device` (`device_id`),
  KEY `idx_session_customer` (`customer_id`),
  KEY `idx_session_status` (`payment_status`),
  KEY `idx_session_date` (`start_time`),
  KEY `idx_sessions_status` (`status`),
  KEY `idx_sessions_client_id` (`client_id`),
  KEY `idx_sessions_device_id` (`device_id`),
  KEY `idx_sessions_customer_id` (`customer_id`),
  KEY `idx_sessions_start_time` (`start_time`),
  CONSTRAINT `fk_session_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_session_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`customer_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_session_device` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول جلسات اللعب';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES (2,1,1,1,'hourly','single',1,'2025-06-21 05:22:26','2025-06-21 06:39:31',0,0.00,0.00,0.00,30.00,'pending','',1,'2025-06-21 05:22:26','2025-06-21 06:39:31','completed',1,NULL),(7,1,2,1,'hourly','multiplayer',1,'2025-06-21 05:55:45','2025-06-21 06:39:08',0,0.00,0.00,0.00,30.00,'pending','',1,'2025-06-21 05:55:45','2025-06-21 06:39:08','completed',1,NULL),(8,2,4,NULL,'hourly','single',1,'2025-06-21 15:43:11','2025-06-21 15:54:01',0,0.00,0.00,0.00,65.00,'pending','',2,'2025-06-21 15:43:11','2025-06-21 15:54:01','completed',2,NULL),(9,1,1,1,'hourly','single',1,'2025-06-21 16:58:14','2025-06-21 17:00:20',0,0.00,0.00,0.00,20.00,'pending','',1,'2025-06-21 16:58:14','2025-06-21 17:00:20','completed',1,NULL),(10,1,2,1,'hourly','single',1,'2025-06-21 16:58:51','2025-06-21 17:00:16',0,0.00,0.00,0.00,10.00,'pending','',1,'2025-06-21 16:58:51','2025-06-21 17:00:16','completed',1,NULL),(11,1,5,NULL,'hourly','single',1,'2025-06-21 16:59:19','2025-06-21 17:00:23',0,0.00,0.00,0.00,15.00,'pending','',1,'2025-06-21 16:59:19','2025-06-21 17:00:23','completed',1,NULL),(12,1,1,1,'hourly','single',1,'2025-06-21 17:21:23','2025-06-28 10:47:28',0,0.00,0.00,0.00,1614.33,'pending','',1,'2025-06-21 17:21:23','2025-06-28 10:47:28','completed',1,NULL),(13,1,6,NULL,'hourly','single',1,'2025-06-21 17:22:57','2025-06-28 10:43:56',0,0.00,0.00,0.00,2420.00,'pending','',1,'2025-06-21 17:22:57','2025-06-28 10:43:56','completed',1,NULL),(14,1,5,NULL,'hourly','single',1,'2025-06-21 17:23:02','2025-06-23 18:23:18',0,0.00,0.00,0.00,735.00,'pending','',1,'2025-06-21 17:23:02','2025-06-23 18:23:18','completed',1,NULL),(15,1,7,NULL,'hourly','single',1,'2025-06-21 17:23:06','2025-06-23 19:13:24',0,0.00,0.00,0.00,747.50,'pending','',1,'2025-06-21 17:23:06','2025-06-23 19:13:24','completed',1,NULL),(16,1,2,NULL,'hourly','single',1,'2025-06-21 17:23:12','2025-06-21 17:26:56',0,0.00,0.00,0.00,10.00,'pending','',1,'2025-06-21 17:23:12','2025-06-21 17:26:56','completed',1,NULL),(17,2,4,NULL,'hourly','single',1,'2025-06-21 20:02:02','2025-06-21 20:12:12',0,0.00,0.00,0.00,20.83,'pending','',2,'2025-06-21 20:02:02','2025-06-21 20:12:12','completed',2,NULL),(18,2,4,NULL,'hourly','single',1,'2025-06-21 20:24:03',NULL,0,0.00,0.00,0.00,0.00,'pending','',2,'2025-06-21 20:24:03','2025-06-21 20:24:03','active',NULL,NULL),(19,1,1,NULL,'hourly','single',1,'2025-06-28 10:50:59','2025-06-28 11:32:08',0,0.00,0.00,0.00,6.83,'pending','',1,'2025-06-28 10:50:59','2025-06-28 11:32:08','completed',1,NULL),(20,1,5,NULL,'hourly','single',1,'2025-06-28 11:01:34','2025-06-28 11:31:12',0,0.00,0.00,0.00,7.25,'pending','',1,'2025-06-28 11:01:34','2025-06-28 11:31:12','completed',1,NULL),(21,1,1,NULL,'hourly','single',1,'2025-06-28 11:32:35','2025-07-03 07:36:42',0,0.00,0.00,0.00,1170.67,'pending','',1,'2025-06-28 11:32:35','2025-07-03 07:36:42','completed',1,NULL),(22,1,1,NULL,'hourly','single',1,'2025-07-03 07:40:34','2025-07-03 07:40:54',0,0.00,0.00,0.00,10.00,'pending','',1,'2025-07-03 07:40:34','2025-07-03 07:40:54','completed',1,NULL),(23,1,1,1,'hourly','single',1,'2025-07-05 09:07:52','2025-07-05 09:11:20',0,0.00,0.00,0.00,0.50,'pending','',1,'2025-07-05 09:07:52','2025-07-05 09:11:20','completed',1,NULL),(24,1,5,NULL,'hourly','single',1,'2025-07-05 09:12:22','2025-07-05 09:19:58',0,0.00,0.00,0.00,21.75,'pending','',1,'2025-07-05 09:12:22','2025-07-05 09:19:58','completed',1,NULL),(25,1,5,1,'hourly','single',1,'2025-07-05 17:14:15','2025-07-05 17:17:18',0,0.00,0.00,0.00,10.75,'pending','',1,'2025-07-05 17:14:15','2025-07-05 17:17:18','completed',1,NULL);
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `sessions_detailed`
--

DROP TABLE IF EXISTS `sessions_detailed`;
/*!50001 DROP VIEW IF EXISTS `sessions_detailed`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `sessions_detailed` AS SELECT
 1 AS `session_id`,
  1 AS `client_id`,
  1 AS `business_name`,
  1 AS `device_id`,
  1 AS `device_name`,
  1 AS `device_type`,
  1 AS `customer_id`,
  1 AS `customer_name`,
  1 AS `customer_phone`,
  1 AS `session_type`,
  1 AS `game_type`,
  1 AS `players_count`,
  1 AS `start_time`,
  1 AS `end_time`,
  1 AS `duration_minutes`,
  1 AS `hourly_rate`,
  1 AS `time_cost`,
  1 AS `products_cost`,
  1 AS `total_cost`,
  1 AS `payment_status`,
  1 AS `notes`,
  1 AS `created_at` */;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `shift_attendance`
--

DROP TABLE IF EXISTS `shift_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_attendance` (
  `attendance_id` int(11) NOT NULL AUTO_INCREMENT,
  `assignment_id` int(11) NOT NULL,
  `shift_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `check_in_time` timestamp NULL DEFAULT NULL,
  `check_out_time` timestamp NULL DEFAULT NULL,
  `break_start_time` timestamp NULL DEFAULT NULL,
  `break_end_time` timestamp NULL DEFAULT NULL,
  `actual_hours` decimal(4,2) DEFAULT 0.00,
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `break_hours` decimal(4,2) DEFAULT 0.00,
  `status` enum('absent','present','late','early_leave','overtime') DEFAULT 'absent',
  `late_minutes` int(11) DEFAULT 0,
  `early_leave_minutes` int(11) DEFAULT 0,
  `notes` text DEFAULT NULL,
  `recorded_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`attendance_id`),
  KEY `assignment_id` (`assignment_id`),
  KEY `idx_employee_date` (`employee_id`,`created_at`),
  KEY `idx_shift_attendance` (`shift_id`,`status`),
  CONSTRAINT `shift_attendance_ibfk_1` FOREIGN KEY (`assignment_id`) REFERENCES `employee_shifts` (`assignment_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`shift_id`) ON DELETE CASCADE,
  CONSTRAINT `shift_attendance_ibfk_3` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_attendance`
--

LOCK TABLES `shift_attendance` WRITE;
/*!40000 ALTER TABLE `shift_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_settings`
--

DROP TABLE IF EXISTS `shift_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `setting_name` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `unique_client_setting` (`client_id`,`setting_name`),
  CONSTRAINT `shift_settings_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_settings`
--

LOCK TABLES `shift_settings` WRITE;
/*!40000 ALTER TABLE `shift_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shift_templates`
--

DROP TABLE IF EXISTS `shift_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shift_templates` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_name` varchar(100) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0 COMMENT 'وردية ليلية تمتد لليوم التالي',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`template_id`),
  KEY `client_id` (`client_id`),
  CONSTRAINT `shift_templates_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shift_templates`
--

LOCK TABLES `shift_templates` WRITE;
/*!40000 ALTER TABLE `shift_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `shift_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `shifts`
--

DROP TABLE IF EXISTS `shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `shifts` (
  `shift_id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `shift_name` varchar(100) NOT NULL,
  `shift_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'مدة الاستراحة بالدقائق',
  `is_overnight` tinyint(1) DEFAULT 0,
  `max_employees` int(11) DEFAULT 1 COMMENT 'الحد الأقصى للموظفين في هذه الوردية',
  `min_employees` int(11) DEFAULT 1 COMMENT 'الحد الأدنى للموظفين في هذه الوردية',
  `status` enum('scheduled','active','completed','cancelled') DEFAULT 'scheduled',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`shift_id`),
  KEY `template_id` (`template_id`),
  KEY `idx_client_date` (`client_id`,`shift_date`),
  KEY `idx_status` (`status`),
  CONSTRAINT `shifts_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `shifts_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `shift_templates` (`template_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shifts`
--

LOCK TABLES `shifts` WRITE;
/*!40000 ALTER TABLE `shifts` DISABLE KEYS */;
INSERT INTO `shifts` VALUES (1,1,NULL,'صباحية','2025-07-01','06:00:00','18:00:00',0,0,1,1,'scheduled','',1,'2025-07-11 11:14:21','2025-07-11 11:14:21'),(2,1,NULL,'صباحية','2025-07-11','06:00:00','18:00:00',0,0,1,1,'scheduled','',1,'2025-07-11 11:15:19','2025-07-11 11:15:19');
/*!40000 ALTER TABLE `shifts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscription_plans`
--

DROP TABLE IF EXISTS `subscription_plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscription_plans` (
  `plan_id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(100) NOT NULL,
  `plan_name_ar` varchar(100) NOT NULL,
  `plan_description` text DEFAULT NULL,
  `plan_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `plan_duration_days` int(11) NOT NULL DEFAULT 30,
  `max_devices` int(11) DEFAULT 5,
  `max_employees` int(11) DEFAULT 3,
  `max_customers` int(11) DEFAULT 100,
  `max_products` int(11) DEFAULT 50,
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `plan_name` (`plan_name`),
  KEY `idx_plan_name` (`plan_name`),
  KEY `idx_plan_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='خطط الاشتراك';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscription_plans`
--

LOCK TABLES `subscription_plans` WRITE;
/*!40000 ALTER TABLE `subscription_plans` DISABLE KEYS */;
INSERT INTO `subscription_plans` VALUES (1,'basic','الخطة الأساسية','خطة مناسبة للمحلات الصغيرة',99.00,30,5,3,100,50,NULL,1,'2025-06-20 08:34:30','2025-06-20 08:34:30'),(2,'premium','الخطة المتقدمة','خطة مناسبة للمحلات المتوسطة',199.00,30,15,10,500,200,NULL,1,'2025-06-20 08:34:30','2025-06-20 08:34:30'),(3,'enterprise','خطة المؤسسات','خطة مناسبة للمحلات الكبيرة',399.00,30,-1,-1,-1,-1,NULL,1,'2025-06-20 08:34:30','2025-06-20 08:34:30');
/*!40000 ALTER TABLE `subscription_plans` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `suspicious_activities`
--

DROP TABLE IF EXISTS `suspicious_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `suspicious_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `activity_type` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `severity` enum('low','medium','high','critical') DEFAULT 'medium',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `blocked` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `idx_ip_activity` (`ip_address`,`activity_type`),
  KEY `idx_severity_time` (`severity`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `suspicious_activities`
--

LOCK TABLES `suspicious_activities` WRITE;
/*!40000 ALTER TABLE `suspicious_activities` DISABLE KEYS */;
/*!40000 ALTER TABLE `suspicious_activities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_cafeteria_items`
--

DROP TABLE IF EXISTS `trial_cafeteria_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_cafeteria_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `category` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_cafeteria_items_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_cafeteria_items`
--

LOCK TABLES `trial_cafeteria_items` WRITE;
/*!40000 ALTER TABLE `trial_cafeteria_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_cafeteria_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_clients`
--

DROP TABLE IF EXISTS `trial_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_clients` (
  `trial_id` int(11) NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `owner_name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `trial_start` timestamp NOT NULL DEFAULT current_timestamp(),
  `trial_end` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`trial_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_clients`
--

LOCK TABLES `trial_clients` WRITE;
/*!40000 ALTER TABLE `trial_clients` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_customers`
--

DROP TABLE IF EXISTS `trial_customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_customers` (
  `customer_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`customer_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_customers_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_customers`
--

LOCK TABLES `trial_customers` WRITE;
/*!40000 ALTER TABLE `trial_customers` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_devices`
--

DROP TABLE IF EXISTS `trial_devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_devices` (
  `device_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_name` varchar(100) NOT NULL,
  `device_type` enum('PS4','PS5','Xbox','PC') NOT NULL,
  `hourly_rate` decimal(8,2) NOT NULL DEFAULT 15.00,
  `status` enum('available','occupied','maintenance') DEFAULT 'available',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`device_id`),
  KEY `trial_id` (`trial_id`),
  CONSTRAINT `trial_devices_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_devices`
--

LOCK TABLES `trial_devices` WRITE;
/*!40000 ALTER TABLE `trial_devices` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `trial_sessions`
--

DROP TABLE IF EXISTS `trial_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `trial_sessions` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `trial_id` int(11) NOT NULL,
  `device_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` datetime DEFAULT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `total_cost` decimal(10,2) DEFAULT 0.00,
  `status` enum('active','completed','cancelled') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`session_id`),
  KEY `trial_id` (`trial_id`),
  KEY `device_id` (`device_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `trial_sessions_ibfk_1` FOREIGN KEY (`trial_id`) REFERENCES `trial_clients` (`trial_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `trial_devices` (`device_id`) ON DELETE CASCADE,
  CONSTRAINT `trial_sessions_ibfk_3` FOREIGN KEY (`customer_id`) REFERENCES `trial_customers` (`customer_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trial_sessions`
--

LOCK TABLES `trial_sessions` WRITE;
/*!40000 ALTER TABLE `trial_sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `trial_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `admin_page_permissions_detailed`
--

/*!50001 DROP VIEW IF EXISTS `admin_page_permissions_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `admin_page_permissions_detailed` AS select `a`.`admin_id` AS `admin_id`,`a`.`username` AS `username`,`a`.`full_name` AS `full_name`,`a`.`role` AS `role`,`a`.`is_active` AS `admin_active`,`ap`.`page_id` AS `page_id`,`ap`.`page_name` AS `page_name`,`ap`.`page_label` AS `page_label`,`ap`.`page_url` AS `page_url`,`ap`.`page_icon` AS `page_icon`,`ap`.`category` AS `category`,`ap`.`description` AS `description`,`ap`.`is_default` AS `is_default`,`ap`.`required_role` AS `required_role`,case when `a`.`role` = 'super_admin' then 1 when `ap`.`required_role` = 'super_admin' and `a`.`role` <> 'super_admin' then 0 when `ap`.`required_role` = 'admin' and `a`.`role` not in ('super_admin','admin') then 0 else coalesce(`app`.`is_enabled`,`ap`.`is_default`) end AS `has_permission`,`app`.`granted_at` AS `granted_at`,`app`.`updated_at` AS `updated_at` from ((`admins` `a` join `admin_pages` `ap`) left join `admin_page_permissions` `app` on(`a`.`admin_id` = `app`.`admin_id` and `ap`.`page_id` = `app`.`page_id`)) where `ap`.`is_active` = 1 and `a`.`is_active` = 1 order by `a`.`admin_id`,`ap`.`category`,`ap`.`page_label` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `customers_stats`
--

/*!50001 DROP VIEW IF EXISTS `customers_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `customers_stats` AS select `c`.`customer_id` AS `customer_id`,`c`.`client_id` AS `client_id`,`cl`.`business_name` AS `business_name`,`c`.`name` AS `name`,`c`.`phone` AS `phone`,`c`.`email` AS `email`,count(`s`.`session_id`) AS `total_sessions`,coalesce(sum(`s`.`total_cost`),0) AS `total_spent`,coalesce(avg(`s`.`total_cost`),0) AS `avg_session_cost`,max(`s`.`start_time`) AS `last_visit`,min(`s`.`start_time`) AS `first_visit`,`c`.`created_at` AS `registration_date` from ((`customers` `c` left join `clients` `cl` on(`c`.`client_id` = `cl`.`client_id`)) left join `sessions` `s` on(`c`.`customer_id` = `s`.`customer_id`)) group by `c`.`customer_id`,`c`.`client_id`,`cl`.`business_name`,`c`.`name`,`c`.`phone`,`c`.`email`,`c`.`created_at` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `devices_stats`
--

/*!50001 DROP VIEW IF EXISTS `devices_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `devices_stats` AS select `d`.`device_id` AS `device_id`,`d`.`client_id` AS `client_id`,`cl`.`business_name` AS `business_name`,`d`.`device_name` AS `device_name`,`d`.`device_type` AS `device_type`,`d`.`status` AS `status`,`d`.`hourly_rate` AS `hourly_rate`,count(`s`.`session_id`) AS `total_sessions`,coalesce(sum(`s`.`duration_minutes`),0) AS `total_minutes`,coalesce(sum(`s`.`total_cost`),0) AS `total_revenue`,coalesce(avg(`s`.`duration_minutes`),0) AS `avg_session_duration`,max(`s`.`start_time`) AS `last_used` from ((`devices` `d` left join `clients` `cl` on(`d`.`client_id` = `cl`.`client_id`)) left join `sessions` `s` on(`d`.`device_id` = `s`.`device_id`)) group by `d`.`device_id`,`d`.`client_id`,`cl`.`business_name`,`d`.`device_name`,`d`.`device_type`,`d`.`status`,`d`.`hourly_rate` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `inventory_detailed`
--

/*!50001 DROP VIEW IF EXISTS `inventory_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `inventory_detailed` AS select `ci`.`id` AS `id`,`ci`.`client_id` AS `client_id`,`cl`.`business_name` AS `business_name`,`ci`.`name` AS `name`,`ci`.`description` AS `description`,`ci`.`price` AS `price`,`ci`.`cost_price` AS `cost_price`,`ci`.`price` - `ci`.`cost_price` AS `profit_per_unit`,case when `ci`.`cost_price` > 0 then round((`ci`.`price` - `ci`.`cost_price`) / `ci`.`cost_price` * 100,2) else 0 end AS `profit_percentage`,`ci`.`stock_quantity` AS `stock_quantity`,`ci`.`min_stock_level` AS `min_stock_level`,`ci`.`max_stock_level` AS `max_stock_level`,case when `ci`.`stock_quantity` <= 0 then 'نفد المخزون' when `ci`.`stock_quantity` <= `ci`.`min_stock_level` then 'مخزون منخفض' when `ci`.`stock_quantity` >= `ci`.`max_stock_level` then 'مخزون مرتفع' else 'مخزون طبيعي' end AS `stock_status`,`ci`.`status` AS `status`,`cat`.`name` AS `category_name`,`ci`.`barcode` AS `barcode`,`ci`.`supplier` AS `supplier`,`ci`.`last_restock_date` AS `last_restock_date`,`ci`.`created_at` AS `created_at`,`ci`.`updated_at` AS `updated_at` from ((`cafeteria_items` `ci` left join `clients` `cl` on(`ci`.`client_id` = `cl`.`client_id`)) left join `categories` `cat` on(`ci`.`category_id` = `cat`.`category_id`)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `monthly_financial_summary`
--

/*!50001 DROP VIEW IF EXISTS `monthly_financial_summary`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `monthly_financial_summary` AS select `income_data`.`client_id` AS `client_id`,year(`income_data`.`income_date`) AS `year`,month(`income_data`.`income_date`) AS `month`,'income' AS `type`,sum(`income_data`.`amount`) AS `total_amount` from (select `sessions`.`client_id` AS `client_id`,`sessions`.`created_at` AS `income_date`,`sessions`.`total_cost` AS `amount` from `sessions` where `sessions`.`payment_status` = 'paid' union all select `additional_income`.`client_id` AS `client_id`,`additional_income`.`income_date` AS `income_date`,`additional_income`.`amount` AS `amount` from `additional_income`) `income_data` group by `income_data`.`client_id`,year(`income_data`.`income_date`),month(`income_data`.`income_date`) union all select `expenses`.`client_id` AS `client_id`,year(`expenses`.`expense_date`) AS `year`,month(`expenses`.`expense_date`) AS `month`,'expense' AS `type`,sum(`expenses`.`amount`) AS `total_amount` from `expenses` group by `expenses`.`client_id`,year(`expenses`.`expense_date`),month(`expenses`.`expense_date`) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `sessions_detailed`
--

/*!50001 DROP VIEW IF EXISTS `sessions_detailed`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `sessions_detailed` AS select `s`.`session_id` AS `session_id`,`s`.`client_id` AS `client_id`,`cl`.`business_name` AS `business_name`,`s`.`device_id` AS `device_id`,`d`.`device_name` AS `device_name`,`d`.`device_type` AS `device_type`,`s`.`customer_id` AS `customer_id`,`c`.`name` AS `customer_name`,`c`.`phone` AS `customer_phone`,`s`.`session_type` AS `session_type`,`s`.`game_type` AS `game_type`,`s`.`players_count` AS `players_count`,`s`.`start_time` AS `start_time`,`s`.`end_time` AS `end_time`,`s`.`duration_minutes` AS `duration_minutes`,`s`.`hourly_rate` AS `hourly_rate`,`s`.`time_cost` AS `time_cost`,`s`.`products_cost` AS `products_cost`,`s`.`total_cost` AS `total_cost`,`s`.`payment_status` AS `payment_status`,`s`.`notes` AS `notes`,`s`.`created_at` AS `created_at` from (((`sessions` `s` left join `clients` `cl` on(`s`.`client_id` = `cl`.`client_id`)) left join `devices` `d` on(`s`.`device_id` = `d`.`device_id`)) left join `customers` `c` on(`s`.`customer_id` = `c`.`customer_id`)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 10:20:05

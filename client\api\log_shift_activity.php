<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../includes/employee-auth.php';

header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل دخول الموظف
if (!isset($_SESSION['employee_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من وجود شيفت نشط
$employee_id = $_SESSION['employee_id'];
$client_id = $_SESSION['client_id'];

try {
    $stmt = $pdo->prepare("
        SELECT shift_id 
        FROM employee_shifts 
        WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
        ORDER BY shift_start_time DESC 
        LIMIT 1
    ");
    $stmt->execute([$employee_id]);
    $active_shift = $stmt->fetch();
    
    if (!$active_shift) {
        echo json_encode(['success' => false, 'message' => 'لا يوجد شيفت نشط']);
        exit;
    }
    
    $shift_id = $active_shift['shift_id'];
    
    // الحصول على بيانات النشاط
    $activity_type = $_POST['activity_type'] ?? 'other';
    $activity_title = $_POST['activity_title'] ?? '';
    $activity_description = $_POST['activity_description'] ?? '';
    $activity_data = $_POST['activity_data'] ?? '{}';
    
    // التحقق من صحة البيانات
    if (empty($activity_title)) {
        echo json_encode(['success' => false, 'message' => 'عنوان النشاط مطلوب']);
        exit;
    }
    
    // تحويل البيانات الإضافية إلى JSON
    $additional_data = json_decode($activity_data, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $additional_data = [];
    }
    
    // إضافة معلومات إضافية
    $final_data = array_merge($additional_data, [
        'source' => 'javascript',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'request_time' => date('Y-m-d H:i:s'),
        'session_id' => session_id()
    ]);
    
    // تسجيل النشاط
    $stmt = $pdo->prepare("
        INSERT INTO employee_shift_activities (
            shift_id, employee_id, client_id, activity_type,
            activity_title, activity_description, activity_data,
            ip_address, user_agent, page_url, http_method
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $shift_id,
        $employee_id,
        $client_id,
        $activity_type,
        $activity_title,
        $activity_description,
        json_encode($final_data, JSON_UNESCAPED_UNICODE),
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $additional_data['page_url'] ?? $_SERVER['HTTP_REFERER'] ?? '',
        'POST'
    ]);
    
    if ($result) {
        // تحديث آخر نشاط للموظف
        $stmt = $pdo->prepare("
            UPDATE employees 
            SET last_activity_time = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$employee_id]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تسجيل النشاط بنجاح',
            'activity_id' => $pdo->lastInsertId()
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في تسجيل النشاط']);
    }
    
} catch (PDOException $e) {
    error_log("خطأ في تسجيل نشاط الشيفت: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}
?>

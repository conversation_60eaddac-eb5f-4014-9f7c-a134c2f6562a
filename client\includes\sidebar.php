<?php
require_once __DIR__ . '/employee-auth.php';

// دالة مساعدة للتحقق من إمكانية الوصول للصفحة
function canAccessPage($page_name, $permission = null) {
    // إذا كان المستخدم عميل (مالك)، يمكنه الوصول لجميع الصفحات
    if (isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
        return true;
    }

    // إذا كان موظف، تحقق من الصلاحيات والصفحات المخصصة
    if (isset($_SESSION['employee_id'])) {
        // تحقق من الصفحات المخصصة أولاً
        if (employeeCanAccessPage($page_name)) {
            // إذا كانت هناك صلاحية مطلوبة، تحقق منها أيضاً
            if ($permission) {
                return employeeHasPermission($permission);
            }
            return true;
        }

        // إذا لم تكن الصفحة في القائمة المخصصة، تحقق من الصلاحية فقط
        if ($permission) {
            return employeeHasPermission($permission);
        }
    }

    return false;
}
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3" style="margin-top: 56px;">
        <ul class="nav flex-column">
            <!-- Dashboard - Available to all -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
            </li>

            <?php if (canAccessPage('devices', 'manage_devices')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'devices' ? 'active' : ''; ?>" href="devices.php">
                    <i class="fas fa-gamepad"></i> الأجهزة
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('sessions', 'manage_sessions')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'sessions' ? 'active' : ''; ?>" href="sessions.php">
                    <i class="fas fa-play-circle"></i> الجلسات
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('rooms', 'manage_rooms')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'rooms' ? 'active' : ''; ?>" href="rooms.php">
                    <i class="fas fa-home"></i> الغرف
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('customers', 'manage_customers')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'customers' ? 'active' : ''; ?>" href="customers.php">
                    <i class="fas fa-users"></i> العملاء
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('cafeteria', 'manage_cafeteria')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'cafeteria' ? 'active' : ''; ?>" href="cafeteria.php">
                    <i class="fas fa-coffee"></i> الكافتيريا
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('inventory', 'manage_cafeteria')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'inventory' ? 'active' : ''; ?>" href="inventory.php">
                    <i class="fas fa-warehouse"></i> إدارة المخزن
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('orders', 'manage_orders')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'orders' ? 'active' : ''; ?>" href="orders.php">
                    <i class="fas fa-shopping-cart"></i> الأوردرات
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('reservations', 'manage_reservations')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'reservations' ? 'active' : ''; ?>" href="reservations.php">
                    <i class="fas fa-calendar-alt"></i> الحجوزات
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('finances', 'view_finances')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'finances' ? 'active' : ''; ?>" href="finances.php">
                    <i class="fas fa-chart-line"></i> الماليات
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('reports', 'view_reports')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'reports' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-chart-bar"></i> التقارير
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('employees', 'manage_employees')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'employees' ? 'active' : ''; ?>" href="employees.php">
                    <i class="fas fa-user-tie"></i> الموظفين
                </a>
            </li>
            <?php endif; ?>

            <!-- قسم إدارة الشيفت الجديد -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>إدارة الشيفت</span>
                </h6>
            </li>

            <!-- بدء/إنهاء الشيفت للموظفين -->
            <?php if (isset($_SESSION['employee_id'])): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_start' ? 'active' : ''; ?>" href="employee_shift_start.php">
                    <i class="fas fa-play-circle text-success"></i> إدارة الشيفت
                    <?php
                    // عرض حالة الشيفت النشط
                    if (isset($pdo)) {
                        $stmt = $pdo->prepare("SELECT shift_status FROM employees WHERE id = ?");
                        $stmt->execute([$_SESSION['employee_id']]);
                        $employee = $stmt->fetch();
                        if ($employee && $employee['shift_status'] == 'on_duty') {
                            echo '<span class="badge bg-success ms-2">نشط</span>';
                        }
                    }
                    ?>
                </a>
            </li>
            <?php endif; ?>

            <!-- تقارير الشيفت للإدارة -->
            <?php if (canAccessPage('employee_shift_reports', 'view_reports') || canAccessPage('employee_shift_reports', 'manage_employees')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_reports' ? 'active' : ''; ?>" href="employee_shift_reports.php">
                    <i class="fas fa-chart-line"></i> تقارير الشيفت
                </a>
            </li>
            <?php endif; ?>

            <!-- قسم الورديات القديم -->
            <?php if (canAccessPage('shifts', 'manage_shifts') || canAccessPage('shifts', 'view_shifts')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shifts' ? 'active' : ''; ?>" href="shifts.php">
                    <i class="fas fa-clock"></i> الورديات (قديم)
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('attendance', 'manage_attendance') || canAccessPage('attendance', 'view_attendance')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'attendance' ? 'active' : ''; ?>" href="attendance.php">
                    <i class="fas fa-user-check"></i> الحضور والانصراف
                </a>
            </li>
            <?php endif; ?>

            <!-- تسجيل الحضور السريع للموظفين فقط -->
            <?php if (isset($_SESSION['employee_id'])): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'quick_attendance' ? 'active' : ''; ?>" href="quick_attendance.php">
                    <i class="fas fa-clock"></i> تسجيل حضور سريع
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('shift_reports', 'view_shift_reports')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_reports' ? 'active' : ''; ?>" href="shift_reports.php">
                    <i class="fas fa-chart-line"></i> تقارير الورديات (قديم)
                </a>
            </li>
            <?php endif; ?>

            <!-- أحداث الشيفت -->
            <?php if (canAccessPage('shift_events', 'manage_shift_events') || canAccessPage('shift_events', 'view_shift_events')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_events' ? 'active' : ''; ?>" href="shift_events.php">
                    <i class="fas fa-list-alt"></i> أحداث الشيفت
                </a>
            </li>
            <?php endif; ?>

            <!-- ملخصات الشيفت -->
            <?php if (canAccessPage('shift_summaries', 'view_shift_summaries') || canAccessPage('shift_summaries', 'generate_shift_reports')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_summaries' ? 'active' : ''; ?>" href="shift_summaries.php">
                    <i class="fas fa-chart-pie"></i> ملخصات الشيفت
                </a>
            </li>
            <?php endif; ?>

            <!-- الإشعارات -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($active_page ?? '') === 'notifications' ? 'active' : ''; ?>" href="notifications.php" id="notifications-link">
                    <i class="fas fa-bell"></i> الإشعارات
                    <span class="badge bg-danger notification-badge" id="notification-count" style="display: none;"></span>
                </a>
            </li>

            <!-- إعدادات المستخدم -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>الإعدادات</span>
                </h6>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo $active_page === 'profile' ? 'active' : ''; ?>" href="profile.php">
                    <i class="fas fa-user"></i> الملف الشخصي
                </a>
            </li>

            <?php if (!isset($_SESSION['employee_id'])): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo $active_page === 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i> إعدادات المحل
                </a>
            </li>
            <?php endif; ?>

            <li class="nav-item">
                <a class="nav-link text-danger" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </li>
        </ul>
    </div>
</nav>

<?php if (employeeHasPermission('manage_sessions')): ?>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newSessionModal">
        <i class="fas fa-plus"></i> جلسة جديدة
    </button>
<?php endif; ?>

<?php if (employeeHasPermission('view_reports')): ?>
    <div class="card mt-3">
        <div class="card-header">
            إحصائيات الجلسات
        </div>
        <div class="card-body">
            <!-- محتوى الإحصائيات -->
        </div>
    </div>
<?php endif; ?>
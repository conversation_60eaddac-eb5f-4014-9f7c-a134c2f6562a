-- نظام إدارة الشيفت للموظفين مع تسجيل العمليات
-- تاريخ الإنشاء: 2025-08-01

-- 1. جدول الشيفت النشط للموظفين
CREATE TABLE IF NOT EXISTS employee_shifts (
    shift_id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    client_id INT NOT NULL,
    shift_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    shift_end_time TIMESTAMP NULL,
    planned_start_time TIME NULL COMMENT 'الوقت المخطط لبدء الشيفت',
    planned_end_time TIME NULL COMMENT 'الوقت المخطط لانتهاء الشيفت',
    actual_duration_minutes INT DEFAULT 0,
    break_start_time TIMESTAMP NULL,
    break_end_time TIMESTAMP NULL,
    break_duration_minutes INT DEFAULT 0,
    shift_status ENUM('active', 'completed', 'cancelled', 'on_break') DEFAULT 'active',
    shift_notes TEXT NULL,
    location_info VARCHAR(255) NULL COMMENT 'معلومات الموقع أو المحطة',
    ip_address VARCHAR(45) NULL,
    device_info TEXT NULL COMMENT 'معلومات الجهاز المستخدم',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    
    INDEX idx_employee_shift (employee_id, shift_status),
    INDEX idx_client_shift (client_id, shift_start_time),
    INDEX idx_shift_status (shift_status),
    INDEX idx_shift_date (DATE(shift_start_time))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. جدول تسجيل العمليات والأنشطة
CREATE TABLE IF NOT EXISTS employee_shift_activities (
    activity_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL,
    employee_id INT NOT NULL,
    client_id INT NOT NULL,
    activity_type ENUM(
        'login', 'logout', 'session_start', 'session_end', 'session_update',
        'customer_add', 'customer_edit', 'customer_delete',
        'device_update', 'device_maintenance',
        'cafeteria_order', 'cafeteria_payment',
        'inventory_update', 'inventory_check',
        'financial_transaction', 'report_view',
        'settings_change', 'employee_action',
        'break_start', 'break_end',
        'system_access', 'page_visit',
        'other'
    ) NOT NULL,
    activity_title VARCHAR(255) NOT NULL,
    activity_description TEXT NULL,
    target_type VARCHAR(50) NULL COMMENT 'نوع الهدف (session, customer, device, etc.)',
    target_id INT NULL COMMENT 'معرف الهدف',
    old_values JSON NULL COMMENT 'القيم القديمة قبل التغيير',
    new_values JSON NULL COMMENT 'القيم الجديدة بعد التغيير',
    activity_data JSON NULL COMMENT 'بيانات إضافية للنشاط',
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    page_url VARCHAR(500) NULL,
    http_method VARCHAR(10) NULL,
    response_status INT NULL,
    execution_time_ms INT NULL COMMENT 'وقت تنفيذ العملية بالميلي ثانية',
    activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    
    INDEX idx_shift_activities (shift_id, activity_timestamp),
    INDEX idx_employee_activities (employee_id, activity_timestamp),
    INDEX idx_activity_type (activity_type),
    INDEX idx_activity_date (DATE(activity_timestamp)),
    INDEX idx_target (target_type, target_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. جدول ملخص الشيفت (يتم إنشاؤه عند انتهاء الشيفت)
CREATE TABLE IF NOT EXISTS employee_shift_summaries (
    summary_id INT AUTO_INCREMENT PRIMARY KEY,
    shift_id INT NOT NULL UNIQUE,
    employee_id INT NOT NULL,
    client_id INT NOT NULL,
    shift_date DATE NOT NULL,
    total_duration_minutes INT NOT NULL,
    break_duration_minutes INT DEFAULT 0,
    productive_time_minutes INT NOT NULL,
    total_activities INT DEFAULT 0,
    sessions_handled INT DEFAULT 0,
    customers_served INT DEFAULT 0,
    revenue_generated DECIMAL(10,2) DEFAULT 0.00,
    activities_breakdown JSON NULL COMMENT 'تفصيل الأنشطة حسب النوع',
    performance_metrics JSON NULL COMMENT 'مقاييس الأداء',
    shift_rating ENUM('excellent', 'good', 'average', 'poor') NULL,
    manager_notes TEXT NULL,
    auto_generated_summary TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    
    INDEX idx_employee_summaries (employee_id, shift_date),
    INDEX idx_client_summaries (client_id, shift_date),
    INDEX idx_shift_date (shift_date),
    INDEX idx_shift_rating (shift_rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. جدول إعدادات نظام الشيفت
CREATE TABLE IF NOT EXISTS shift_system_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NOT NULL,
    setting_description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_client_setting (client_id, setting_key),
    INDEX idx_client_settings (client_id, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) 
SELECT 
    client_id,
    'auto_track_activities',
    'true',
    'تفعيل تتبع الأنشطة التلقائي'
FROM clients WHERE is_active = 1;

INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) 
SELECT 
    client_id,
    'require_shift_notes',
    'false',
    'إجبار كتابة ملاحظات عند انتهاء الشيفت'
FROM clients WHERE is_active = 1;

INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) 
SELECT 
    client_id,
    'max_shift_duration_hours',
    '12',
    'الحد الأقصى لمدة الشيفت بالساعات'
FROM clients WHERE is_active = 1;

INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) 
SELECT 
    client_id,
    'break_reminder_minutes',
    '240',
    'تذكير بالاستراحة كل X دقيقة'
FROM clients WHERE is_active = 1;

-- 5. إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_employee_shifts_active ON employee_shifts(employee_id, shift_status, shift_start_time);
CREATE INDEX IF NOT EXISTS idx_shift_activities_recent ON employee_shift_activities(shift_id, activity_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_activities_by_type_date ON employee_shift_activities(activity_type, DATE(activity_timestamp));

-- 6. إضافة عمود لتتبع الشيفت النشط في جدول الموظفين
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS current_shift_id INT NULL,
ADD COLUMN IF NOT EXISTS shift_status ENUM('off_duty', 'on_duty', 'on_break') DEFAULT 'off_duty',
ADD COLUMN IF NOT EXISTS last_activity_time TIMESTAMP NULL;

-- إضافة فهرس للشيفت النشط
CREATE INDEX IF NOT EXISTS idx_employee_shift_status ON employees(shift_status, current_shift_id);

-- 7. إنشاء view لعرض الشيفت النشط مع تفاصيل الموظف
CREATE OR REPLACE VIEW active_employee_shifts AS
SELECT 
    es.shift_id,
    es.employee_id,
    e.name as employee_name,
    e.role as employee_role,
    es.client_id,
    c.business_name,
    es.shift_start_time,
    es.planned_end_time,
    es.shift_status,
    es.location_info,
    TIMESTAMPDIFF(MINUTE, es.shift_start_time, NOW()) as current_duration_minutes,
    (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities,
    (SELECT activity_timestamp FROM employee_shift_activities WHERE shift_id = es.shift_id ORDER BY activity_timestamp DESC LIMIT 1) as last_activity_time
FROM employee_shifts es
JOIN employees e ON es.employee_id = e.id
JOIN clients c ON es.client_id = c.client_id
WHERE es.shift_status IN ('active', 'on_break');

-- إنشاء جدول لتتبع أخطاء النظام
CREATE TABLE IF NOT EXISTS shift_system_errors (
    error_id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NULL,
    shift_id INT NULL,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    error_data JSON NULL,
    stack_trace TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_error_type (error_type),
    INDEX idx_error_date (created_at),
    INDEX idx_employee_errors (employee_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء view لعرض تقارير الشيفت اليومية
CREATE OR REPLACE VIEW daily_shift_report AS
SELECT 
    DATE(es.shift_start_time) as shift_date,
    es.client_id,
    c.business_name,
    COUNT(DISTINCT es.employee_id) as total_employees,
    COUNT(es.shift_id) as total_shifts,
    SUM(es.actual_duration_minutes) as total_minutes_worked,
    AVG(es.actual_duration_minutes) as avg_shift_duration,
    SUM(CASE WHEN es.shift_status = 'completed' THEN 1 ELSE 0 END) as completed_shifts,
    SUM(CASE WHEN es.shift_status = 'active' THEN 1 ELSE 0 END) as active_shifts,
    (SELECT COUNT(*) FROM employee_shift_activities esa 
     WHERE DATE(esa.activity_timestamp) = DATE(es.shift_start_time) 
     AND esa.client_id = es.client_id) as total_activities
FROM employee_shifts es
JOIN clients c ON es.client_id = c.client_id
GROUP BY DATE(es.shift_start_time), es.client_id, c.business_name
ORDER BY shift_date DESC, c.business_name;

-- إنشاء view لعرض أداء الموظفين
CREATE OR REPLACE VIEW employee_performance_summary AS
SELECT 
    e.id as employee_id,
    e.name as employee_name,
    e.role as employee_role,
    e.client_id,
    c.business_name,
    COUNT(es.shift_id) as total_shifts,
    SUM(es.actual_duration_minutes) as total_minutes_worked,
    AVG(es.actual_duration_minutes) as avg_shift_duration,
    (SELECT COUNT(*) FROM employee_shift_activities esa WHERE esa.employee_id = e.id) as total_activities,
    (SELECT COUNT(*) FROM employee_shift_activities esa WHERE esa.employee_id = e.id AND esa.activity_type = 'session_start') as sessions_handled,
    (SELECT MAX(activity_timestamp) FROM employee_shift_activities esa WHERE esa.employee_id = e.id) as last_activity,
    (SELECT shift_start_time FROM employee_shifts WHERE employee_id = e.id ORDER BY shift_start_time DESC LIMIT 1) as last_shift
FROM employees e
JOIN clients c ON e.client_id = c.client_id
LEFT JOIN employee_shifts es ON e.id = es.employee_id
WHERE e.is_active = 1
GROUP BY e.id, e.name, e.role, e.client_id, c.business_name
ORDER BY total_shifts DESC, total_activities DESC;

-- إنشاء view للإحصائيات السريعة
CREATE OR REPLACE VIEW shift_quick_stats AS
SELECT 
    'active_shifts' as stat_name,
    COUNT(*) as stat_value,
    'عدد الشيفت النشط حالياً' as stat_description
FROM employee_shifts 
WHERE shift_status IN ('active', 'on_break')

UNION ALL

SELECT 
    'total_activities_today' as stat_name,
    COUNT(*) as stat_value,
    'إجمالي الأنشطة اليوم' as stat_description
FROM employee_shift_activities 
WHERE DATE(activity_timestamp) = CURDATE()

UNION ALL

SELECT 
    'employees_on_duty' as stat_name,
    COUNT(*) as stat_value,
    'عدد الموظفين في الخدمة' as stat_description
FROM employees 
WHERE shift_status = 'on_duty'

UNION ALL

SELECT 
    'completed_shifts_today' as stat_name,
    COUNT(*) as stat_value,
    'الشيفت المكتملة اليوم' as stat_description
FROM employee_shifts 
WHERE DATE(shift_start_time) = CURDATE() 
AND shift_status = 'completed';

-- 8. إنشاء stored procedure لبدء الشيفت
DELIMITER //
CREATE OR REPLACE PROCEDURE StartEmployeeShift(
    IN p_employee_id INT,
    IN p_client_id INT,
    IN p_planned_start_time TIME,
    IN p_planned_end_time TIME,
    IN p_location_info VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    IN p_device_info TEXT,
    OUT p_shift_id INT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_active_shift_count INT DEFAULT 0;
    DECLARE v_employee_active BOOLEAN DEFAULT FALSE;

    -- التحقق من وجود شيفت نشط للموظف
    SELECT COUNT(*) INTO v_active_shift_count
    FROM employee_shifts
    WHERE employee_id = p_employee_id
    AND shift_status IN ('active', 'on_break');

    -- التحقق من أن الموظف مفعل
    SELECT is_active INTO v_employee_active
    FROM employees
    WHERE id = p_employee_id;

    IF v_active_shift_count > 0 THEN
        SET p_result = 'ERROR_ACTIVE_SHIFT_EXISTS';
        SET p_shift_id = NULL;
    ELSEIF NOT v_employee_active THEN
        SET p_result = 'ERROR_EMPLOYEE_INACTIVE';
        SET p_shift_id = NULL;
    ELSE
        -- إنشاء شيفت جديد
        INSERT INTO employee_shifts (
            employee_id, client_id, planned_start_time, planned_end_time,
            location_info, ip_address, device_info, shift_status
        ) VALUES (
            p_employee_id, p_client_id, p_planned_start_time, p_planned_end_time,
            p_location_info, p_ip_address, p_device_info, 'active'
        );

        SET p_shift_id = LAST_INSERT_ID();

        -- تحديث حالة الموظف
        UPDATE employees
        SET current_shift_id = p_shift_id,
            shift_status = 'on_duty',
            last_activity_time = NOW()
        WHERE id = p_employee_id;

        -- تسجيل نشاط بدء الشيفت
        INSERT INTO employee_shift_activities (
            shift_id, employee_id, client_id, activity_type,
            activity_title, activity_description, ip_address,
            activity_data
        ) VALUES (
            p_shift_id, p_employee_id, p_client_id, 'login',
            'بدء الشيفت', 'تم بدء شيفت جديد', p_ip_address,
            JSON_OBJECT(
                'planned_start', p_planned_start_time,
                'planned_end', p_planned_end_time,
                'location', p_location_info
            )
        );

        SET p_result = 'SUCCESS';
    END IF;
END //
DELIMITER ;

-- إنشاء stored procedure لإنهاء الشيفت
DELIMITER //
CREATE OR REPLACE PROCEDURE EndEmployeeShift(
    IN p_shift_id INT,
    IN p_employee_id INT,
    IN p_shift_notes TEXT,
    OUT p_result VARCHAR(100)
)
BEGIN
    DECLARE v_shift_exists BOOLEAN DEFAULT FALSE;
    DECLARE v_shift_active BOOLEAN DEFAULT FALSE;
    DECLARE v_start_time TIMESTAMP;
    DECLARE v_duration_minutes INT;
    DECLARE v_break_duration INT DEFAULT 0;
    DECLARE v_client_id INT;

    -- التحقق من وجود الشيفت وأنه نشط
    SELECT
        COUNT(*) > 0,
        shift_status = 'active' OR shift_status = 'on_break',
        shift_start_time,
        COALESCE(break_duration_minutes, 0),
        client_id
    INTO v_shift_exists, v_shift_active, v_start_time, v_break_duration, v_client_id
    FROM employee_shifts
    WHERE shift_id = p_shift_id AND employee_id = p_employee_id;

    IF NOT v_shift_exists THEN
        SET p_result = 'ERROR_SHIFT_NOT_FOUND';
    ELSEIF NOT v_shift_active THEN
        SET p_result = 'ERROR_SHIFT_NOT_ACTIVE';
    ELSE
        -- حساب مدة الشيفت
        SET v_duration_minutes = TIMESTAMPDIFF(MINUTE, v_start_time, NOW());

        -- إنهاء الشيفت
        UPDATE employee_shifts
        SET
            shift_end_time = NOW(),
            actual_duration_minutes = v_duration_minutes,
            shift_status = 'completed',
            shift_notes = p_shift_notes
        WHERE shift_id = p_shift_id;

        -- تحديث حالة الموظف
        UPDATE employees
        SET
            current_shift_id = NULL,
            shift_status = 'off_duty',
            last_activity_time = NOW()
        WHERE id = p_employee_id;

        -- تسجيل نشاط إنهاء الشيفت
        INSERT INTO employee_shift_activities (
            shift_id, employee_id, client_id, activity_type,
            activity_title, activity_description,
            activity_data
        ) VALUES (
            p_shift_id, p_employee_id, v_client_id, 'logout',
            'إنهاء الشيفت', CONCAT('تم إنهاء الشيفت بعد ', v_duration_minutes, ' دقيقة'),
            JSON_OBJECT(
                'duration_minutes', v_duration_minutes,
                'break_duration', v_break_duration,
                'notes', p_shift_notes
            )
        );

        -- إنشاء ملخص الشيفت
        CALL GenerateShiftSummary(p_shift_id);

        SET p_result = 'SUCCESS';
    END IF;
END //
DELIMITER ;

-- إنشاء stored procedure لتوليد ملخص الشيفت
DELIMITER //
CREATE OR REPLACE PROCEDURE GenerateShiftSummary(
    IN p_shift_id INT
)
BEGIN
    DECLARE v_employee_id INT;
    DECLARE v_client_id INT;
    DECLARE v_shift_date DATE;
    DECLARE v_duration_minutes INT;
    DECLARE v_break_duration INT;
    DECLARE v_total_activities INT;
    DECLARE v_sessions_handled INT;
    DECLARE v_customers_served INT;
    DECLARE v_revenue_generated DECIMAL(10,2);
    DECLARE v_activities_breakdown JSON;
    DECLARE v_performance_metrics JSON;
    DECLARE v_auto_summary TEXT;

    -- جلب بيانات الشيفت
    SELECT employee_id, client_id, DATE(shift_start_time),
           actual_duration_minutes, COALESCE(break_duration_minutes, 0)
    INTO v_employee_id, v_client_id, v_shift_date, v_duration_minutes, v_break_duration
    FROM employee_shifts
    WHERE shift_id = p_shift_id;

    -- حساب الإحصائيات
    SELECT COUNT(*) INTO v_total_activities
    FROM employee_shift_activities
    WHERE shift_id = p_shift_id;

    SELECT COUNT(*) INTO v_sessions_handled
    FROM employee_shift_activities
    WHERE shift_id = p_shift_id
    AND activity_type IN ('session_start', 'session_end');

    SELECT COUNT(DISTINCT target_id) INTO v_customers_served
    FROM employee_shift_activities
    WHERE shift_id = p_shift_id
    AND activity_type IN ('customer_add', 'customer_edit')
    AND target_type = 'customer';

    -- حساب الإيرادات المولدة (تقديري)
    SELECT COALESCE(SUM(s.total_cost), 0) INTO v_revenue_generated
    FROM sessions s
    JOIN employee_shift_activities esa ON s.session_id = esa.target_id
    WHERE esa.shift_id = p_shift_id
    AND esa.activity_type = 'session_start'
    AND s.payment_status = 'paid';

    -- تفصيل الأنشطة
    SELECT JSON_OBJECTAGG(activity_type, activity_count) INTO v_activities_breakdown
    FROM (
        SELECT activity_type, COUNT(*) as activity_count
        FROM employee_shift_activities
        WHERE shift_id = p_shift_id
        GROUP BY activity_type
    ) activities;

    -- مقاييس الأداء
    SET v_performance_metrics = JSON_OBJECT(
        'activities_per_hour', ROUND(v_total_activities / (v_duration_minutes / 60.0), 2),
        'sessions_per_hour', ROUND(v_sessions_handled / (v_duration_minutes / 60.0), 2),
        'revenue_per_hour', ROUND(v_revenue_generated / (v_duration_minutes / 60.0), 2),
        'break_percentage', ROUND((v_break_duration / v_duration_minutes) * 100, 2)
    );

    -- توليد ملخص تلقائي
    SET v_auto_summary = CONCAT(
        'تم إنجاز ', v_total_activities, ' نشاط خلال ',
        FLOOR(v_duration_minutes / 60), ' ساعة و ', (v_duration_minutes % 60), ' دقيقة. ',
        'تم التعامل مع ', v_sessions_handled, ' جلسة و ', v_customers_served, ' عميل. ',
        'الإيرادات المولدة: ', v_revenue_generated, ' ريال.'
    );

    -- إدراج الملخص
    INSERT INTO employee_shift_summaries (
        shift_id, employee_id, client_id, shift_date,
        total_duration_minutes, break_duration_minutes,
        productive_time_minutes, total_activities,
        sessions_handled, customers_served, revenue_generated,
        activities_breakdown, performance_metrics, auto_generated_summary
    ) VALUES (
        p_shift_id, v_employee_id, v_client_id, v_shift_date,
        v_duration_minutes, v_break_duration,
        v_duration_minutes - v_break_duration, v_total_activities,
        v_sessions_handled, v_customers_served, v_revenue_generated,
        v_activities_breakdown, v_performance_metrics, v_auto_summary
    );

END //
DELIMITER ;

-- إنشاء trigger لتسجيل الأنشطة التلقائي
DELIMITER //
CREATE OR REPLACE TRIGGER after_session_insert
AFTER INSERT ON sessions
FOR EACH ROW
BEGIN
    DECLARE v_shift_id INT;
    DECLARE v_employee_id INT;

    -- البحث عن الشيفت النشط للموظف الذي أنشأ الجلسة
    IF NEW.created_by IS NOT NULL THEN
        SELECT es.shift_id, es.employee_id
        INTO v_shift_id, v_employee_id
        FROM employee_shifts es
        JOIN employees e ON es.employee_id = e.id
        WHERE e.id = NEW.created_by
        AND es.shift_status IN ('active', 'on_break')
        LIMIT 1;

        -- تسجيل النشاط إذا وجد شيفت نشط
        IF v_shift_id IS NOT NULL THEN
            INSERT INTO employee_shift_activities (
                shift_id, employee_id, client_id, activity_type,
                activity_title, activity_description, target_type, target_id,
                activity_data
            ) VALUES (
                v_shift_id, v_employee_id, NEW.client_id, 'session_start',
                'بدء جلسة جديدة', CONCAT('تم بدء جلسة للعميل: ', COALESCE(NEW.customer_id, 'غير محدد')),
                'session', NEW.session_id,
                JSON_OBJECT(
                    'session_type', NEW.session_type,
                    'device_id', NEW.device_id,
                    'customer_id', NEW.customer_id,
                    'hourly_rate', NEW.hourly_rate
                )
            );
        END IF;
    END IF;
END //
DELIMITER ;

-- trigger لتسجيل تحديث الجلسات
DELIMITER //
CREATE OR REPLACE TRIGGER after_session_update
AFTER UPDATE ON sessions
FOR EACH ROW
BEGIN
    DECLARE v_shift_id INT;
    DECLARE v_employee_id INT;

    -- البحث عن الشيفت النشط للموظف
    IF NEW.created_by IS NOT NULL THEN
        SELECT es.shift_id, es.employee_id
        INTO v_shift_id, v_employee_id
        FROM employee_shifts es
        WHERE es.employee_id = NEW.created_by
        AND es.shift_status IN ('active', 'on_break')
        LIMIT 1;

        -- تسجيل النشاط إذا وجد شيفت نشط
        IF v_shift_id IS NOT NULL THEN
            -- تسجيل إنهاء الجلسة
            IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description, target_type, target_id,
                    old_values, new_values
                ) VALUES (
                    v_shift_id, v_employee_id, NEW.client_id, 'session_end',
                    'إنهاء جلسة', CONCAT('تم إنهاء الجلسة بتكلفة: ', NEW.total_cost),
                    'session', NEW.session_id,
                    JSON_OBJECT('status', OLD.status, 'total_cost', OLD.total_cost),
                    JSON_OBJECT('status', NEW.status, 'total_cost', NEW.total_cost)
                );
            -- تسجيل تحديث الجلسة
            ELSEIF OLD.total_cost != NEW.total_cost OR OLD.status != NEW.status THEN
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description, target_type, target_id,
                    old_values, new_values
                ) VALUES (
                    v_shift_id, v_employee_id, NEW.client_id, 'session_update',
                    'تحديث جلسة', 'تم تحديث بيانات الجلسة',
                    'session', NEW.session_id,
                    JSON_OBJECT('status', OLD.status, 'total_cost', OLD.total_cost),
                    JSON_OBJECT('status', NEW.status, 'total_cost', NEW.total_cost)
                );
            END IF;
        END IF;
    END IF;
END //
DELIMITER ;

-- trigger لتسجيل إضافة العملاء
DELIMITER //
CREATE OR REPLACE TRIGGER after_customer_insert
AFTER INSERT ON customers
FOR EACH ROW
BEGIN
    DECLARE v_shift_id INT;
    DECLARE v_employee_id INT;

    -- البحث عن الشيفت النشط لأي موظف في نفس العميل
    SELECT es.shift_id, es.employee_id
    INTO v_shift_id, v_employee_id
    FROM employee_shifts es
    WHERE es.client_id = NEW.client_id
    AND es.shift_status IN ('active', 'on_break')
    ORDER BY es.shift_start_time DESC
    LIMIT 1;

    -- تسجيل النشاط إذا وجد شيفت نشط
    IF v_shift_id IS NOT NULL THEN
        INSERT INTO employee_shift_activities (
            shift_id, employee_id, client_id, activity_type,
            activity_title, activity_description, target_type, target_id,
            activity_data
        ) VALUES (
            v_shift_id, v_employee_id, NEW.client_id, 'customer_add',
            'إضافة عميل جديد', CONCAT('تم إضافة العميل: ', NEW.name),
            'customer', NEW.customer_id,
            JSON_OBJECT(
                'name', NEW.name,
                'phone', NEW.phone,
                'email', NEW.email
            )
        );
    END IF;
END //
DELIMITER ;

-- إنشاء function لحساب إحصائيات الشيفت
DELIMITER //
CREATE OR REPLACE FUNCTION GetShiftStatistics(p_shift_id INT)
RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_result JSON;

    SELECT JSON_OBJECT(
        'total_activities', COUNT(*),
        'activity_types', JSON_OBJECTAGG(
            activity_type,
            COUNT(*)
        ),
        'duration_minutes', (
            SELECT actual_duration_minutes
            FROM employee_shifts
            WHERE shift_id = p_shift_id
        ),
        'sessions_handled', (
            SELECT COUNT(*)
            FROM employee_shift_activities
            WHERE shift_id = p_shift_id
            AND activity_type IN ('session_start', 'session_end')
        ),
        'last_activity', (
            SELECT activity_timestamp
            FROM employee_shift_activities
            WHERE shift_id = p_shift_id
            ORDER BY activity_timestamp DESC
            LIMIT 1
        )
    ) INTO v_result
    FROM employee_shift_activities
    WHERE shift_id = p_shift_id;

    RETURN v_result;
END //
DELIMITER ;

-- إنشاء function لتسجيل نشاط مخصص
DELIMITER //
CREATE OR REPLACE FUNCTION LogEmployeeActivity(
    p_employee_id INT,
    p_activity_type VARCHAR(50),
    p_activity_title VARCHAR(255),
    p_activity_description TEXT,
    p_target_type VARCHAR(50),
    p_target_id INT,
    p_activity_data JSON
)
RETURNS BOOLEAN
READS SQL DATA
MODIFIES SQL DATA
BEGIN
    DECLARE v_shift_id INT;
    DECLARE v_client_id INT;
    DECLARE v_result BOOLEAN DEFAULT FALSE;

    -- البحث عن الشيفت النشط للموظف
    SELECT es.shift_id, es.client_id
    INTO v_shift_id, v_client_id
    FROM employee_shifts es
    WHERE es.employee_id = p_employee_id
    AND es.shift_status IN ('active', 'on_break')
    LIMIT 1;

    -- تسجيل النشاط إذا وجد شيفت نشط
    IF v_shift_id IS NOT NULL THEN
        INSERT INTO employee_shift_activities (
            shift_id, employee_id, client_id, activity_type,
            activity_title, activity_description, target_type, target_id,
            activity_data, ip_address, user_agent
        ) VALUES (
            v_shift_id, p_employee_id, v_client_id, p_activity_type,
            p_activity_title, p_activity_description, p_target_type, p_target_id,
            p_activity_data,
            COALESCE(@user_ip, '127.0.0.1'),
            COALESCE(@user_agent, 'System')
        );

        -- تحديث آخر نشاط للموظف
        UPDATE employees
        SET last_activity_time = NOW()
        WHERE id = p_employee_id;

        SET v_result = TRUE;
    END IF;

    RETURN v_result;
END //
DELIMITER ;

-- إنشاء event لتنظيف البيانات القديمة (يعمل يومياً)
CREATE EVENT IF NOT EXISTS cleanup_old_shift_data
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- حذف الأنشطة الأقدم من 6 أشهر
    DELETE FROM employee_shift_activities
    WHERE activity_timestamp < DATE_SUB(NOW(), INTERVAL 6 MONTH);

    -- حذف الشيفت المكتمل الأقدم من سنة
    DELETE FROM employee_shifts
    WHERE shift_status = 'completed'
    AND shift_end_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

    -- حذف الأخطاء الأقدم من 3 أشهر
    DELETE FROM shift_system_errors
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);
END;

-- تفعيل event scheduler إذا لم يكن مفعلاً
SET GLOBAL event_scheduler = ON;

-- إنشاء جدول للإشعارات المتعلقة بالشيفت
CREATE TABLE IF NOT EXISTS shift_notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    employee_id INT NULL,
    shift_id INT NULL,
    notification_type ENUM('shift_start', 'shift_end', 'break_reminder', 'overtime_alert', 'system_alert') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,

    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE SET NULL,

    INDEX idx_client_notifications (client_id, is_read, created_at),
    INDEX idx_employee_notifications (employee_id, is_read),
    INDEX idx_notification_type (notification_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء stored procedure لإرسال إشعار
DELIMITER //
CREATE OR REPLACE PROCEDURE SendShiftNotification(
    IN p_client_id INT,
    IN p_employee_id INT,
    IN p_shift_id INT,
    IN p_type VARCHAR(50),
    IN p_title VARCHAR(255),
    IN p_message TEXT,
    IN p_priority VARCHAR(20)
)
BEGIN
    INSERT INTO shift_notifications (
        client_id, employee_id, shift_id, notification_type,
        title, message, priority
    ) VALUES (
        p_client_id, p_employee_id, p_shift_id, p_type,
        p_title, p_message, p_priority
    );
END //
DELIMITER ;

-- إنشاء trigger لإرسال إشعارات تلقائية
DELIMITER //
CREATE OR REPLACE TRIGGER after_shift_start
AFTER INSERT ON employee_shifts
FOR EACH ROW
BEGIN
    CALL SendShiftNotification(
        NEW.client_id,
        NEW.employee_id,
        NEW.shift_id,
        'shift_start',
        'بدء شيفت جديد',
        CONCAT('بدأ الموظف شيفت جديد في ', TIME(NEW.shift_start_time)),
        'medium'
    );
END //
DELIMITER ;

DELIMITER //
CREATE OR REPLACE TRIGGER after_shift_end
AFTER UPDATE ON employee_shifts
FOR EACH ROW
BEGIN
    IF OLD.shift_status != 'completed' AND NEW.shift_status = 'completed' THEN
        CALL SendShiftNotification(
            NEW.client_id,
            NEW.employee_id,
            NEW.shift_id,
            'shift_end',
            'انتهاء الشيفت',
            CONCAT('انتهى الشيفت بعد ', NEW.actual_duration_minutes, ' دقيقة'),
            'medium'
        );
    END IF;
END //
DELIMITER ;

-- إضافة فهارس نهائية لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_shift_activities_performance ON employee_shift_activities(employee_id, activity_type, activity_timestamp);
CREATE INDEX IF NOT EXISTS idx_shifts_date_range ON employee_shifts(client_id, DATE(shift_start_time), shift_status);
CREATE INDEX IF NOT EXISTS idx_activities_summary ON employee_shift_activities(shift_id, activity_type);

-- إنهاء السكريبت
SELECT 'تم إنشاء نظام إدارة الشيفت بنجاح!' as result;

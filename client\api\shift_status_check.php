<?php
/**
 * API للتحقق من حالة الشيفت والحصول على الإحصائيات السريعة
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';

header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل الدخول
if (!isset($_SESSION['employee_id']) && !isset($_SESSION['client_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$employee_id = $_SESSION['employee_id'] ?? null;

try {
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'is_employee' => $employee_id !== null
    ];
    
    // إحصائيات عامة
    $stats_stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM employee_shifts WHERE client_id = ? AND shift_status IN ('active', 'on_break')) as active_shifts,
            (SELECT COUNT(*) FROM employee_shift_activities esa 
             JOIN employee_shifts es ON esa.shift_id = es.shift_id 
             WHERE es.client_id = ? AND DATE(esa.activity_timestamp) = CURDATE()) as today_activities,
            (SELECT COUNT(*) FROM employees WHERE client_id = ? AND shift_status = 'on_duty') as employees_on_duty,
            (SELECT COUNT(*) FROM employee_shifts WHERE client_id = ? AND DATE(shift_start_time) = CURDATE() AND shift_status = 'completed') as completed_shifts_today
    ");
    $stats_stmt->execute([$client_id, $client_id, $client_id, $client_id]);
    $stats = $stats_stmt->fetch();
    
    $response['stats'] = $stats;
    
    // إذا كان موظف، جلب معلومات الشيفت النشط
    if ($employee_id) {
        $shift_stmt = $pdo->prepare("
            SELECT 
                shift_id,
                shift_start_time,
                shift_status,
                location_info,
                TIMESTAMPDIFF(MINUTE, shift_start_time, NOW()) as current_duration_minutes,
                (SELECT COUNT(*) FROM employee_shift_activities WHERE shift_id = es.shift_id) as total_activities
            FROM employee_shifts es
            WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
            ORDER BY shift_start_time DESC 
            LIMIT 1
        ");
        $shift_stmt->execute([$employee_id]);
        $active_shift = $shift_stmt->fetch();
        
        $response['employee'] = [
            'id' => $employee_id,
            'name' => $_SESSION['employee_name'] ?? '',
            'role' => $_SESSION['employee_role'] ?? '',
            'has_active_shift' => $active_shift !== false,
            'active_shift' => $active_shift ?: null
        ];
        
        // تحديث آخر نشاط
        $pdo->prepare("UPDATE employees SET last_activity_time = NOW() WHERE id = ?")->execute([$employee_id]);
    }
    
    // آخر الأنشطة (للإدارة)
    if (!$employee_id || ($_SESSION['employee_role'] ?? '') === 'admin') {
        $recent_activities_stmt = $pdo->prepare("
            SELECT 
                esa.activity_title,
                esa.activity_timestamp,
                e.name as employee_name,
                esa.activity_type
            FROM employee_shift_activities esa
            JOIN employee_shifts es ON esa.shift_id = es.shift_id
            JOIN employees e ON esa.employee_id = e.id
            WHERE es.client_id = ?
            ORDER BY esa.activity_timestamp DESC
            LIMIT 10
        ");
        $recent_activities_stmt->execute([$client_id]);
        $response['recent_activities'] = $recent_activities_stmt->fetchAll();
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ عام: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>

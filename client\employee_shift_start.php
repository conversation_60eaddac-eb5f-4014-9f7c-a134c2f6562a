<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل دخول الموظف
if (!isset($_SESSION['employee_id'])) {
    header('Location: employee-login.php');
    exit;
}

$employee_id = $_SESSION['employee_id'];
$client_id = $_SESSION['client_id'];
$employee_name = $_SESSION['employee_name'];
$employee_role = $_SESSION['employee_role'];

$page_title = "بدء الشيفت";
$active_page = "shift_start";

$message = '';
$error = '';

// التحقق من وجود شيفت نشط
$stmt = $pdo->prepare("
    SELECT shift_id, shift_start_time, shift_status, location_info
    FROM employee_shifts 
    WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
    ORDER BY shift_start_time DESC 
    LIMIT 1
");
$stmt->execute([$employee_id]);
$active_shift = $stmt->fetch();

// معالجة طلب بدء الشيفت
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_shift'])) {
    if ($active_shift) {
        $error = 'لديك شيفت نشط بالفعل. يجب إنهاء الشيفت الحالي أولاً.';
    } else {
        try {
            $planned_start = $_POST['planned_start_time'] ?? date('H:i');
            $planned_end = $_POST['planned_end_time'] ?? date('H:i', strtotime('+8 hours'));
            $location = $_POST['location_info'] ?? '';
            $notes = $_POST['shift_notes'] ?? '';
            
            // الحصول على معلومات الجهاز والموقع
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $device_info = json_encode([
                'user_agent' => $user_agent,
                'ip' => $ip_address,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
            // بدء الشيفت
            $stmt = $pdo->prepare("
                INSERT INTO employee_shifts (
                    employee_id, client_id, planned_start_time, planned_end_time,
                    location_info, ip_address, device_info, shift_status, shift_notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?)
            ");
            
            $stmt->execute([
                $employee_id, $client_id, $planned_start, $planned_end,
                $location, $ip_address, $device_info, $notes
            ]);
            
            $shift_id = $pdo->lastInsertId();
            
            // تحديث حالة الموظف
            $stmt = $pdo->prepare("
                UPDATE employees 
                SET current_shift_id = ?, shift_status = 'on_duty', last_activity_time = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$shift_id, $employee_id]);
            
            // تسجيل نشاط بدء الشيفت
            $stmt = $pdo->prepare("
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description, ip_address,
                    activity_data
                ) VALUES (?, ?, ?, 'login', 'بدء الشيفت', ?, ?, ?)
            ");
            
            $activity_data = json_encode([
                'planned_start' => $planned_start,
                'planned_end' => $planned_end,
                'location' => $location,
                'notes' => $notes
            ]);
            
            $stmt->execute([
                $shift_id, $employee_id, $client_id,
                'تم بدء شيفت جديد بواسطة ' . $employee_name,
                $ip_address, $activity_data
            ]);
            
            // إرسال إشعار
            $stmt = $pdo->prepare("
                INSERT INTO shift_notifications (
                    client_id, employee_id, shift_id, notification_type,
                    title, message, priority
                ) VALUES (?, ?, ?, 'shift_start', ?, ?, 'medium')
            ");
            
            $stmt->execute([
                $client_id, $employee_id, $shift_id,
                'بدء شيفت جديد',
                "بدأ الموظف {$employee_name} شيفت جديد في " . date('H:i')
            ]);
            
            $message = 'تم بدء الشيفت بنجاح!';
            
            // إعادة جلب بيانات الشيفت النشط
            $stmt = $pdo->prepare("
                SELECT shift_id, shift_start_time, shift_status, location_info
                FROM employee_shifts 
                WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
                ORDER BY shift_start_time DESC 
                LIMIT 1
            ");
            $stmt->execute([$employee_id]);
            $active_shift = $stmt->fetch();
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء بدء الشيفت: ' . $e->getMessage();
        }
    }
}

// معالجة طلب إنهاء الشيفت
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['end_shift'])) {
    if (!$active_shift) {
        $error = 'لا يوجد شيفت نشط لإنهاؤه.';
    } else {
        try {
            $end_notes = $_POST['end_notes'] ?? '';
            
            // حساب مدة الشيفت
            $duration = strtotime('now') - strtotime($active_shift['shift_start_time']);
            $duration_minutes = floor($duration / 60);
            
            // إنهاء الشيفت
            $stmt = $pdo->prepare("
                UPDATE employee_shifts 
                SET shift_end_time = NOW(), actual_duration_minutes = ?, 
                    shift_status = 'completed', shift_notes = CONCAT(COALESCE(shift_notes, ''), '\n', ?)
                WHERE shift_id = ?
            ");
            $stmt->execute([$duration_minutes, $end_notes, $active_shift['shift_id']]);
            
            // تحديث حالة الموظف
            $stmt = $pdo->prepare("
                UPDATE employees 
                SET current_shift_id = NULL, shift_status = 'off_duty', last_activity_time = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$employee_id]);
            
            // تسجيل نشاط إنهاء الشيفت
            $stmt = $pdo->prepare("
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description, activity_data
                ) VALUES (?, ?, ?, 'logout', 'إنهاء الشيفت', ?, ?)
            ");
            
            $activity_data = json_encode([
                'duration_minutes' => $duration_minutes,
                'end_notes' => $end_notes
            ]);
            
            $stmt->execute([
                $active_shift['shift_id'], $employee_id, $client_id,
                "تم إنهاء الشيفت بعد {$duration_minutes} دقيقة",
                $activity_data
            ]);
            
            // إرسال إشعار
            $stmt = $pdo->prepare("
                INSERT INTO shift_notifications (
                    client_id, employee_id, shift_id, notification_type,
                    title, message, priority
                ) VALUES (?, ?, ?, 'shift_end', ?, ?, 'medium')
            ");
            
            $stmt->execute([
                $client_id, $employee_id, $active_shift['shift_id'],
                'انتهاء الشيفت',
                "انتهى شيفت الموظف {$employee_name} بعد {$duration_minutes} دقيقة"
            ]);
            
            $message = 'تم إنهاء الشيفت بنجاح!';
            $active_shift = null;
            
        } catch (PDOException $e) {
            $error = 'حدث خطأ أثناء إنهاء الشيفت: ' . $e->getMessage();
        }
    }
}

// جلب إحصائيات الشيفت الحالي
$shift_stats = null;
if ($active_shift) {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_activities,
            COUNT(CASE WHEN activity_type = 'session_start' THEN 1 END) as sessions_started,
            COUNT(CASE WHEN activity_type = 'customer_add' THEN 1 END) as customers_added,
            MAX(activity_timestamp) as last_activity
        FROM employee_shift_activities 
        WHERE shift_id = ?
    ");
    $stmt->execute([$active_shift['shift_id']]);
    $shift_stats = $stmt->fetch();
    
    // حساب مدة الشيفت الحالية
    $current_duration = strtotime('now') - strtotime($active_shift['shift_start_time']);
    $shift_stats['current_duration_minutes'] = floor($current_duration / 60);
    $shift_stats['current_duration_hours'] = floor($current_duration / 3600);
    $shift_stats['current_duration_remaining_minutes'] = floor(($current_duration % 3600) / 60);
}

include 'includes/header_sidebar_only.php';
?>


        
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-clock text-primary"></i>
                    إدارة الشيفت
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- معلومات الموظف -->
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user"></i>
                                معلومات الموظف
                            </h5>
                        </div>
                        <div class="card-body">
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($employee_name); ?></p>
                            <p><strong>الدور:</strong> <?php echo htmlspecialchars($employee_role); ?></p>
                            <p><strong>الوقت الحالي:</strong> <span id="current-time"><?php echo date('H:i:s'); ?></span></p>
                            <p><strong>التاريخ:</strong> <?php echo date('Y-m-d'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- حالة الشيفت -->
                <div class="col-md-8 mb-4">
                    <?php if ($active_shift): ?>
                        <!-- الشيفت النشط -->
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-play-circle"></i>
                                    الشيفت النشط
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>بدء الشيفت:</strong> <?php echo date('H:i', strtotime($active_shift['shift_start_time'])); ?></p>
                                        <p><strong>المدة الحالية:</strong> 
                                            <span class="badge bg-info">
                                                <?php echo $shift_stats['current_duration_hours']; ?> ساعة و 
                                                <?php echo $shift_stats['current_duration_remaining_minutes']; ?> دقيقة
                                            </span>
                                        </p>
                                        <p><strong>الموقع:</strong> <?php echo htmlspecialchars($active_shift['location_info'] ?: 'غير محدد'); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>إجمالي الأنشطة:</strong> <span class="badge bg-primary"><?php echo $shift_stats['total_activities']; ?></span></p>
                                        <p><strong>الجلسات المبدوءة:</strong> <span class="badge bg-warning"><?php echo $shift_stats['sessions_started']; ?></span></p>
                                        <p><strong>العملاء المضافون:</strong> <span class="badge bg-info"><?php echo $shift_stats['customers_added']; ?></span></p>
                                    </div>
                                </div>
                                
                                <!-- إنهاء الشيفت -->
                                <hr>
                                <form method="POST" onsubmit="return confirm('هل أنت متأكد من إنهاء الشيفت؟')">
                                    <div class="mb-3">
                                        <label for="end_notes" class="form-label">ملاحظات إنهاء الشيفت (اختياري)</label>
                                        <textarea class="form-control" id="end_notes" name="end_notes" rows="3" placeholder="أضف أي ملاحظات حول الشيفت..."></textarea>
                                    </div>
                                    <button type="submit" name="end_shift" class="btn btn-danger">
                                        <i class="fas fa-stop-circle"></i>
                                        إنهاء الشيفت
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- بدء شيفت جديد -->
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-play"></i>
                                    بدء شيفت جديد
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="planned_start_time" class="form-label">وقت البدء المخطط</label>
                                                <input type="time" class="form-control" id="planned_start_time" name="planned_start_time" value="<?php echo date('H:i'); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="planned_end_time" class="form-label">وقت الانتهاء المخطط</label>
                                                <input type="time" class="form-control" id="planned_end_time" name="planned_end_time" value="<?php echo date('H:i', strtotime('+8 hours')); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="location_info" class="form-label">الموقع/المحطة</label>
                                        <input type="text" class="form-control" id="location_info" name="location_info" placeholder="مثال: المحطة الرئيسية، الطابق الأول">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="shift_notes" class="form-label">ملاحظات الشيفت (اختياري)</label>
                                        <textarea class="form-control" id="shift_notes" name="shift_notes" rows="3" placeholder="أضف أي ملاحظات حول الشيفت..."></textarea>
                                    </div>
                                    
                                    <button type="submit" name="start_shift" class="btn btn-success btn-lg">
                                        <i class="fas fa-play-circle"></i>
                                        بدء الشيفت
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// تحديث الوقت كل ثانية
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

setInterval(updateTime, 1000);

// تحديث الصفحة كل 5 دقائق للحصول على أحدث البيانات
setInterval(function() {
    if (<?php echo $active_shift ? 'true' : 'false'; ?>) {
        location.reload();
    }
}, 300000); // 5 دقائق
</script>

<?php include 'includes/footer_sidebar_only.php'; ?>

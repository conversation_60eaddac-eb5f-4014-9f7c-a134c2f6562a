<?php
/**
 * تحديث جدول الموظفين لدعم نظام الشيفت الجديد
 */

require_once 'config/database.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <title>تحديث جدول الموظفين</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h1 class='text-center text-primary mb-4'>
        <i class='fas fa-users-cog'></i>
        تحديث جدول الموظفين
    </h1>";

try {
    // فحص الأعمدة الموجودة
    echo "<div class='card mb-3'>
        <div class='card-header bg-info text-white'>
            <h5>فحص الأعمدة الموجودة</h5>
        </div>
        <div class='card-body'>";
    
    $columns = $pdo->query("DESCRIBE employees")->fetchAll();
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
    }
    
    echo "<div class='alert alert-info'>";
    echo "<strong>الأعمدة الموجودة:</strong><br>";
    echo implode(', ', $existing_columns);
    echo "</div>";
    
    echo "</div></div>";
    
    // إضافة الأعمدة المطلوبة
    echo "<div class='card mb-3'>
        <div class='card-header bg-primary text-white'>
            <h5>إضافة الأعمدة المطلوبة</h5>
        </div>
        <div class='card-body'>";
    
    $required_columns = [
        'current_shift_id' => 'INT NULL',
        'shift_status' => "ENUM('off_duty', 'on_duty', 'on_break') DEFAULT 'off_duty'",
        'last_activity_time' => 'TIMESTAMP NULL'
    ];
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $pdo->exec("ALTER TABLE employees ADD COLUMN $column_name $column_definition");
                echo "<div class='alert alert-success'><i class='fas fa-plus'></i> تم إضافة عمود $column_name</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في إضافة عمود $column_name: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='alert alert-info'><i class='fas fa-check'></i> عمود $column_name موجود مسبقاً</div>";
        }
    }
    
    echo "</div></div>";
    
    // إضافة فهارس
    echo "<div class='card mb-3'>
        <div class='card-header bg-success text-white'>
            <h5>إضافة فهارس للأداء</h5>
        </div>
        <div class='card-body'>";
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_shift_status ON employees(shift_status, current_shift_id)");
        echo "<div class='alert alert-success'><i class='fas fa-index'></i> تم إنشاء فهرس idx_employee_shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> فهرس idx_employee_shift_status موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_last_activity ON employees(last_activity_time)");
        echo "<div class='alert alert-success'><i class='fas fa-index'></i> تم إنشاء فهرس idx_employee_last_activity</div>";
    } catch (Exception $e) {
        echo "<div class='alert alert-info'><i class='fas fa-info'></i> فهرس idx_employee_last_activity موجود مسبقاً</div>";
    }
    
    echo "</div></div>";
    
    // اختبار التحديث
    echo "<div class='card mb-3'>
        <div class='card-header bg-warning text-white'>
            <h5>اختبار التحديث</h5>
        </div>
        <div class='card-body'>";
    
    // اختبار تحديث حالة موظف
    $employee_stmt = $pdo->query("SELECT id FROM employees WHERE is_active = 1 LIMIT 1");
    $employee = $employee_stmt->fetch();
    
    if ($employee) {
        try {
            $stmt = $pdo->prepare("
                UPDATE employees 
                SET shift_status = 'off_duty', last_activity_time = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$employee['id']]);
            
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم اختبار تحديث حالة الموظف بنجاح</div>";
            
            // التحقق من التحديث
            $stmt = $pdo->prepare("SELECT shift_status, last_activity_time FROM employees WHERE id = ?");
            $stmt->execute([$employee['id']]);
            $result = $stmt->fetch();
            
            echo "<div class='alert alert-info'><i class='fas fa-info'></i> حالة الموظف: {$result['shift_status']}, آخر نشاط: {$result['last_activity_time']}</div>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في اختبار التحديث: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> لا يوجد موظفين لاختبار التحديث</div>";
    }
    
    echo "</div></div>";
    
    // إنشاء جدول الإعدادات إذا لم يكن موجوداً
    echo "<div class='card mb-3'>
        <div class='card-header bg-secondary text-white'>
            <h5>إنشاء جدول الإعدادات</h5>
        </div>
        <div class='card-body'>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT NOT NULL,
        setting_description TEXT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_client_setting (client_id, setting_key),
        INDEX idx_client_settings (client_id, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='alert alert-success'><i class='fas fa-cog'></i> تم إنشاء جدول shift_system_settings</div>";
    
    // إدراج الإعدادات الافتراضية
    $settings = [
        ['auto_track_activities', 'true', 'تفعيل تتبع الأنشطة التلقائي'],
        ['require_shift_notes', 'false', 'إجبار كتابة ملاحظات عند انتهاء الشيفت'],
        ['max_shift_duration_hours', '12', 'الحد الأقصى لمدة الشيفت بالساعات'],
        ['break_reminder_minutes', '240', 'تذكير بالاستراحة كل X دقيقة']
    ];
    
    $clients_stmt = $pdo->query("SELECT client_id FROM clients WHERE is_active = 1");
    $clients = $clients_stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $settings_added = 0;
    foreach ($clients as $client_id) {
        foreach ($settings as $setting) {
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO shift_system_settings (client_id, setting_key, setting_value, setting_description) VALUES (?, ?, ?, ?)");
                $result = $stmt->execute([$client_id, $setting[0], $setting[1], $setting[2]]);
                if ($result) $settings_added++;
            } catch (Exception $e) {
                // تجاهل الأخطاء للإعدادات الموجودة
            }
        }
    }
    
    echo "<div class='alert alert-success'><i class='fas fa-plus'></i> تم إدراج $settings_added إعداد افتراضي</div>";
    echo "</div></div>";
    
    // النتيجة النهائية
    echo "<div class='card border-success'>
        <div class='card-header bg-success text-white text-center'>
            <h3><i class='fas fa-check-double'></i> اكتمل التحديث!</h3>
        </div>
        <div class='card-body text-center'>
            <div class='alert alert-success' style='font-size: 1.3em;'>
                <i class='fas fa-rocket fa-2x mb-3'></i><br>
                <strong>نظام الشيفت جاهز للاستخدام بالكامل!</strong>
            </div>
            
            <div class='row'>
                <div class='col-md-4'>
                    <div class='card border-primary'>
                        <div class='card-body text-center'>
                            <i class='fas fa-play-circle fa-2x text-primary mb-2'></i>
                            <h6>للموظفين</h6>
                            <a href='client/employee_shift_start.php' class='btn btn-primary'>بدء الشيفت</a>
                        </div>
                    </div>
                </div>
                <div class='col-md-4'>
                    <div class='card border-success'>
                        <div class='card-body text-center'>
                            <i class='fas fa-chart-line fa-2x text-success mb-2'></i>
                            <h6>للإدارة</h6>
                            <a href='client/employee_shift_reports.php' class='btn btn-success'>التقارير</a>
                        </div>
                    </div>
                </div>
                <div class='col-md-4'>
                    <div class='card border-info'>
                        <div class='card-body text-center'>
                            <i class='fas fa-vial fa-2x text-info mb-2'></i>
                            <h6>للاختبار</h6>
                            <a href='client/test_shift_system.php' class='btn btn-info'>اختبار</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div>
<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>

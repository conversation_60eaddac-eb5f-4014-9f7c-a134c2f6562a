<?php
/**
 * نظام تسجيل العمليات التلقائي للشيفت
 * يتم استدعاؤه تلقائياً لتسجيل جميع أنشطة الموظف
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

class ShiftActivityTracker {
    private $pdo;
    private $employee_id;
    private $client_id;
    private $shift_id;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        
        if (isset($_SESSION['employee_id'])) {
            $this->employee_id = $_SESSION['employee_id'];
            $this->client_id = $_SESSION['client_id'];
            $this->shift_id = $this->getActiveShiftId();
        }
    }
    
    /**
     * الحصول على معرف الشيفت النشط للموظف
     */
    private function getActiveShiftId() {
        if (!$this->employee_id) return null;
        
        try {
            $stmt = $this->pdo->prepare("
                SELECT shift_id 
                FROM employee_shifts 
                WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
                ORDER BY shift_start_time DESC 
                LIMIT 1
            ");
            $stmt->execute([$this->employee_id]);
            $result = $stmt->fetch();
            
            return $result ? $result['shift_id'] : null;
        } catch (PDOException $e) {
            error_log("خطأ في الحصول على الشيفت النشط: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تسجيل نشاط عام
     */
    public function logActivity($activity_type, $activity_title, $activity_description = '', $target_type = null, $target_id = null, $old_values = null, $new_values = null, $additional_data = []) {
        if (!$this->shift_id || !$this->employee_id) {
            return false;
        }
        
        try {
            // إعداد البيانات الإضافية
            $activity_data = array_merge([
                'page' => $_SERVER['REQUEST_URI'] ?? '',
                'method' => $_SERVER['REQUEST_METHOD'] ?? '',
                'timestamp' => date('Y-m-d H:i:s'),
                'session_id' => session_id()
            ], $additional_data);
            
            $stmt = $this->pdo->prepare("
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description, target_type, target_id,
                    old_values, new_values, activity_data,
                    ip_address, user_agent, page_url, http_method
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $this->shift_id,
                $this->employee_id,
                $this->client_id,
                $activity_type,
                $activity_title,
                $activity_description,
                $target_type,
                $target_id,
                $old_values ? json_encode($old_values, JSON_UNESCAPED_UNICODE) : null,
                $new_values ? json_encode($new_values, JSON_UNESCAPED_UNICODE) : null,
                json_encode($activity_data, JSON_UNESCAPED_UNICODE),
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                $_SERVER['REQUEST_URI'] ?? '',
                $_SERVER['REQUEST_METHOD'] ?? ''
            ]);
            
            // تحديث آخر نشاط للموظف
            if ($result) {
                $this->updateLastActivity();
            }
            
            return $result;
            
        } catch (PDOException $e) {
            error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث آخر نشاط للموظف
     */
    private function updateLastActivity() {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE employees 
                SET last_activity_time = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$this->employee_id]);
        } catch (PDOException $e) {
            error_log("خطأ في تحديث آخر نشاط: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل زيارة صفحة
     */
    public function logPageVisit($page_name, $page_title = '') {
        return $this->logActivity(
            'page_visit',
            'زيارة صفحة: ' . ($page_title ?: $page_name),
            'تم الوصول إلى الصفحة',
            'page',
            null,
            null,
            null,
            ['page_name' => $page_name, 'page_title' => $page_title]
        );
    }
    
    /**
     * تسجيل بدء جلسة
     */
    public function logSessionStart($session_id, $session_data = []) {
        return $this->logActivity(
            'session_start',
            'بدء جلسة جديدة',
            'تم بدء جلسة جديدة للعميل',
            'session',
            $session_id,
            null,
            $session_data,
            ['action' => 'create_session']
        );
    }
    
    /**
     * تسجيل إنهاء جلسة
     */
    public function logSessionEnd($session_id, $old_data = [], $new_data = []) {
        return $this->logActivity(
            'session_end',
            'إنهاء جلسة',
            'تم إنهاء الجلسة وحساب التكلفة',
            'session',
            $session_id,
            $old_data,
            $new_data,
            ['action' => 'end_session']
        );
    }
    
    /**
     * تسجيل تحديث جلسة
     */
    public function logSessionUpdate($session_id, $old_data = [], $new_data = [], $description = '') {
        return $this->logActivity(
            'session_update',
            'تحديث جلسة',
            $description ?: 'تم تحديث بيانات الجلسة',
            'session',
            $session_id,
            $old_data,
            $new_data,
            ['action' => 'update_session']
        );
    }
    
    /**
     * تسجيل إضافة عميل
     */
    public function logCustomerAdd($customer_id, $customer_data = []) {
        return $this->logActivity(
            'customer_add',
            'إضافة عميل جديد',
            'تم إضافة عميل جديد إلى النظام',
            'customer',
            $customer_id,
            null,
            $customer_data,
            ['action' => 'create_customer']
        );
    }
    
    /**
     * تسجيل تعديل عميل
     */
    public function logCustomerEdit($customer_id, $old_data = [], $new_data = []) {
        return $this->logActivity(
            'customer_edit',
            'تعديل بيانات عميل',
            'تم تعديل بيانات العميل',
            'customer',
            $customer_id,
            $old_data,
            $new_data,
            ['action' => 'update_customer']
        );
    }
    
    /**
     * تسجيل حذف عميل
     */
    public function logCustomerDelete($customer_id, $customer_data = []) {
        return $this->logActivity(
            'customer_delete',
            'حذف عميل',
            'تم حذف العميل من النظام',
            'customer',
            $customer_id,
            $customer_data,
            null,
            ['action' => 'delete_customer']
        );
    }
    
    /**
     * تسجيل طلب كافيتيريا
     */
    public function logCafeteriaOrder($order_id, $order_data = []) {
        return $this->logActivity(
            'cafeteria_order',
            'طلب كافيتيريا',
            'تم إنشاء طلب جديد من الكافيتيريا',
            'order',
            $order_id,
            null,
            $order_data,
            ['action' => 'create_order']
        );
    }
    
    /**
     * تسجيل دفع كافيتيريا
     */
    public function logCafeteriaPayment($order_id, $payment_data = []) {
        return $this->logActivity(
            'cafeteria_payment',
            'دفع طلب كافيتيريا',
            'تم دفع طلب الكافيتيريا',
            'order',
            $order_id,
            null,
            $payment_data,
            ['action' => 'pay_order']
        );
    }
    
    /**
     * تسجيل تحديث جهاز
     */
    public function logDeviceUpdate($device_id, $old_data = [], $new_data = []) {
        return $this->logActivity(
            'device_update',
            'تحديث جهاز',
            'تم تحديث بيانات الجهاز',
            'device',
            $device_id,
            $old_data,
            $new_data,
            ['action' => 'update_device']
        );
    }
    
    /**
     * تسجيل معاملة مالية
     */
    public function logFinancialTransaction($transaction_type, $amount, $description = '', $reference_id = null) {
        return $this->logActivity(
            'financial_transaction',
            'معاملة مالية: ' . $transaction_type,
            $description,
            'transaction',
            $reference_id,
            null,
            ['type' => $transaction_type, 'amount' => $amount],
            ['action' => 'financial_transaction', 'amount' => $amount]
        );
    }
    
    /**
     * تسجيل عرض تقرير
     */
    public function logReportView($report_type, $report_params = []) {
        return $this->logActivity(
            'report_view',
            'عرض تقرير: ' . $report_type,
            'تم عرض التقرير',
            'report',
            null,
            null,
            $report_params,
            ['action' => 'view_report', 'report_type' => $report_type]
        );
    }
    
    /**
     * تسجيل تغيير إعدادات
     */
    public function logSettingsChange($setting_key, $old_value, $new_value, $description = '') {
        return $this->logActivity(
            'settings_change',
            'تغيير إعداد: ' . $setting_key,
            $description ?: 'تم تغيير إعداد النظام',
            'setting',
            null,
            ['key' => $setting_key, 'value' => $old_value],
            ['key' => $setting_key, 'value' => $new_value],
            ['action' => 'change_setting']
        );
    }
    
    /**
     * تسجيل بدء استراحة
     */
    public function logBreakStart($break_reason = '') {
        return $this->logActivity(
            'break_start',
            'بدء استراحة',
            $break_reason ?: 'بدء فترة استراحة',
            null,
            null,
            null,
            ['reason' => $break_reason],
            ['action' => 'start_break']
        );
    }
    
    /**
     * تسجيل انتهاء استراحة
     */
    public function logBreakEnd($break_duration_minutes = 0) {
        return $this->logActivity(
            'break_end',
            'انتهاء استراحة',
            'انتهاء فترة الاستراحة والعودة للعمل',
            null,
            null,
            null,
            ['duration_minutes' => $break_duration_minutes],
            ['action' => 'end_break']
        );
    }
    
    /**
     * تسجيل نشاط مخصص
     */
    public function logCustomActivity($title, $description = '', $data = []) {
        return $this->logActivity(
            'other',
            $title,
            $description,
            null,
            null,
            null,
            $data,
            ['action' => 'custom_activity']
        );
    }
    
    /**
     * الحصول على إحصائيات الشيفت الحالي
     */
    public function getCurrentShiftStats() {
        if (!$this->shift_id) return null;
        
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_activities,
                    COUNT(CASE WHEN activity_type = 'session_start' THEN 1 END) as sessions_started,
                    COUNT(CASE WHEN activity_type = 'session_end' THEN 1 END) as sessions_ended,
                    COUNT(CASE WHEN activity_type = 'customer_add' THEN 1 END) as customers_added,
                    COUNT(CASE WHEN activity_type = 'cafeteria_order' THEN 1 END) as orders_created,
                    COUNT(CASE WHEN activity_type = 'page_visit' THEN 1 END) as pages_visited,
                    MAX(activity_timestamp) as last_activity_time
                FROM employee_shift_activities 
                WHERE shift_id = ?
            ");
            $stmt->execute([$this->shift_id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("خطأ في الحصول على إحصائيات الشيفت: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * التحقق من وجود شيفت نشط
     */
    public function hasActiveShift() {
        return $this->shift_id !== null;
    }
    
    /**
     * الحصول على معرف الشيفت النشط
     */
    public function getShiftId() {
        return $this->shift_id;
    }
}

// إنشاء مثيل عام للاستخدام
if (isset($pdo)) {
    $shift_tracker = new ShiftActivityTracker($pdo);
    
    // تسجيل زيارة الصفحة تلقائياً إذا كان هناك شيفت نشط
    if ($shift_tracker->hasActiveShift() && !defined('SKIP_AUTO_PAGE_TRACKING')) {
        $page_name = basename($_SERVER['PHP_SELF'], '.php');
        $shift_tracker->logPageVisit($page_name);
    }
}

/**
 * دالة مساعدة لتسجيل النشاط من أي مكان
 */
function logShiftActivity($activity_type, $title, $description = '', $target_type = null, $target_id = null, $old_values = null, $new_values = null, $additional_data = []) {
    global $shift_tracker;
    
    if (isset($shift_tracker)) {
        return $shift_tracker->logActivity($activity_type, $title, $description, $target_type, $target_id, $old_values, $new_values, $additional_data);
    }
    
    return false;
}
?>

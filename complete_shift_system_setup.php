<?php
/**
 * إكمال إعداد نظام الشيفت - الجداول المتبقية
 * تاريخ الإنشاء: 2025-08-01
 */

require_once 'config/database.php';

// تعيين الترميز
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html dir='rtl' lang='ar'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إكمال إعداد نظام الشيفت</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #333; text-align: center; }
        h2 { color: #007bff; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 إكمال إعداد نظام الشيفت</h1>";

try {
    // الخطوة 1: إنشاء جدول الإشعارات
    echo "<div class='step'><h2>الخطوة 1: إنشاء جدول الإشعارات</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        client_id INT NOT NULL,
        employee_id INT NULL,
        shift_id INT NULL,
        notification_type ENUM('shift_start', 'shift_end', 'break_reminder', 'overtime_alert', 'system_alert') NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at TIMESTAMP NULL,
        
        FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
        FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (shift_id) REFERENCES employee_shifts(shift_id) ON DELETE SET NULL,
        
        INDEX idx_client_notifications (client_id, is_read, created_at),
        INDEX idx_employee_notifications (employee_id, is_read),
        INDEX idx_notification_type (notification_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_notifications بنجاح</div>";
    echo "</div>";
    
    // الخطوة 2: إنشاء جدول الأخطاء
    echo "<div class='step'><h2>الخطوة 2: إنشاء جدول الأخطاء</h2>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS shift_system_errors (
        error_id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NULL,
        shift_id INT NULL,
        error_type VARCHAR(100) NOT NULL,
        error_message TEXT NOT NULL,
        error_data JSON NULL,
        stack_trace TEXT NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_error_type (error_type),
        INDEX idx_error_date (created_at),
        INDEX idx_employee_errors (employee_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "<div class='success'>✅ تم إنشاء جدول shift_system_errors بنجاح</div>";
    echo "</div>";
    
    // الخطوة 3: إنشاء فهارس إضافية
    echo "<div class='step'><h2>الخطوة 3: إنشاء فهارس إضافية</h2>";
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_shifts_active ON employee_shifts(employee_id, shift_status, shift_start_time)");
        echo "<div class='success'>✅ تم إنشاء فهرس idx_employee_shifts_active</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ فهرس idx_employee_shifts_active موجود مسبقاً</div>";
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_employee_shift_status ON employees(shift_status, current_shift_id)");
        echo "<div class='success'>✅ تم إنشاء فهرس idx_employee_shift_status</div>";
    } catch (Exception $e) {
        echo "<div class='info'>ℹ️ فهرس idx_employee_shift_status موجود مسبقاً</div>";
    }
    
    echo "</div>";
    
    // الخطوة 4: اختبار النظام
    echo "<div class='step'><h2>الخطوة 4: اختبار النظام</h2>";
    
    // اختبار إدراج شيفت تجريبي
    $test_employee_stmt = $pdo->query("SELECT id, client_id FROM employees WHERE is_active = 1 LIMIT 1");
    $test_employee = $test_employee_stmt->fetch();
    
    if ($test_employee) {
        try {
            // اختبار إدراج شيفت
            $stmt = $pdo->prepare("
                INSERT INTO employee_shifts (employee_id, client_id, shift_status, location_info) 
                VALUES (?, ?, 'active', 'اختبار النظام')
            ");
            $stmt->execute([$test_employee['id'], $test_employee['client_id']]);
            $test_shift_id = $pdo->lastInsertId();
            
            echo "<div class='success'>✅ تم إنشاء شيفت تجريبي #$test_shift_id</div>";
            
            // اختبار إدراج نشاط
            $stmt = $pdo->prepare("
                INSERT INTO employee_shift_activities (
                    shift_id, employee_id, client_id, activity_type,
                    activity_title, activity_description
                ) VALUES (?, ?, ?, 'login', 'اختبار النظام', 'نشاط تجريبي لاختبار النظام')
            ");
            $stmt->execute([$test_shift_id, $test_employee['id'], $test_employee['client_id']]);
            
            echo "<div class='success'>✅ تم تسجيل نشاط تجريبي</div>";
            
            // حذف البيانات التجريبية
            $pdo->prepare("DELETE FROM employee_shift_activities WHERE shift_id = ?")->execute([$test_shift_id]);
            $pdo->prepare("DELETE FROM employee_shifts WHERE shift_id = ?")->execute([$test_shift_id]);
            
            echo "<div class='info'>ℹ️ تم حذف البيانات التجريبية</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في اختبار النظام: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ لا يوجد موظفين لاختبار النظام</div>";
    }
    
    echo "</div>";
    
    // الخطوة 5: التحقق من Views
    echo "<div class='step'><h2>الخطوة 5: التحقق من Views</h2>";
    
    try {
        $result = $pdo->query("SELECT * FROM active_employee_shifts LIMIT 1");
        echo "<div class='success'>✅ view active_employee_shifts يعمل بشكل صحيح</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في view active_employee_shifts: " . $e->getMessage() . "</div>";
    }
    
    try {
        $result = $pdo->query("SELECT * FROM shift_quick_stats");
        $stats = $result->fetchAll();
        echo "<div class='success'>✅ view shift_quick_stats يعمل بشكل صحيح</div>";
        
        foreach ($stats as $stat) {
            echo "<div class='info'>📊 {$stat['stat_description']}: {$stat['stat_value']}</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في view shift_quick_stats: " . $e->getMessage() . "</div>";
    }
    
    echo "</div>";
    
    // رسالة النجاح النهائية
    echo "<div class='success' style='text-align: center; font-size: 18px; margin: 20px 0;'>
        🎉 <strong>تم إكمال إعداد نظام الشيفت بنجاح!</strong><br>
        النظام جاهز للاستخدام الآن.
    </div>";
    
    echo "<div class='info'>
        <h3>روابط مهمة:</h3>
        <ul>
            <li><a href='client/employee_shift_start.php' target='_blank'>صفحة إدارة الشيفت للموظفين</a></li>
            <li><a href='client/employee_shift_reports.php' target='_blank'>صفحة تقارير الشيفت للإدارة</a></li>
            <li><a href='client/test_shift_system.php' target='_blank'>صفحة اختبار النظام</a></li>
        </ul>
        
        <h3>ملاحظات مهمة:</h3>
        <ul>
            <li>تأكد من تسجيل دخول الموظفين لاستخدام النظام</li>
            <li>النظام يسجل جميع الأنشطة تلقائياً</li>
            <li>يمكن للإدارة عرض التقارير المفصلة</li>
            <li>النظام يدعم العمل بدون اتصال</li>
        </ul>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
}

echo "</div></body></html>";
?>

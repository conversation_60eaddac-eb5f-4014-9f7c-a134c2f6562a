<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
require_once 'includes/employee-auth.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

$client_id = isset($_SESSION['employee_id']) ? $_SESSION['client_id'] : $_SESSION['client_id'];
$page_title = "اختبار نظام الشيفت";
$active_page = "test_shift";

// جلب إحصائيات سريعة
try {
    // عدد الشيفت النشط
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM employee_shifts WHERE client_id = ? AND shift_status IN ('active', 'on_break')");
    $stmt->execute([$client_id]);
    $active_shifts = $stmt->fetch()['count'];
    
    // عدد الأنشطة اليوم
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM employee_shift_activities esa
        JOIN employee_shifts es ON esa.shift_id = es.shift_id
        WHERE es.client_id = ? AND DATE(esa.activity_timestamp) = CURDATE()
    ");
    $stmt->execute([$client_id]);
    $today_activities = $stmt->fetch()['count'];
    
    // عدد الموظفين في الخدمة
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM employees WHERE client_id = ? AND shift_status = 'on_duty'");
    $stmt->execute([$client_id]);
    $employees_on_duty = $stmt->fetch()['count'];
    
    // الشيفت المكتملة اليوم
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM employee_shifts 
        WHERE client_id = ? AND DATE(shift_start_time) = CURDATE() AND shift_status = 'completed'
    ");
    $stmt->execute([$client_id]);
    $completed_shifts_today = $stmt->fetch()['count'];
    
} catch (PDOException $e) {
    $active_shifts = 0;
    $today_activities = 0;
    $employees_on_duty = 0;
    $completed_shifts_today = 0;
}

include 'includes/header_sidebar_only.php';
?>


        
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-vial text-primary"></i>
                    اختبار نظام الشيفت
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات السريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo $active_shifts; ?></h3>
                            <p class="card-text">شيفت نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $today_activities; ?></h3>
                            <p class="card-text">أنشطة اليوم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo $employees_on_duty; ?></h3>
                            <p class="card-text">موظفين في الخدمة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo $completed_shifts_today; ?></h3>
                            <p class="card-text">شيفت مكتمل اليوم</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبار تتبع الأنشطة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-mouse-pointer"></i>
                                اختبار تتبع الأنشطة
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">اختبر نظام تتبع الأنشطة التلقائي:</p>
                            
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="testActivity('button_click', 'اختبار نقر زر')">
                                    <i class="fas fa-click"></i> اختبار نقر زر
                                </button>
                                
                                <button type="button" class="btn btn-success" onclick="testSessionActivity()">
                                    <i class="fas fa-play"></i> اختبار نشاط جلسة
                                </button>
                                
                                <button type="button" class="btn btn-warning" onclick="testCustomerActivity()">
                                    <i class="fas fa-user"></i> اختبار نشاط عميل
                                </button>
                                
                                <button type="button" class="btn btn-info" onclick="testOrderActivity()">
                                    <i class="fas fa-shopping-cart"></i> اختبار طلب كافيتيريا
                                </button>
                            </div>
                            
                            <hr>
                            
                            <form onsubmit="testFormSubmit(event)">
                                <div class="mb-3">
                                    <label for="test_input" class="form-label">اختبار إرسال نموذج</label>
                                    <input type="text" class="form-control" id="test_input" placeholder="أدخل نص تجريبي">
                                </div>
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-paper-plane"></i> إرسال نموذج تجريبي
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list"></i>
                                سجل الأنشطة المباشر
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="activity-log" style="height: 400px; overflow-y: auto; border: 1px solid #dee2e6; padding: 10px; background: #f8f9fa;">
                                <p class="text-muted text-center">سيتم عرض الأنشطة هنا...</p>
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshActivityLog()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearActivityLog()">
                                    <i class="fas fa-trash"></i> مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i>
                                معلومات النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>معلومات المستخدم:</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>نوع المستخدم:</strong> <?php echo isset($_SESSION['employee_id']) ? 'موظف' : 'مالك'; ?></li>
                                        <?php if (isset($_SESSION['employee_id'])): ?>
                                        <li><strong>اسم الموظف:</strong> <?php echo htmlspecialchars($_SESSION['employee_name']); ?></li>
                                        <li><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['employee_role']); ?></li>
                                        <?php endif; ?>
                                        <li><strong>معرف العميل:</strong> <?php echo $client_id; ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>حالة النظام:</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>تتبع الأنشطة:</strong> <span class="badge bg-success">مفعل</span></li>
                                        <li><strong>JavaScript:</strong> <span id="js-status" class="badge bg-success">مفعل</span></li>
                                        <li><strong>الاتصال:</strong> <span id="connection-status" class="badge bg-success">متصل</span></li>
                                        <li><strong>آخر نشاط:</strong> <span id="last-activity">الآن</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
let activityCount = 0;

function testActivity(type, title) {
    if (window.trackShiftActivity) {
        window.trackShiftActivity(title, 'نشاط تجريبي من صفحة الاختبار', {
            test_type: type,
            test_number: ++activityCount
        });
        
        addToActivityLog('تم إرسال: ' + title);
    } else {
        addToActivityLog('خطأ: نظام التتبع غير متاح');
    }
}

function testSessionActivity() {
    if (window.trackSessionAction) {
        const sessionId = Math.floor(Math.random() * 1000);
        window.trackSessionAction('start', sessionId, {
            test: true,
            device_id: 1
        });
        
        addToActivityLog('تم إرسال: بدء جلسة تجريبية #' + sessionId);
    } else {
        addToActivityLog('خطأ: نظام التتبع غير متاح');
    }
}

function testCustomerActivity() {
    if (window.trackCustomerAction) {
        const customerId = Math.floor(Math.random() * 1000);
        window.trackCustomerAction('add', customerId, {
            test: true,
            name: 'عميل تجريبي'
        });
        
        addToActivityLog('تم إرسال: إضافة عميل تجريبي #' + customerId);
    } else {
        addToActivityLog('خطأ: نظام التتبع غير متاح');
    }
}

function testOrderActivity() {
    if (window.trackOrderAction) {
        const orderId = Math.floor(Math.random() * 1000);
        window.trackOrderAction(orderId, {
            test: true,
            total: 25.50
        });
        
        addToActivityLog('تم إرسال: طلب كافيتيريا تجريبي #' + orderId);
    } else {
        addToActivityLog('خطأ: نظام التتبع غير متاح');
    }
}

function testFormSubmit(event) {
    event.preventDefault();
    
    const input = document.getElementById('test_input');
    addToActivityLog('تم إرسال نموذج تجريبي: ' + input.value);
    
    // مسح الحقل
    input.value = '';
}

function addToActivityLog(message) {
    const log = document.getElementById('activity-log');
    const time = new Date().toLocaleTimeString('ar-SA');
    const entry = document.createElement('div');
    entry.className = 'mb-1 p-2 bg-white border-start border-primary border-3';
    entry.innerHTML = `<small class="text-muted">${time}</small><br>${message}`;
    
    log.insertBefore(entry, log.firstChild);
    
    // الاحتفاظ بآخر 20 إدخال فقط
    while (log.children.length > 20) {
        log.removeChild(log.lastChild);
    }
}

function refreshActivityLog() {
    // يمكن إضافة استدعاء API لجلب آخر الأنشطة
    addToActivityLog('تم تحديث السجل');
}

function clearActivityLog() {
    const log = document.getElementById('activity-log');
    log.innerHTML = '<p class="text-muted text-center">تم مسح السجل</p>';
}

// تحديث حالة الاتصال
function updateConnectionStatus() {
    const status = document.getElementById('connection-status');
    if (navigator.onLine) {
        status.className = 'badge bg-success';
        status.textContent = 'متصل';
    } else {
        status.className = 'badge bg-danger';
        status.textContent = 'غير متصل';
    }
}

// تحديث آخر نشاط
function updateLastActivity() {
    document.getElementById('last-activity').textContent = new Date().toLocaleTimeString('ar-SA');
}

// مراقبة الأحداث
window.addEventListener('online', updateConnectionStatus);
window.addEventListener('offline', updateConnectionStatus);

// تحديث دوري
setInterval(updateLastActivity, 1000);
setInterval(updateConnectionStatus, 5000);

// تهيئة أولية
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionStatus();
    addToActivityLog('تم تحميل صفحة اختبار نظام الشيفت');
});
</script>

<?php include 'includes/footer_sidebar_only.php'; ?>

<?php
/**
 * Header جديد يعتمد على القائمة الجانبية فقط
 * بدون القائمة العلوية
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['client_id']) && !isset($_SESSION['employee_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد نوع المستخدم
$is_employee = isset($_SESSION['employee_id']);
$user_name = $is_employee ? $_SESSION['employee_name'] : $_SESSION['business_name'];
$user_role = $is_employee ? $_SESSION['employee_role'] : 'مالك المحل';
$business_name = $_SESSION['business_name'] ?? 'PlayGood';

// تضمين ملفات الصلاحيات
if ($is_employee) {
    require_once 'employee-auth.php';
} else {
    // دوال الصلاحيات للعملاء
    if (!function_exists('hasPagePermission')) {
        function hasPagePermission($page_name) {
            return true; // العميل له صلاحية الوصول لجميع الصفحات
        }
    }
    
    if (!function_exists('canAccessPage')) {
        function canAccessPage($page_name, $permission = null) {
            return true;
        }
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' . htmlspecialchars($business_name) : htmlspecialchars($business_name); ?></title>
    <link rel="icon" type="image/png" href="../../assets/images/favicon.png">
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="assets/css/custom-theme.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="assets/css/sidebar-enhanced.css?v=<?php echo time(); ?>"
    
    <style>
        /* تصميم القائمة الجانبية المحسن */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.3s ease;
        }
        
        .sidebar-header {
            padding: 20px;
            background: rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        
        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }
        
        .sidebar-brand:hover {
            color: #3498db;
            text-decoration: none;
        }
        
        .user-info {
            margin-top: 15px;
            padding: 15px;
            background: rgba(0,0,0,0.1);
            border-radius: 10px;
            text-align: center;
        }
        
        .user-info .user-name {
            color: white;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .user-info .user-role {
            color: #bdc3c7;
            font-size: 0.9rem;
        }
        
        .sidebar-nav {
            padding: 20px 0;
        }
        
        .nav-section {
            margin-bottom: 30px;
        }
        
        .nav-section-title {
            color: #bdc3c7;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }
        
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border-right-color: #3498db;
        }
        
        .nav-link.active {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border-right-color: #3498db;
        }
        
        .nav-link i {
            width: 20px;
            margin-left: 15px;
            text-align: center;
        }
        
        .nav-link .badge {
            margin-right: auto;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 280px;
            min-height: 100vh;
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        .content-header {
            background: white;
            padding: 20px 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-right: 4px solid #3498db;
        }
        
        .content-header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .content-header .breadcrumb {
            margin: 10px 0 0;
            background: none;
            padding: 0;
        }
        
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 100%;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 20px;
            }
            
            .sidebar-toggle {
                display: block;
            }
            
            .content-header {
                padding: 15px 20px;
            }
        }
        
        /* تحسينات إضافية */
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        /* تأثيرات التمرير */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
        }
        
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <!-- زر التبديل للموبايل -->
    <button class="sidebar-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- القائمة الجانبية -->
    <nav class="sidebar" id="sidebar">
        <!-- ترويسة القائمة -->
        <div class="sidebar-header">
            <a href="dashboard.php" class="sidebar-brand">
                <i class="fas fa-gamepad me-2"></i>
                <?php echo htmlspecialchars($business_name); ?>
            </a>
            
            <div class="user-info">
                <div class="user-name"><?php echo htmlspecialchars($user_name); ?></div>
                <div class="user-role"><?php echo htmlspecialchars($user_role); ?></div>
                <?php if ($is_employee): ?>
                    <div class="mt-2">
                        <?php
                        // عرض حالة الشيفت
                        if (isset($pdo)) {
                            try {
                                $stmt = $pdo->prepare("SELECT shift_status FROM employees WHERE id = ?");
                                $stmt->execute([$_SESSION['employee_id']]);
                                $employee = $stmt->fetch();
                                if ($employee) {
                                    $status_colors = [
                                        'on_duty' => 'success',
                                        'on_break' => 'warning', 
                                        'off_duty' => 'secondary'
                                    ];
                                    $status_labels = [
                                        'on_duty' => 'في الخدمة',
                                        'on_break' => 'في استراحة',
                                        'off_duty' => 'خارج الخدمة'
                                    ];
                                    $status = $employee['shift_status'] ?? 'off_duty';
                                    echo '<span class="badge bg-' . $status_colors[$status] . '">' . $status_labels[$status] . '</span>';
                                }
                            } catch (Exception $e) {
                                // تجاهل الأخطاء
                            }
                        }
                        ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- محتوى القائمة -->
        <div class="sidebar-nav">
            <!-- القسم الرئيسي -->
            <div class="nav-section">
                <div class="nav-section-title">القسم الرئيسي</div>
                
                <a class="nav-link <?php echo ($active_page ?? '') === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </div>

            <!-- إدارة الجلسات -->
            <?php if (hasPagePermission('sessions') || hasPagePermission('devices')): ?>
            <div class="nav-section">
                <div class="nav-section-title">إدارة الجلسات</div>
                
                <?php if (hasPagePermission('sessions')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'sessions' ? 'active' : ''; ?>" href="sessions.php">
                    <i class="fas fa-play-circle"></i>
                    <span>الجلسات</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('devices')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'devices' ? 'active' : ''; ?>" href="devices.php">
                    <i class="fas fa-gamepad"></i>
                    <span>الأجهزة</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('rooms')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'rooms' ? 'active' : ''; ?>" href="rooms.php">
                    <i class="fas fa-home"></i>
                    <span>الغرف</span>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- إدارة العملاء -->
            <?php if (hasPagePermission('customers')): ?>
            <div class="nav-section">
                <div class="nav-section-title">إدارة العملاء</div>
                
                <a class="nav-link <?php echo ($active_page ?? '') === 'customers' ? 'active' : ''; ?>" href="customers.php">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </a>
            </div>
            <?php endif; ?>

            <!-- الكافتيريا والطلبات -->
            <?php if (hasPagePermission('cafeteria') || hasPagePermission('orders')): ?>
            <div class="nav-section">
                <div class="nav-section-title">الكافتيريا والطلبات</div>
                
                <?php if (hasPagePermission('cafeteria')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'cafeteria' ? 'active' : ''; ?>" href="cafeteria.php">
                    <i class="fas fa-coffee"></i>
                    <span>الكافتيريا</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('orders')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'orders' ? 'active' : ''; ?>" href="orders.php">
                    <i class="fas fa-shopping-cart"></i>
                    <span>الطلبات</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('inventory')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'inventory' ? 'active' : ''; ?>" href="inventory.php">
                    <i class="fas fa-warehouse"></i>
                    <span>المخزون</span>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- إدارة الموظفين -->
            <?php if (hasPagePermission('employees') || $is_employee): ?>
            <div class="nav-section">
                <div class="nav-section-title">إدارة الموظفين</div>
                
                <?php if (hasPagePermission('employees')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'employees' ? 'active' : ''; ?>" href="employees.php">
                    <i class="fas fa-user-tie"></i>
                    <span>الموظفين</span>
                </a>
                <?php endif; ?>
                
                <!-- نظام الشيفت الجديد -->
                <?php if ($is_employee): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_start' ? 'active' : ''; ?>" href="employee_shift_start.php">
                    <i class="fas fa-play-circle text-success"></i>
                    <span>إدارة الشيفت</span>
                    <?php
                    // عرض حالة الشيفت النشط
                    if (isset($pdo)) {
                        try {
                            $stmt = $pdo->prepare("SELECT shift_status FROM employees WHERE id = ?");
                            $stmt->execute([$_SESSION['employee_id']]);
                            $employee = $stmt->fetch();
                            if ($employee && $employee['shift_status'] == 'on_duty') {
                                echo '<span class="badge bg-success">نشط</span>';
                            }
                        } catch (Exception $e) {
                            // تجاهل الأخطاء
                        }
                    }
                    ?>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('employees') || ($is_employee && ($_SESSION['employee_role'] ?? '') === 'admin')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'shift_reports' ? 'active' : ''; ?>" href="employee_shift_reports.php">
                    <i class="fas fa-chart-line"></i>
                    <span>تقارير الشيفت</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('attendance')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'attendance' ? 'active' : ''; ?>" href="attendance.php">
                    <i class="fas fa-user-check"></i>
                    <span>الحضور والانصراف</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('shifts')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'shifts' ? 'active' : ''; ?>" href="shifts.php">
                    <i class="fas fa-clock"></i>
                    <span>الورديات</span>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- المالية والتقارير -->
            <?php if (hasPagePermission('finances') || hasPagePermission('reports')): ?>
            <div class="nav-section">
                <div class="nav-section-title">المالية والتقارير</div>
                
                <?php if (hasPagePermission('invoices')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'invoices' ? 'active' : ''; ?>" href="invoices.php">
                    <i class="fas fa-file-invoice"></i>
                    <span>الفواتير</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('finances')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'finances' ? 'active' : ''; ?>" href="finances.php">
                    <i class="fas fa-chart-line"></i>
                    <span>المالية</span>
                </a>
                <?php endif; ?>
                
                <?php if (hasPagePermission('reports')): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'reports' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- الإعدادات -->
            <div class="nav-section">
                <div class="nav-section-title">الإعدادات</div>
                
                <a class="nav-link <?php echo ($active_page ?? '') === 'notifications' ? 'active' : ''; ?>" href="notifications.php">
                    <i class="fas fa-bell"></i>
                    <span>الإشعارات</span>
                    <span class="badge bg-danger notification-badge" id="notification-count" style="display: none;"></span>
                </a>
                
                <a class="nav-link <?php echo ($active_page ?? '') === 'profile' ? 'active' : ''; ?>" href="profile.php">
                    <i class="fas fa-user"></i>
                    <span>الملف الشخصي</span>
                </a>
                
                <?php if (!$is_employee): ?>
                <a class="nav-link <?php echo ($active_page ?? '') === 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>إعدادات المحل</span>
                </a>
                <?php endif; ?>
                
                <a class="nav-link text-danger" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- ترويسة المحتوى -->
        <div class="content-header">
            <h1>
                <?php if (isset($page_icon)): ?>
                    <i class="<?php echo $page_icon; ?> me-2"></i>
                <?php endif; ?>
                <?php echo isset($page_title) ? htmlspecialchars($page_title) : 'الصفحة الرئيسية'; ?>
            </h1>
            
            <?php if (isset($breadcrumb) && is_array($breadcrumb)): ?>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumb as $item): ?>
                        <?php if (isset($item['url'])): ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo $item['url']; ?>"><?php echo htmlspecialchars($item['title']); ?></a>
                            </li>
                        <?php else: ?>
                            <li class="breadcrumb-item active"><?php echo htmlspecialchars($item['title']); ?></li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
            <?php endif; ?>
        </div>

        <!-- محتوى الصفحة -->
        <div class="page-content">

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/sidebar-enhanced.js?v=<?php echo time(); ?>"></script>
    <script>
        // تبديل القائمة الجانبية في الموبايل
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // إغلاق القائمة عند النقر خارجها في الموبايل
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !toggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // تحديث حجم النافذة
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>

    <?php
    // تضمين نظام تتبع الأنشطة للموظفين
    if ($is_employee) {
        if (!defined('SKIP_AUTO_PAGE_TRACKING')) {
            require_once __DIR__ . '/shift_activity_tracker.php';
        }
        
        // التحقق من وجود شيفت نشط
        $has_active_shift = false;
        try {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM employee_shifts 
                WHERE employee_id = ? AND shift_status IN ('active', 'on_break')
            ");
            $stmt->execute([$_SESSION['employee_id']]);
            $result = $stmt->fetch();
            $has_active_shift = $result['count'] > 0;
        } catch (Exception $e) {
            // تجاهل الأخطاء
        }
        
        // إضافة بيانات للـ JavaScript
        echo '<script>
            document.body.dataset.isEmployee = "true";
            document.body.dataset.hasActiveShift = "' . ($has_active_shift ? 'true' : 'false') . '";
        </script>';
        
        // تحميل JavaScript للتتبع
        if ($has_active_shift) {
            echo '<script src="assets/js/shift_tracker.js"></script>';
        }
        
        // إضافة JavaScript لتحديث حالة الشيفت
        echo '<script>
            // تحديث حالة الشيفت كل دقيقة
            setInterval(function() {
                fetch("api/shift_status_check.php")
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.employee) {
                            const shiftBadge = document.querySelector(".nav-link[href=\'employee_shift_start.php\'] .badge");
                            if (shiftBadge) {
                                if (data.employee.has_active_shift) {
                                    shiftBadge.className = "badge bg-success";
                                    shiftBadge.textContent = "نشط";
                                } else {
                                    shiftBadge.className = "badge bg-secondary";
                                    shiftBadge.textContent = "متوقف";
                                }
                            }
                        }
                    })
                    .catch(error => console.warn("خطأ في تحديث حالة الشيفت:", error));
            }, 60000); // كل دقيقة
        </script>';
    }
    
    // نظام الإشعارات
    echo '<script src="assets/js/notifications.js"></script>';
    ?>

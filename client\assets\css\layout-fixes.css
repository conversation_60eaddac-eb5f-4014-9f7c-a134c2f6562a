
/* إصلاحات التخطيط للقائمة الجانبية */
.main-content .container,
.main-content .container-fluid {
    max-width: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.main-content .row {
    margin: 0 !important;
}

.main-content .col-md-3,
.main-content .col-lg-2 {
    display: none !important; /* إخفاء أعمدة القائمة الجانبية القديمة */
}

/* تحسين المحتوى */
.page-content {
    width: 100%;
    max-width: none;
}

/* إصلاح الجداول */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* إصلاح النماذج */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* تحسين التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

/* تحسين البطاقات */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    font-weight: 600;
}

/* تحسين شريط التقدم */
.progress {
    border-radius: 10px;
    height: 8px;
    background: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* تحسين الشارات */
.badge {
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* تأثيرات الحركة */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.page-content > .row,
.page-content > .card {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .sidebar-toggle {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
        padding: 0 !important;
    }
    
    .content-header {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-after: avoid;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        page-break-inside: avoid;
    }
}
